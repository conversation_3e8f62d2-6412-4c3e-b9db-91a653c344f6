<?php
/**
 * Create Cache Directory Script
 * Creates the cache directory and sets proper permissions for IP geolocation caching
 */

// Set the path to the application root
define('APPROOT', dirname(dirname(__FILE__)));

echo "Creating cache directory for voting security system...\n";

try {
    $cacheDir = APPROOT . '/cache';
    
    // Create cache directory if it doesn't exist
    if (!is_dir($cacheDir)) {
        if (mkdir($cacheDir, 0755, true)) {
            echo "✓ Created cache directory: {$cacheDir}\n";
        } else {
            throw new Exception("Failed to create cache directory: {$cacheDir}");
        }
    } else {
        echo "✓ Cache directory already exists: {$cacheDir}\n";
    }
    
    // Create index.php file to prevent directory browsing
    $indexFile = $cacheDir . '/index.php';
    if (!file_exists($indexFile)) {
        $indexContent = "<?php\n// Prevent directory browsing\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n?>";
        if (file_put_contents($indexFile, $indexContent)) {
            echo "✓ Created index.php protection file\n";
        } else {
            echo "⚠ Warning: Could not create index.php protection file\n";
        }
    }
    
    // Create .htaccess file to deny web access
    $htaccessFile = $cacheDir . '/.htaccess';
    if (!file_exists($htaccessFile)) {
        $htaccessContent = "# Deny all web access to cache directory\nDeny from all\n";
        if (file_put_contents($htaccessFile, $htaccessContent)) {
            echo "✓ Created .htaccess protection file\n";
        } else {
            echo "⚠ Warning: Could not create .htaccess protection file\n";
        }
    }
    
    // Test write permissions
    $testFile = $cacheDir . '/test_write.tmp';
    if (file_put_contents($testFile, 'test')) {
        unlink($testFile);
        echo "✓ Cache directory is writable\n";
    } else {
        throw new Exception("Cache directory is not writable. Please check permissions.");
    }
    
    echo "\n✅ Cache directory setup completed successfully!\n";
    echo "The IP geolocation service can now cache data in: {$cacheDir}\n";
    
} catch (Exception $e) {
    echo "\n❌ Error: " . $e->getMessage() . "\n";
    echo "Please manually create the cache directory and set proper permissions.\n";
    exit(1);
}

exit(0);
?>

<?php
/**
 * Coordinator Controller - CLEANED AND REORGANIZED
 *
 * This controller handles all coordinator-related functionality.
 *
 * SECTIONS:
 * 1. Properties and Dependencies
 * 2. Constructor and Initialization
 * 3. Navigation and Routing
 * 4. Dashboard and Reports
 * 5. Show Management
 * 6. Category Management
 * 7. Judging Metrics Management
 * 8. Age Weights Management
 * 9. Registration Management
 * 10. Staff Management
 * 11. QR Code Management
 * 12. Results and Analytics
 * 13. Messaging and Communication
 *
 * Version 3.64.3 - CLEANUP AND REORGANIZATION
 * - Added comprehensive section headers for better organization
 * - Fixed undefined properties and missing dependencies
 * - Removed alias methods and dead code
 * - Cleaned up unused variables
 * - Improved documentation and structure
 * - Backup created in autobackup/ before cleanup
 *
 * @version 3.64.3
 * <AUTHOR> Assistant
 * @backup Created in autobackup/ before cleanup
 */
class CoordinatorController extends Controller {

    // ============================================================================
    // SECTION 1: PROPERTIES AND DEPENDENCIES
    // ============================================================================

    private $showModel;
    private $userModel;
    private $vehicleModel;
    private $registrationModel;
    private $judgingModel;
    private $defaultCategoryModel;
    private $defaultMetricModel;
    private $defaultAgeWeightModel;
    private $settingsModel;
    private $formDesignerModel;
    private $entityTemplateManager;
    private $auth;
    private $db;

    // ============================================================================
    // SECTION 2: CONSTRUCTOR AND INITIALIZATION
    // ============================================================================

    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in and is a coordinator
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole(['coordinator', 'admin'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $this->showModel = $this->model('ShowModel');
        $this->userModel = $this->model('UserModel');
        $this->vehicleModel = $this->model('VehicleModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->judgingModel = $this->model('JudgingModel');
        $this->defaultCategoryModel = $this->model('DefaultCategoryModel');
        $this->defaultMetricModel = $this->model('DefaultMetricModel');
        $this->defaultAgeWeightModel = $this->model('DefaultAgeWeightModel');
        $this->settingsModel = $this->model('SettingsModel');
        $this->formDesignerModel = $this->model('FormDesignerModel');
        $this->entityTemplateManager = $this->model('EntityTemplateManager');
        $this->db = new Database();
    }

    // ============================================================================
    // SECTION 3: NAVIGATION AND ROUTING
    // ============================================================================

    /**
     * Default index method - redirects to dashboard
     */
    public function index() {
        $this->redirect('coordinator/dashboard');
    }
    
    /**
     * Create a new show - redirects to the admin's addShow method
     */
    public function createShow() {
        // Redirect to the admin's addShow method which now handles both admin and coordinator roles
        $this->redirect('admin/addShow');
        
        // Get the listing fee from settings
        $listingFee = $this->settingsModel->getSetting('default_listing_fee', 0);
        $listingFeeType = $this->settingsModel->getSetting('listing_fee_type', 'per_show');
        
        // Get current user ID
        $userId = $this->auth->getCurrentUserId();
        
        // First check if there's a default template set in DefaultTemplateManager
        $templateId = $this->entityTemplateManager->getBestTemplateId('show', 0);

        // If a template was found, use it
        if ($templateId) {
            $template = $this->formDesignerModel->getFormTemplateById($templateId);
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CoordinatorController::createShow - Using template ID {$templateId} from DefaultTemplateManager");
            }
        } else {
            // Fall back to the default admin form template
            $template = $this->formDesignerModel->getFormTemplateByTypeAndEntity('admin_show', 0);
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CoordinatorController::createShow - Using default admin_show template");
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data - start with standard fields
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'location' => trim($_POST['location']),
                'start_date' => convertUserDateTimeToUTC(trim($_POST['start_date']), $userId),
                'end_date' => convertUserDateTimeToUTC(trim($_POST['end_date']), $userId),
                'registration_start' => convertUserDateTimeToUTC(trim($_POST['registration_start']), $userId),
                'registration_end' => convertUserDateTimeToUTC(trim($_POST['registration_end']), $userId),
                'status' => trim($_POST['status']),
                'fan_voting_enabled' => isset($_POST['fan_voting_enabled']) ? (bool)$_POST['fan_voting_enabled'] : true,
                'is_free' => isset($_POST['is_free']) ? (bool)$_POST['is_free'] : false,
                // If is_free is checked, always set registration_fee to 0
                'registration_fee' => (isset($_POST['is_free']) && $_POST['is_free']) ? 0.00 : (isset($_POST['registration_fee']) ? $_POST['registration_fee'] : 0.00),
                'listing_fee' => $listingFee,
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_start_err' => '',
                'registration_end_err' => '',
                'status_err' => '',
                'title' => 'Create Show',
                'template' => $template,
                'listing_fee' => $listingFee,
                'listing_fee_type' => $listingFeeType,
                'is_admin' => false
            ];
            
            // Always assign to current coordinator
            $data['coordinator_id'] = $userId;
            $data['listing_paid'] = false; // Not paid yet for coordinators
            
            // Add template fields to data
            if ($template && isset($template->fields)) {
                $fields = json_decode($template->fields);
                if ($fields) {
                    foreach ($fields as $field) {
                        // Skip if field doesn't have an ID
                        if (!isset($field->id)) {
                            continue;
                        }
                        
                        // Skip standard fields that are already handled
                        if (in_array($field->id, [
                            'name', 'description', 'location', 
                            'start_date', 'end_date', 'registration_start', 'registration_end', 
                            'coordinator_id', 'status', 'fan_voting_enabled', 'registration_fee', 
                            'is_free', 'listing_fee', 'listing_paid', 'featured_image_id'
                        ])) {
                            continue;
                        }
                        
                        // Skip section dividers and HTML fields (they don't need database columns)
                        if (isset($field->type) && in_array($field->type, ['section', 'html'])) {
                            continue;
                        }
                        
                        // Add the field to the data array
                        $fieldId = $field->id;
                        if (isset($_POST[$fieldId])) {
                            // Handle different field types
                            switch ($field->type) {
                                case 'checkbox':
                                    $data[$fieldId] = isset($_POST[$fieldId]) ? true : false;
                                    break;
                                case 'number':
                                    $data[$fieldId] = is_numeric($_POST[$fieldId]) ? $_POST[$fieldId] : 0;
                                    break;
                                default:
                                    $data[$fieldId] = trim($_POST[$fieldId]);
                            }
                        } else {
                            // Set default value if field is not in POST data
                            $data[$fieldId] = isset($field->default) ? $field->default : null;
                        }
                    }
                }
            }
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate location
            if (empty($data['location'])) {
                $data['location_err'] = 'Please enter a location';
            }
            
            // Validate dates
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif ($data['end_date'] < $data['start_date']) {
                $data['end_date_err'] = 'End date must be after start date';
            }
            
            if (empty($data['registration_start'])) {
                $data['registration_start_err'] = 'Please enter a registration start date';
            }
            
            if (empty($data['registration_end'])) {
                $data['registration_end_err'] = 'Please enter a registration end date';
            } elseif ($data['registration_end'] < $data['registration_start']) {
                $data['registration_end_err'] = 'Registration end date must be after registration start date';
            } elseif ($data['registration_end'] > $data['start_date']) {
                $data['registration_end_err'] = 'Registration must end before the show starts';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select a status';
            }
            
            // Check for errors
            $hasErrors = !empty($data['name_err']) || !empty($data['location_err']) || 
                        !empty($data['start_date_err']) || !empty($data['end_date_err']) || 
                        !empty($data['registration_start_err']) || !empty($data['registration_end_err']) || 
                        !empty($data['status_err']);
            
            if (!$hasErrors) {
                // Check if coordinator is exempt from listing fees
                $this->db = new Database();
                $this->db->query('SELECT exempt_from_listing_fees FROM users WHERE id = :id');
                $this->db->bind(':id', $userId);
                $user = $this->db->single();
                $isExempt = $user && $user->exempt_from_listing_fees;
                
                // Check if coordinator is pre-approved
                $this->db->query('SELECT * FROM pre_approved_cords 
                                 WHERE coordinator_id = :coordinator_id 
                                 AND begin_date <= CURDATE() 
                                 AND end_date >= CURDATE()');
                $this->db->bind(':coordinator_id', $userId);
                $preApproved = $this->db->single();
                $isPreApproved = !empty($preApproved);
                
                // If coordinator is exempt or pre-approved, mark listing as paid
                if ($isExempt || $isPreApproved) {
                    $data['listing_paid'] = true;
                } else {
                    // For coordinators who need to pay, set status to payment_pending
                    // unless admin has explicitly set a different status
                    $data['status'] = 'payment_pending';
                }
                
                // Create show
                $showId = $this->showModel->createShow($data);
                
                if ($showId) {
                    // Get coordinator info for email notification
                    $coordinator = $this->userModel->getUserById($userId);
                    $show = $this->showModel->getShowById($showId);
                    
                    // Send email notification to admins
                    $emailService = $this->model('EmailService');
                    $emailService->sendNewShowNotification($show, $coordinator);
                    
                    // Set success message
                    $this->setFlashMessage('coordinator_message', 'Show created successfully', 'success');
                    
                    // If exempt or pre-approved, redirect to show page
                    if ($isExempt || $isPreApproved) {
                        if ($isExempt) {
                            $this->setFlashMessage('coordinator_message', 'Show created successfully. Listing fee waived based on your account privileges.', 'success');
                        } else {
                            $this->setFlashMessage('coordinator_message', 'Show created successfully. Listing fee waived based on your pre-approved status.', 'success');
                        }
                        $this->redirect('coordinator/show/' . $showId);
                    } 
                    // Otherwise, redirect to payment page
                    else if ($listingFee > 0) {
                        $this->redirect('payment/showListing/' . $showId);
                    } else {
                        // If no listing fee, redirect to show page
                        $this->redirect('coordinator/show/' . $showId);
                    }
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('coordinator/shows/create_with_template', $data);
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'description' => '',
                'location' => '',
                'start_date' => '',
                'end_date' => '',
                'registration_start' => '',
                'registration_end' => '',
                'status' => 'draft',
                'fan_voting_enabled' => true,
                'registration_fee' => 0.00,
                'is_free' => false,
                'listing_fee' => $listingFee,
                'listing_fee_type' => $listingFeeType,
                'listing_paid' => false,
                'coordinator_id' => $userId, // Always assign to current coordinator
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_start_err' => '',
                'registration_end_err' => '',
                'status_err' => '',
                'title' => 'Create Show',
                'template' => $template,
                'is_admin' => false,
                'data' => [] // This will hold all field values for the template
            ];
            
            // Extract template fields to pre-populate with default values
            if ($template && isset($template->fields)) {
                $fields = json_decode($template->fields);
                if ($fields) {
                    foreach ($fields as $field) {
                        if (isset($field->id) && isset($field->default)) {
                            $data['data'][$field->id] = $field->default;
                        } else if (isset($field->id)) {
                            $data['data'][$field->id] = '';
                        }
                    }
                }
            }
            
            // Load view with template
            $this->view('coordinator/shows/create_with_template', $data);
        }
    }

    // ============================================================================
    // SECTION 5: SHOW MANAGEMENT
    // ============================================================================

    /**
     * Legacy create show method - kept for reference
     */
    public function _createShow_legacy() {
        // Load the form designer model and entity template manager
        $formDesignerModel = $this->model('FormDesignerModel');
        $entityTemplateManager = $this->model('EntityTemplateManager');
        $settingsModel = $this->model('SettingsModel');
        
        // Get the listing fee from settings
        $listingFee = $settingsModel->getSetting('show_listing_fee', 0);
        
        // First check if there's a default template set in DefaultTemplateManager
        $templateId = $entityTemplateManager->getBestTemplateId('show', 0);
        
        // If a template was found, use it
        if ($templateId) {
            $template = $formDesignerModel->getFormTemplateById($templateId);
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CoordinatorController::createShow - Using template ID {$templateId} from DefaultTemplateManager");
            }
        } else {
            // Fall back to the default admin form template
            $template = $formDesignerModel->getFormTemplateByTypeAndEntity('admin_show', 0);
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CoordinatorController::createShow - Using default admin_show template");
            }
        }
        
        // Debug template information
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            if ($template) {
                error_log("CoordinatorController::createShow - Template found: ID={$template->id}, Name={$template->name}, Type={$template->type}");
            } else {
                error_log("CoordinatorController::createShow - No template found");
            }
        }
        
        // If no template exists, create a basic one
        if (!$template) {
            // Log the issue
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Default show form template not found. Creating a basic template.');
            }
            
            // Create a basic template with essential fields including featured image
            $basicFields = [
                (object)[
                    'id' => 'name',
                    'type' => 'text',
                    'label' => 'Show Name',
                    'required' => true,
                    'row' => 0,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'location',
                    'type' => 'text',
                    'label' => 'Location',
                    'required' => true,
                    'row' => 0,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'description',
                    'type' => 'textarea',
                    'label' => 'Description',
                    'required' => false,
                    'row' => 1,
                    'width' => 'col-md-12'
                ],
                (object)[
                    'id' => 'start_date',
                    'type' => 'date',
                    'label' => 'Start Date',
                    'required' => true,
                    'row' => 2,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'end_date',
                    'type' => 'date',
                    'label' => 'End Date',
                    'required' => true,
                    'row' => 2,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'registration_start',
                    'type' => 'date',
                    'label' => 'Registration Start',
                    'required' => true,
                    'row' => 3,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'registration_end',
                    'type' => 'date',
                    'label' => 'Registration End',
                    'required' => true,
                    'row' => 3,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'coordinator_id',
                    'type' => 'select',
                    'label' => 'Coordinator',
                    'required' => true,
                    'row' => 4,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'status',
                    'type' => 'select',
                    'label' => 'Status',
                    'required' => true,
                    'row' => 4,
                    'width' => 'col-md-6',
                    'options' => [
                        (object)['value' => 'draft', 'label' => 'Draft'],
                        (object)['value' => 'published', 'label' => 'Published'],
                        (object)['value' => 'cancelled', 'label' => 'Cancelled'],
                        (object)['value' => 'completed', 'label' => 'Completed']
                    ]
                ],
                (object)[
                    'id' => 'listing_paid',
                    'type' => 'checkbox',
                    'label' => 'Listing Fee Paid',
                    'required' => false,
                    'row' => 5,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'featured_image',
                    'type' => 'image',
                    'label' => 'Featured Image',
                    'required' => false,
                    'row' => 6,
                    'width' => 'col-md-6',
                    'placeholder' => 'Select a featured image for this show'
                ]
            ];
            
            // Create a temporary template object
            $template = (object)[
                'id' => 0,
                'name' => 'Default Show Admin Form (Temporary)',
                'type' => 'admin_show',
                'entity_id' => 0,
                'fields' => json_encode($basicFields)
            ];
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data - start with standard fields
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'location' => trim($_POST['location']),
                'start_date' => convertUserDateTimeToUTC(trim($_POST['start_date']), $this->auth->getCurrentUserId()),
                'end_date' => convertUserDateTimeToUTC(trim($_POST['end_date']), $this->auth->getCurrentUserId()),
                'registration_start' => isset($_POST['registration_open_date']) ? convertUserDateTimeToUTC(trim($_POST['registration_open_date']), $this->auth->getCurrentUserId()) : (isset($_POST['registration_start']) ? convertUserDateTimeToUTC(trim($_POST['registration_start']), $this->auth->getCurrentUserId()) : ''),
                'registration_end' => isset($_POST['registration_close_date']) ? convertUserDateTimeToUTC(trim($_POST['registration_close_date']), $this->auth->getCurrentUserId()) : (isset($_POST['registration_end']) ? convertUserDateTimeToUTC(trim($_POST['registration_end']), $this->auth->getCurrentUserId()) : ''),
                'coordinator_id' => $this->auth->getCurrentUserId(), // Always assign to current coordinator
                'status' => isset($_POST['status']) ? trim($_POST['status']) : 'draft',
                'fan_voting_enabled' => isset($_POST['fan_voting_enabled']) ? 1 : 0,
                'listing_fee' => $listingFee,
                'listing_paid' => 0, // Not paid yet
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_open_date_err' => '',
                'registration_close_date_err' => '',
                'title' => 'Create Show',
                'template' => $template,
                'listing_fee' => $listingFee
            ];
            
            // Get custom fields from the template
            $templateFields = json_decode($template->fields, true);
            if (is_array($templateFields)) {
                foreach ($templateFields as $field) {
                    // Skip if field doesn't have an ID or is a system field
                    if (!isset($field['id']) || in_array($field['id'], [
                        'id', 'created_at', 'updated_at', 'name', 'description', 'location', 
                        'start_date', 'end_date', 'registration_start', 'registration_end',
                        'registration_open_date', 'registration_close_date', 
                        'coordinator_id', 'status', 'fan_voting_enabled', 'listing_fee', 
                        'listing_paid'
                    ])) {
                        continue;
                    }
                    
                    // Skip section dividers and HTML fields (they don't need database columns)
                    if (isset($field['type']) && in_array($field['type'], ['section', 'html'])) {
                        continue;
                    }
                    
                    // Add the field to the data array
                    $fieldId = $field['id'];
                    if (isset($_POST[$fieldId])) {
                        // Handle different field types
                        switch ($field['type']) {
                            case 'checkbox':
                                $data[$fieldId] = isset($_POST[$fieldId]) ? true : false;
                                break;
                            case 'number':
                                $data[$fieldId] = is_numeric($_POST[$fieldId]) ? $_POST[$fieldId] : 0;
                                break;
                            default:
                                $data[$fieldId] = trim($_POST[$fieldId]);
                        }
                    } else {
                        // Set default value if field is not in POST data
                        $data[$fieldId] = isset($field['default']) ? $field['default'] : null;
                    }
                }
            }
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate location
            if (empty($data['location'])) {
                $data['location_err'] = 'Please enter a location';
            }
            
            // Validate dates
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif ($data['end_date'] < $data['start_date']) {
                $data['end_date_err'] = 'End date must be after start date';
            }
            
            if (empty($data['registration_start'])) {
                $data['registration_start_err'] = 'Please enter a registration start date';
            }
            
            if (empty($data['registration_end'])) {
                $data['registration_end_err'] = 'Please enter a registration end date';
            } elseif ($data['registration_end'] < $data['registration_start']) {
                $data['registration_end_err'] = 'Registration end date must be after registration start date';
            } elseif ($data['registration_end'] > $data['start_date']) {
                $data['registration_end_err'] = 'Registration must end before the show starts';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['location_err']) && 
                empty($data['start_date_err']) && empty($data['end_date_err']) && 
                empty($data['registration_start_err']) && empty($data['registration_end_err'])) {
                
                // Create show
                $showId = $this->showModel->createShow($data);
                
                if ($showId) {
                    // Set success message
                    $this->setFlashMessage('coordinator_message', 'Show created successfully');
                    
                    // If there's a listing fee, redirect to payment page
                    if ($listingFee > 0) {
                        $this->redirect('payment/showListing/' . $showId);
                    } else {
                        // Otherwise, redirect to show page
                        $this->redirect('coordinator/show/' . $showId);
                    }
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with template
                $this->view('coordinator/shows/create_with_template', $data);
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'description' => '',
                'location' => '',
                'start_date' => '',
                'end_date' => '',
                'registration_start' => '',
                'registration_end' => '',
                'coordinator_id' => $this->auth->getCurrentUserId(),
                'status' => 'draft',
                'fan_voting_enabled' => true,
                'registration_fee' => 0.00,
                'is_free' => false,
                'listing_fee' => $listingFee,
                'listing_paid' => false,
                'featured_image_id' => '',
                'featured_image' => '',
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_start_err' => '',
                'registration_end_err' => '',
                'title' => 'Create Show',
                'template' => $template,
                'data' => [] // This will hold all field values for the template
            ];
            
            // Extract template fields to pre-populate with default values
            if ($template && isset($template->fields)) {
                // Ensure fields is properly decoded if it's a string
                if (is_string($template->fields)) {
                    $fields = json_decode($template->fields);
                    
                    // Check for JSON decoding errors
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log("CoordinatorController::createShow - Error decoding template fields: " . json_last_error_msg());
                        }
                        $fields = [];
                    }
                } else {
                    $fields = $template->fields;
                }
                
                // Process fields if we have any
                if ($fields && (is_array($fields) || is_object($fields))) {
                    foreach ($fields as $field) {
                        if (isset($field->id) && isset($field->default)) {
                            $data['data'][$field->id] = $field->default;
                        } else if (isset($field->id)) {
                            $data['data'][$field->id] = '';
                        }
                    }
                }
                
                // Debug the template
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("CoordinatorController::createShow - Template: " . json_encode($template));
                    error_log("CoordinatorController::createShow - Template fields type: " . gettype($template->fields));
                    error_log("CoordinatorController::createShow - Fields count: " . (is_array($fields) || is_object($fields) ? count($fields) : 0));
                    if (is_array($fields) || is_object($fields)) {
                        error_log("CoordinatorController::createShow - First field: " . (isset($fields[0]) ? json_encode($fields[0]) : 'none'));
                    }
                }
            }
            
            // Load view with template
            $this->view('coordinator/shows/create_with_template', $data);
        }
    }
    
    /**
     * Reports dashboard
     */
    public function reports() {
        // Get current user ID
        $userId = $this->auth->getCurrentUserId();

        // Get report counts and statistics only (for performance)
        $reportCounts = $this->showModel->getCoordinatorReportCounts($userId);
        $reportStats = $this->showModel->getCoordinatorReportStats($userId);

        $data = [
            'title' => 'Reports Dashboard',
            'report_counts' => $reportCounts,
            'report_stats' => $reportStats
        ];

        $this->view('coordinator/reports/index', $data);
    }

    /**
     * AJAX endpoint for loading paginated coordinator shows for reports
     *
     * @return void
     */
    public function loadReportShows() {
        // Check if request is AJAX
        if (!isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            return;
        }

        // Get current user ID
        $userId = $this->auth->getCurrentUserId();

        // Get parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $statusFilter = $_GET['status_filter'] ?? 'all';
        $dateFilter = $_GET['date_filter'] ?? '';
        $orderBy = $_GET['order_by'] ?? 'start_date';
        $orderDir = $_GET['order_dir'] ?? 'DESC';

        try {
            $result = $this->showModel->getPaginatedCoordinatorReportShows(
                $userId, $page, $perPage, $search, $statusFilter, $dateFilter, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'shows' => $result['shows'],
                'pagination' => $result['pagination']
            ]);
        } catch (Exception $e) {
            error_log('Error in CoordinatorController::loadReportShows: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load shows']);
        }
    }

    // ============================================================================
    // SECTION 7: JUDGING METRICS MANAGEMENT
    // ============================================================================

    /**
     * Registration Report
     *
     * @return void
     */
    public function registrationReport() {
        // Get user ID
        $userId = $this->auth->getCurrentUserId();
        
        // Get show ID from query string if provided
        $showId = isset($_GET['show_id']) ? filter_var($_GET['show_id'], FILTER_VALIDATE_INT) : null;
        
        // If no show ID provided, get the most recent show for this coordinator
        if (!$showId) {
            $this->db = new Database();
            $this->db->query('SELECT id FROM shows WHERE coordinator_id = :coordinator_id ORDER BY start_date DESC LIMIT 1');
            $this->db->bind(':coordinator_id', $userId);
            $show = $this->db->single();
            
            if ($show) {
                $showId = $show->id;
            } else {
                $this->setFlashMessage('report_message', 'No shows found', 'alert alert-warning');
                $this->redirect('coordinator/reports');
                return;
            }
        }
        
        // Get show details
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->setFlashMessage('report_message', 'Show not found', 'alert alert-danger');
            $this->redirect('coordinator/reports');
            return;
        }
        
        // Check if user is authorized to view this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $userId) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get registration statistics
        $this->db = new Database();
        
        // Total registrations
        $this->db->query('SELECT COUNT(*) as total FROM registrations WHERE show_id = :show_id');
        $this->db->bind(':show_id', $showId);
        $totalRegistrations = $this->db->single()->total;
        
        // Registrations by category
        $this->db->query('SELECT sc.name, COUNT(r.id) as count 
                         FROM show_categories sc 
                         LEFT JOIN registrations r ON r.category_id = sc.id 
                         WHERE sc.show_id = :show_id 
                         GROUP BY sc.id 
                         ORDER BY count DESC');
        $this->db->bind(':show_id', $showId);
        $registrationsByCategory = $this->db->resultSet();
        
        // Registrations by day
        $this->db->query('SELECT DATE(created_at) as date, COUNT(*) as count 
                         FROM registrations 
                         WHERE show_id = :show_id 
                         GROUP BY DATE(created_at) 
                         ORDER BY date');
        $this->db->bind(':show_id', $showId);
        $registrationsByDay = $this->db->resultSet();
        
        // Registrations by status
        $this->db->query('SELECT status, COUNT(*) as count 
                         FROM registrations 
                         WHERE show_id = :show_id 
                         GROUP BY status');
        $this->db->bind(':show_id', $showId);
        $registrationsByStatus = $this->db->resultSet();
        
        // Get all shows for the dropdown
        if ($this->auth->hasRole('admin')) {
            $shows = $this->showModel->getShows();
        } else {
            $this->db->query('SELECT * FROM shows WHERE coordinator_id = :coordinator_id ORDER BY start_date DESC');
            $this->db->bind(':coordinator_id', $userId);
            $shows = $this->db->resultSet();
        }
        
        $data = [
            'title' => 'Registration Report - ' . $show->name,
            'show' => $show,
            'shows' => $shows,
            'total_registrations' => $totalRegistrations,
            'registrations_by_category' => $registrationsByCategory,
            'registrations_by_day' => $registrationsByDay,
            'registrations_by_status' => $registrationsByStatus
        ];
        
        $this->view('coordinator/reports/registration_report', $data);
    }

    // ============================================================================
    // SECTION 8: AGE WEIGHTS MANAGEMENT
    // ============================================================================

    /**
     * Financial Report
     *
     * @return void
     */
    public function financialReport() {
        // Get user ID
        $userId = $this->auth->getCurrentUserId();
        
        // Get show ID from query string if provided
        $showId = isset($_GET['show_id']) ? filter_var($_GET['show_id'], FILTER_VALIDATE_INT) : null;
        
        // If no show ID provided, get the most recent show for this coordinator
        if (!$showId) {
            $this->db = new Database();
            $this->db->query('SELECT id FROM shows WHERE coordinator_id = :coordinator_id ORDER BY start_date DESC LIMIT 1');
            $this->db->bind(':coordinator_id', $userId);
            $show = $this->db->single();
            
            if ($show) {
                $showId = $show->id;
            } else {
                $this->setFlashMessage('report_message', 'No shows found', 'alert alert-warning');
                $this->redirect('coordinator/reports');
                return;
            }
        }
        
        // Get show details
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->setFlashMessage('report_message', 'Show not found', 'alert alert-danger');
            $this->redirect('coordinator/reports');
            return;
        }
        
        // Check if user is authorized to view this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $userId) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get financial statistics
        $this->db = new Database();
        
        // Total revenue
        $this->db->query('SELECT SUM(fee) as total_revenue FROM registrations WHERE show_id = :show_id AND status = "approved"');
        $this->db->bind(':show_id', $showId);
        $totalRevenue = $this->db->single()->total_revenue ?? 0;
        
        // Revenue by category
        $this->db->query('SELECT sc.name, SUM(r.fee) as revenue, COUNT(r.id) as count 
                         FROM show_categories sc 
                         LEFT JOIN registrations r ON r.category_id = sc.id AND r.status = "approved"
                         WHERE sc.show_id = :show_id 
                         GROUP BY sc.id 
                         ORDER BY revenue DESC');
        $this->db->bind(':show_id', $showId);
        $revenueByCategory = $this->db->resultSet();
        
        // Revenue by payment method
        $this->db->query('SELECT pm.name, SUM(r.fee) as revenue, COUNT(r.id) as count 
                         FROM payment_methods pm 
                         LEFT JOIN registrations r ON r.payment_method_id = pm.id AND r.show_id = :show_id AND r.status = "approved"
                         GROUP BY pm.id 
                         ORDER BY revenue DESC');
        $this->db->bind(':show_id', $showId);
        $revenueByPaymentMethod = $this->db->resultSet();
        
        // Revenue by day
        $this->db->query('SELECT DATE(created_at) as date, SUM(fee) as revenue 
                         FROM registrations 
                         WHERE show_id = :show_id AND status = "approved"
                         GROUP BY DATE(created_at) 
                         ORDER BY date');
        $this->db->bind(':show_id', $showId);
        $revenueByDay = $this->db->resultSet();
        
        // Get all shows for the dropdown
        if ($this->auth->hasRole('admin')) {
            $shows = $this->showModel->getShows();
        } else {
            $this->db->query('SELECT * FROM shows WHERE coordinator_id = :coordinator_id ORDER BY start_date DESC');
            $this->db->bind(':coordinator_id', $userId);
            $shows = $this->db->resultSet();
        }
        
        $data = [
            'title' => 'Financial Report - ' . $show->name,
            'show' => $show,
            'shows' => $shows,
            'total_revenue' => $totalRevenue,
            'revenue_by_category' => $revenueByCategory,
            'revenue_by_payment_method' => $revenueByPaymentMethod,
            'revenue_by_day' => $revenueByDay
        ];
        
        $this->view('coordinator/reports/financial_report', $data);
    }

    // ============================================================================
    // SECTION 6: CATEGORY MANAGEMENT
    // ============================================================================

    /**
     * Judging Report
     *
     * @return void
     */
    public function judgingReport() {
        // Get user ID
        $userId = $this->auth->getCurrentUserId();
        
        // Get show ID from query string if provided
        $showId = isset($_GET['show_id']) ? filter_var($_GET['show_id'], FILTER_VALIDATE_INT) : null;
        
        // If no show ID provided, get the most recent show for this coordinator
        if (!$showId) {
            $this->db = new Database();
            $this->db->query('SELECT id FROM shows WHERE coordinator_id = :coordinator_id ORDER BY start_date DESC LIMIT 1');
            $this->db->bind(':coordinator_id', $userId);
            $show = $this->db->single();
            
            if ($show) {
                $showId = $show->id;
            } else {
                $this->setFlashMessage('report_message', 'No shows found', 'alert alert-warning');
                $this->redirect('coordinator/reports');
                return;
            }
        }
        
        // Get show details
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->setFlashMessage('report_message', 'Show not found', 'alert alert-danger');
            $this->redirect('coordinator/reports');
            return;
        }
        
        // Check if user is authorized to view this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $userId) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get judging statistics
        $this->db = new Database();
        
        // Get categories
        $this->db->query('SELECT * FROM show_categories WHERE show_id = :show_id ORDER BY name');
        $this->db->bind(':show_id', $showId);
        $categories = $this->db->resultSet();
        
        // Get winners by category
        $winners = [];
        foreach ($categories as $category) {
            $this->db->query('SELECT r.id, r.registration_number, v.make, v.model, v.year, v.color, 
                             u.name as owner_name, 
                             SUM(s.score) as total_score 
                             FROM registrations r 
                             JOIN vehicles v ON r.vehicle_id = v.id 
                             JOIN users u ON r.user_id = u.id 
                             LEFT JOIN scores s ON r.id = s.registration_id 
                             WHERE r.category_id = :category_id AND r.status = "approved"
                             GROUP BY r.id 
                             ORDER BY total_score DESC 
                             LIMIT 3');
            $this->db->bind(':category_id', $category->id);
            $categoryWinners = $this->db->resultSet();
            
            if (!empty($categoryWinners)) {
                $winners[$category->id] = [
                    'category_name' => $category->name,
                    'winners' => $categoryWinners
                ];
            }
        }
        
        // Get judges
        $this->db->query('SELECT u.id, u.name, 
                         COUNT(DISTINCT s.registration_id) as vehicles_judged, 
                         COUNT(s.id) as scores_submitted 
                         FROM users u 
                         JOIN judge_assignments ja ON u.id = ja.judge_id 
                         LEFT JOIN scores s ON u.id = s.judge_id 
                         WHERE ja.show_id = :show_id 
                         GROUP BY u.id');
        $this->db->bind(':show_id', $showId);
        $judges = $this->db->resultSet();
        
        // Get all shows for the dropdown
        if ($this->auth->hasRole('admin')) {
            $shows = $this->showModel->getShows();
        } else {
            $this->db->query('SELECT * FROM shows WHERE coordinator_id = :coordinator_id ORDER BY start_date DESC');
            $this->db->bind(':coordinator_id', $userId);
            $shows = $this->db->resultSet();
        }
        
        $data = [
            'title' => 'Judging Report - ' . $show->name,
            'show' => $show,
            'shows' => $shows,
            'categories' => $categories,
            'winners' => $winners,
            'judges' => $judges
        ];
        
        $this->view('coordinator/reports/judging_report', $data);
    }
    
    /**
     * Show Report
     * 
     * @param int $showId Show ID
     * @return void
     */
    public function showReport($showId = 0) {
        // Get user ID
        $userId = $this->auth->getCurrentUserId();
        
        // Get show details
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->setFlashMessage('report_message', 'Show not found', 'alert alert-danger');
            $this->redirect('coordinator/reports');
            return;
        }
        
        // Check if user is authorized to view this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $userId) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show statistics
        $this->db = new Database();
        
        // Total registrations
        $this->db->query('SELECT COUNT(*) as total FROM registrations WHERE show_id = :show_id');
        $this->db->bind(':show_id', $showId);
        $totalRegistrations = $this->db->single()->total;
        
        // Approved registrations
        $this->db->query('SELECT COUNT(*) as total FROM registrations WHERE show_id = :show_id AND status = "approved"');
        $this->db->bind(':show_id', $showId);
        $approvedRegistrations = $this->db->single()->total;
        
        // Pending registrations
        $this->db->query('SELECT COUNT(*) as total FROM registrations WHERE show_id = :show_id AND status = "pending"');
        $this->db->bind(':show_id', $showId);
        $pendingRegistrations = $this->db->single()->total;
        
        // Total revenue
        $this->db->query('SELECT SUM(fee) as total FROM registrations WHERE show_id = :show_id AND status = "approved"');
        $this->db->bind(':show_id', $showId);
        $totalRevenue = $this->db->single()->total ?? 0;
        
        // Registrations by category
        $this->db->query('SELECT sc.name, COUNT(r.id) as count 
                         FROM show_categories sc 
                         LEFT JOIN registrations r ON r.category_id = sc.id 
                         WHERE sc.show_id = :show_id 
                         GROUP BY sc.id 
                         ORDER BY count DESC');
        $this->db->bind(':show_id', $showId);
        $registrationsByCategory = $this->db->resultSet();
        
        // Get recent registrations
        $this->db->query('SELECT r.id, r.registration_number, r.created_at, r.status, r.fee,
                         v.make, v.model, v.year, 
                         u.name, u.email,
                         sc.name as category_name
                         FROM registrations r 
                         JOIN vehicles v ON r.vehicle_id = v.id 
                         JOIN users u ON r.user_id = u.id 
                         JOIN show_categories sc ON r.category_id = sc.id
                         WHERE r.show_id = :show_id 
                         ORDER BY r.created_at DESC 
                         LIMIT 10');
        $this->db->bind(':show_id', $showId);
        $recentRegistrations = $this->db->resultSet();
        
        $data = [
            'title' => 'Show Report - ' . $show->name,
            'show' => $show,
            'total_registrations' => $totalRegistrations,
            'approved_registrations' => $approvedRegistrations,
            'pending_registrations' => $pendingRegistrations,
            'total_revenue' => $totalRevenue,
            'registrations_by_category' => $registrationsByCategory,
            'recent_registrations' => $recentRegistrations
        ];
        
        $this->view('coordinator/reports/show_report', $data);
    }
    
    /**
     * Export Registrations
     * 
     * @param int $showId Show ID
     * @return void
     */
    public function exportRegistrations($showId = 0) {
        // Get user ID
        $userId = $this->auth->getCurrentUserId();
        
        // Get show details
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->setFlashMessage('report_message', 'Show not found', 'alert alert-danger');
            $this->redirect('coordinator/reports');
            return;
        }
        
        // Check if user is authorized to view this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $userId) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get registrations
        $this->db = new Database();
        
        // Check if the phone field exists in the users table
        $this->db->query("SHOW COLUMNS FROM users LIKE 'phone'");
        $phoneFieldExists = $this->db->single();
        
        if ($phoneFieldExists) {
            $this->db->query('SELECT r.id, r.registration_number, r.created_at, r.status, r.fee,
                             v.make, v.model, v.year, v.color, v.license_plate, v.vin,
                             u.name, u.email, u.phone, u.address, u.city, u.state, u.zip,
                             sc.name as category_name,
                             pm.name as payment_method
                             FROM registrations r 
                             JOIN vehicles v ON r.vehicle_id = v.id 
                             JOIN users u ON r.user_id = u.id 
                             JOIN show_categories sc ON r.category_id = sc.id
                             LEFT JOIN payment_methods pm ON r.payment_method_id = pm.id
                             WHERE r.show_id = :show_id 
                             ORDER BY r.created_at');
        } else {
            // If phone field doesn't exist yet, use a simpler query
            $this->db->query('SELECT r.id, r.registration_number, r.created_at, r.status, r.fee,
                             v.make, v.model, v.year, v.color, v.license_plate, v.vin,
                             u.name, u.email, "" as phone, "" as address, "" as city, "" as state, "" as zip,
                             sc.name as category_name,
                             pm.name as payment_method
                             FROM registrations r 
                             JOIN vehicles v ON r.vehicle_id = v.id 
                             JOIN users u ON r.user_id = u.id 
                             JOIN show_categories sc ON r.category_id = sc.id
                             LEFT JOIN payment_methods pm ON r.payment_method_id = pm.id
                             WHERE r.show_id = :show_id 
                             ORDER BY r.created_at');
        }
        
        $this->db->bind(':show_id', $showId);
        $registrations = $this->db->resultSet();
        
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $show->name . '_registrations.csv"');
        
        // Create a file pointer connected to the output stream
        $output = fopen('php://output', 'w');
        
        // Output the column headings
        fputcsv($output, [
            'Registration ID',
            'Registration Number',
            'Registration Date',
            'Status',
            'Fee',
            'Make',
            'Model',
            'Year',
            'Color',
            'License Plate',
            'VIN',
            'Owner Name',
            'Email',
            'Phone',
            'Address',
            'City',
            'State',
            'ZIP',
            'Category',
            'Payment Method'
        ]);
        
        // Output each row of the data
        foreach ($registrations as $registration) {
            fputcsv($output, [
                $registration->id,
                $registration->registration_number,
                $registration->created_at,
                $registration->status,
                $registration->fee,
                $registration->make,
                $registration->model,
                $registration->year,
                $registration->color,
                $registration->license_plate,
                $registration->vin,
                $registration->name,
                $registration->email,
                $registration->phone,
                $registration->address,
                $registration->city,
                $registration->state,
                $registration->zip,
                $registration->category_name,
                $registration->payment_method
            ]);
        }
        
        // Close the file pointer
        fclose($output);
        exit;
    }

    // ============================================================================
    // SECTION 4: DASHBOARD AND REPORTS
    // ============================================================================

    /**
     * Coordinator dashboard
     */
    public function dashboard() {
        // Get current user ID
        $userId = $this->auth->getCurrentUserId();

        // Get show counts only (for performance)
        if ($this->auth->hasRole('admin')) {
            // For admin, get all show counts (would need a separate method)
            $showCounts = [
                'total' => 0,
                'upcoming' => 0,
                'past' => 0,
                'draft' => 0
            ];
        } else {
            $showCounts = $this->showModel->getCoordinatorShowCounts($userId);
        }

        // Get coordinator shows for judge management dropdown
        $coordinatorShows = [];
        if (!$this->auth->hasRole('admin')) {
            $coordinatorShows = $this->showModel->getShowsByCoordinator($userId, 10); // Get up to 10 recent shows
        }

        $data = [
            'title' => 'Coordinator Dashboard',
            'show_counts' => $showCounts,
            'coordinator_shows' => $coordinatorShows
        ];

        $this->view('coordinator/dashboard', $data);
    }

    // ============================================================================
    // SECTION 9: REGISTRATION MANAGEMENT
    // ============================================================================

    /**
     * AJAX endpoint for loading paginated coordinator shows
     *
     * @return void
     */
    public function loadShows() {
        // Check if request is AJAX
        if (!isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            return;
        }

        // Get current user ID
        $userId = $this->auth->getCurrentUserId();

        // Get parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $statusFilter = $_GET['status_filter'] ?? 'all';
        $orderBy = $_GET['order_by'] ?? 'start_date';
        $orderDir = $_GET['order_dir'] ?? 'DESC';

        try {
            $result = $this->showModel->getPaginatedCoordinatorShows(
                $userId, $page, $perPage, $search, $statusFilter, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'shows' => $result['shows'],
                'pagination' => $result['pagination']
            ]);
        } catch (Exception $e) {
            error_log('Error in CoordinatorController::loadShows: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load shows']);
        }
    }

    /**
     * Show details
     *
     * @param int $id Show ID
     */
    public function show($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to view this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get categories
        $categories = $this->showModel->getShowCategories($id);
        
        // Get registration counts
        $registrationCounts = $this->registrationModel->countRegistrationsByCategory($id);
        
        // Get judge assignments
        $judgeAssignments = $this->showModel->getJudgeAssignments($id);
        
        $data = [
            'title' => $show->name,
            'show' => $show,
            'categories' => $categories,
            'registration_counts' => $registrationCounts,
            'judge_assignments' => $judgeAssignments
        ];
        
        $this->view('coordinator/show', $data);
    }
    
    /**
     * Manage categories
     * 
     * @param int $showId Show ID
     */
    public function categories($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get categories
        $categories = $this->showModel->getShowCategories($showId);
        
        $data = [
            'title' => 'Manage Categories',
            'show' => $show,
            'categories' => $categories
        ];
        
        $this->view('coordinator/categories/index', $data);
    }
    
    /**
     * Add category
     * 
     * @param int $showId Show ID
     */
    public function addCategory($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'show_id' => $showId,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'registration_fee' => floatval($_POST['registration_fee']),
                'max_entries' => intval($_POST['max_entries']),
                'name_err' => '',
                'registration_fee_err' => '',
                'max_entries_err' => '',
                'title' => 'Add Category',
                'show' => $show
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate registration fee
            if ($data['registration_fee'] < 0) {
                $data['registration_fee_err'] = 'Registration fee cannot be negative';
            }
            
            // Validate max entries
            if ($data['max_entries'] < 0) {
                $data['max_entries_err'] = 'Max entries cannot be negative';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['registration_fee_err']) && 
                empty($data['max_entries_err'])) {
                
                // Create category
                $categoryId = $this->showModel->createCategory($data);
                
                if ($categoryId) {
                    // Set success message
                    $this->setFlashMessage('category_added', 'Category added successfully');
                    
                    // Check if there's a referrer in the form data
                    if (isset($_POST['referrer']) && !empty($_POST['referrer'])) {
                        $referrer = $_POST['referrer'];
                        
                        // Basic validation to ensure the URL is from our site
                        $urlRoot = URLROOT ?? BASE_URL ?? '';
                        if (!empty($urlRoot) && strpos($referrer, $urlRoot) === 0) {
                            // Redirect back to the referring page from form
                            header('Location: ' . $referrer);
                            exit;
                        }
                    } 
                    // Check if there's a referrer in the HTTP headers
                    else if (isset($_SERVER['HTTP_REFERER']) && !empty($_SERVER['HTTP_REFERER'])) {
                        $referrer = $_SERVER['HTTP_REFERER'];
                        
                        // Basic validation to ensure the URL is from our site
                        $urlRoot = URLROOT ?? BASE_URL ?? '';
                        if (!empty($urlRoot) && strpos($referrer, $urlRoot) === 0) {
                            // Redirect back to the referring page from headers
                            header('Location: ' . $referrer);
                            exit;
                        }
                    }
                    
                    // Fallback to categories page if no valid referrer
                    $this->redirect('coordinator/categories/' . $showId);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('coordinator/categories/add', $data);
            }
        } else {
            // Init data
            $data = [
                'show_id' => $showId,
                'name' => '',
                'description' => '',
                'registration_fee' => 0,
                'max_entries' => 0,
                'name_err' => '',
                'registration_fee_err' => '',
                'max_entries_err' => '',
                'title' => 'Add Category',
                'show' => $show
            ];
            
            // Load view
            $this->view('coordinator/categories/add', $data);
        }
    }
    
    /**
     * Edit category
     * 
     * @param int $id Category ID
     */
    public function editCategory($id) {
        // Get category
        $category = $this->showModel->getCategoryById($id);
        
        if (!$category) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($category->show_id);
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'registration_fee' => floatval($_POST['registration_fee']),
                'max_entries' => intval($_POST['max_entries']),
                'name_err' => '',
                'registration_fee_err' => '',
                'max_entries_err' => '',
                'title' => 'Edit Category',
                'show' => $show,
                'category' => $category
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate registration fee
            if ($data['registration_fee'] < 0) {
                $data['registration_fee_err'] = 'Registration fee cannot be negative';
            }
            
            // Validate max entries
            if ($data['max_entries'] < 0) {
                $data['max_entries_err'] = 'Max entries cannot be negative';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['registration_fee_err']) && 
                empty($data['max_entries_err'])) {
                
                // Update category
                if ($this->showModel->updateCategory($data)) {
                    // Redirect to categories page
                    $this->redirect('coordinator/categories/' . $category->show_id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('coordinator/categories/edit', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $category->id,
                'name' => $category->name,
                'description' => $category->description,
                'registration_fee' => $category->registration_fee,
                'max_entries' => $category->max_entries,
                'name_err' => '',
                'registration_fee_err' => '',
                'max_entries_err' => '',
                'title' => 'Edit Category',
                'show' => $show,
                'category' => $category
            ];
            
            // Load view
            $this->view('coordinator/categories/edit', $data);
        }
    }
    
    /**
     * Delete category
     * 
     * @param int $id Category ID
     */
    public function deleteCategory($id) {
        // Get category
        $category = $this->showModel->getCategoryById($id);
        
        if (!$category) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($category->show_id);
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete category
            if ($this->showModel->deleteCategory($id)) {
                // Set flash message
                $this->setFlashMessage('category_deleted', 'Category deleted successfully');
                // Redirect to edit show page with categories section anchor
                $this->redirect('coordinator/editShow/' . $category->show_id . '#categories');
            } else {
                $this->setFlashMessage('category_error', 'Failed to delete category', 'alert alert-danger');
                $this->redirect('coordinator/editShow/' . $category->show_id . '#categories');
            }
        } else {
            $this->redirect('coordinator/editShow/' . $category->show_id . '#categories');
        }
    }
    
    /**
     * Manage judging metrics
     * 
     * @param int $showId Show ID
     */
    public function metrics($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get metrics
        $metrics = $this->showModel->getJudgingMetrics($showId);
        
        $data = [
            'title' => 'Manage Judging Metrics',
            'show' => $show,
            'metrics' => $metrics
        ];
        
        $this->view('coordinator/metrics/index', $data);
    }
    
    /**
     * Add judging metric
     * 
     * @param int $showId Show ID
     */
    public function addMetric($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get categories for the show
            $categories = $this->showModel->getShowCategories($showId);
            
            // Get form data
            $data = [
                'show_id' => $showId,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'max_score' => intval($_POST['max_score']),
                'weight' => floatval($_POST['weight']),
                'display_order' => isset($_POST['display_order']) ? intval($_POST['display_order']) : 0,
                'category_id' => intval($_POST['category_id'] ?? 0),
                'categories' => $categories,
                'name_err' => '',
                'max_score_err' => '',
                'weight_err' => '',
                'display_order_err' => '',
                'title' => 'Add Judging Metric',
                'show' => $show
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate max score
            if ($data['max_score'] <= 0) {
                $data['max_score_err'] = 'Max score must be greater than 0';
            }
            
            // Validate weight
            if ($data['weight'] <= 0) {
                $data['weight_err'] = 'Weight must be greater than 0';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['max_score_err']) && 
                empty($data['weight_err']) && empty($data['display_order_err'])) {
                
                // Create metric
                $metricId = $this->showModel->createMetric($data);
                
                if ($metricId) {
                    // Set success message
                    $this->setFlashMessage('metric_added', 'Judging metric added successfully');
                    
                    // Check if there's a referrer in the form data
                    if (isset($_POST['referrer']) && !empty($_POST['referrer'])) {
                        $referrer = $_POST['referrer'];
                        
                        // Basic validation to ensure the URL is from our site
                        $urlRoot = URLROOT ?? BASE_URL ?? '';
                        if (!empty($urlRoot) && strpos($referrer, $urlRoot) === 0) {
                            // Redirect back to the referring page from form
                            header('Location: ' . $referrer);
                            exit;
                        }
                    } 
                    // Check if there's a referrer in the HTTP headers
                    else if (isset($_SERVER['HTTP_REFERER']) && !empty($_SERVER['HTTP_REFERER'])) {
                        $referrer = $_SERVER['HTTP_REFERER'];
                        
                        // Basic validation to ensure the URL is from our site
                        $urlRoot = URLROOT ?? BASE_URL ?? '';
                        if (!empty($urlRoot) && strpos($referrer, $urlRoot) === 0) {
                            // Redirect back to the referring page from headers
                            header('Location: ' . $referrer);
                            exit;
                        }
                    }
                    
                    // Fallback to metrics page if no valid referrer
                    $this->redirect('coordinator/metrics/' . $showId);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('coordinator/metrics/add', $data);
            }
        } else {
            // Get current highest display order
            $metrics = $this->showModel->getJudgingMetrics($showId);
            $displayOrder = 0;
            
            if (!empty($metrics)) {
                $lastMetric = end($metrics);
                $displayOrder = $lastMetric->display_order + 1;
            }
            
            // Get categories for the show
            $categories = $this->showModel->getShowCategories($showId);
            
            // Init data
            $data = [
                'show_id' => $showId,
                'name' => '',
                'description' => '',
                'max_score' => 10,
                'weight' => 1.0,
                'display_order' => $displayOrder,
                'category_id' => 0,
                'categories' => $categories,
                'name_err' => '',
                'max_score_err' => '',
                'weight_err' => '',
                'display_order_err' => '',
                'title' => 'Add Judging Metric',
                'show' => $show
            ];
            
            // Load view
            $this->view('coordinator/metrics/add', $data);
        }
    }
    
    /**
     * Edit judging metric
     * 
     * @param int $id Metric ID
     */
    public function editMetric($id) {
        // Get metric
        $metric = $this->showModel->getMetricById($id);
        
        if (!$metric) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($metric->show_id);
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get categories for the show
            $categories = $this->showModel->getShowCategories($metric->show_id);
            
            // Get form data
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'max_score' => intval($_POST['max_score']),
                'weight' => floatval($_POST['weight']),
                'display_order' => intval($_POST['display_order']),
                'category_id' => intval($_POST['category_id'] ?? 0),
                'categories' => $categories,
                'name_err' => '',
                'max_score_err' => '',
                'weight_err' => '',
                'display_order_err' => '',
                'title' => 'Edit Judging Metric',
                'show' => $show,
                'metric' => $metric
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate max score
            if ($data['max_score'] <= 0) {
                $data['max_score_err'] = 'Max score must be greater than 0';
            }
            
            // Validate weight
            if ($data['weight'] <= 0) {
                $data['weight_err'] = 'Weight must be greater than 0';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['max_score_err']) && 
                empty($data['weight_err']) && empty($data['display_order_err'])) {
                
                // Update metric
                if ($this->showModel->updateMetric($data)) {
                    // Redirect to metrics page
                    $this->redirect('coordinator/metrics/' . $metric->show_id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('coordinator/metrics/edit', $data);
            }
        } else {
            // Get categories for the show
            $categories = $this->showModel->getShowCategories($metric->show_id);
            
            // Init data
            $data = [
                'id' => $metric->id,
                'name' => $metric->name,
                'description' => $metric->description,
                'max_score' => $metric->max_score,
                'weight' => $metric->weight,
                'display_order' => $metric->display_order,
                'category_id' => $metric->category_id ?? 0,
                'categories' => $categories,
                'name_err' => '',
                'max_score_err' => '',
                'weight_err' => '',
                'display_order_err' => '',
                'title' => 'Edit Judging Metric',
                'show' => $show,
                'metric' => $metric
            ];
            
            // Load view
            $this->view('coordinator/metrics/edit', $data);
        }
    }
    
    /**
     * Delete judging metric
     * 
     * @param int $id Metric ID
     */
    public function deleteMetric($id) {
        // Get metric
        $metric = $this->showModel->getMetricById($id);
        
        if (!$metric) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($metric->show_id);
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete metric
            if ($this->showModel->deleteMetric($id)) {
                // Set flash message
                $this->setFlashMessage('metric_deleted', 'Metric deleted successfully');
                // Redirect to edit show page with metrics section anchor
                $this->redirect('coordinator/editShow/' . $metric->show_id . '#metrics');
            } else {
                $this->setFlashMessage('metric_error', 'Failed to delete metric', 'alert alert-danger');
                $this->redirect('coordinator/editShow/' . $metric->show_id . '#metrics');
            }
        } else {
            $this->redirect('coordinator/editShow/' . $metric->show_id . '#metrics');
        }
    }
    
    /**
     * Manage age weights
     * 
     * @param int $showId Show ID
     */
    public function ageWeights($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get age weights
        $ageWeights = $this->showModel->getAgeWeights($showId);
        
        $data = [
            'title' => 'Manage Age Weights',
            'show' => $show,
            'age_weights' => $ageWeights
        ];
        
        $this->view('coordinator/age_weights/index', $data);
    }
    
    /**
     * Add age weight
     * 
     * @param int $showId Show ID
     */
    public function addAgeWeight($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'show_id' => $showId,
                'min_age' => intval($_POST['min_age']),
                'max_age' => intval($_POST['max_age']),
                'multiplier' => floatval($_POST['multiplier']),
                'min_age_err' => '',
                'max_age_err' => '',
                'multiplier_err' => '',
                'title' => 'Add Age Weight',
                'show' => $show
            ];
            
            // Validate min age
            if ($data['min_age'] < 1900 || $data['min_age'] > gmdate('Y')) {
                $data['min_age_err'] = 'Min age must be between 1900 and current year';
            }
            
            // Validate max age
            if ($data['max_age'] < 1900 || $data['max_age'] > gmdate('Y')) {
                $data['max_age_err'] = 'Max age must be between 1900 and current year';
            } elseif ($data['max_age'] < $data['min_age']) {
                $data['max_age_err'] = 'Max age must be greater than or equal to min age';
            }
            
            // Validate multiplier
            if ($data['multiplier'] <= 0) {
                $data['multiplier_err'] = 'Multiplier must be greater than 0';
            }
            
            // Check for errors
            if (empty($data['min_age_err']) && empty($data['max_age_err']) && 
                empty($data['multiplier_err'])) {
                
                // Create age weight
                $ageWeightId = $this->showModel->createAgeWeight($data);
                
                if ($ageWeightId) {
                    // Set success message
                    $this->setFlashMessage('age_weight_added', 'Age weight added successfully');
                    
                    // Check if there's a referrer in the form data
                    if (isset($_POST['referrer']) && !empty($_POST['referrer'])) {
                        $referrer = $_POST['referrer'];
                        
                        // Basic validation to ensure the URL is from our site
                        $urlRoot = URLROOT ?? BASE_URL ?? '';
                        if (!empty($urlRoot) && strpos($referrer, $urlRoot) === 0) {
                            // Redirect back to the referring page from form
                            header('Location: ' . $referrer);
                            exit;
                        }
                    } 
                    // Check if there's a referrer in the HTTP headers
                    else if (isset($_SERVER['HTTP_REFERER']) && !empty($_SERVER['HTTP_REFERER'])) {
                        $referrer = $_SERVER['HTTP_REFERER'];
                        
                        // Basic validation to ensure the URL is from our site
                        $urlRoot = URLROOT ?? BASE_URL ?? '';
                        if (!empty($urlRoot) && strpos($referrer, $urlRoot) === 0) {
                            // Redirect back to the referring page from headers
                            header('Location: ' . $referrer);
                            exit;
                        }
                    }
                    
                    // Fallback to age weights page if no valid referrer
                    $this->redirect('coordinator/age_weights/' . $showId);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('coordinator/age_weights/add', $data);
            }
        } else {
            // Init data
            $data = [
                'show_id' => $showId,
                'min_age' => gmdate('Y') - 50,
                'max_age' => gmdate('Y') - 40,
                'multiplier' => 1.0,
                'min_age_err' => '',
                'max_age_err' => '',
                'multiplier_err' => '',
                'title' => 'Add Age Weight',
                'show' => $show
            ];
            
            // Load view
            $this->view('coordinator/age_weights/add', $data);
        }
    }
    
    /**
     * Edit age weight
     * 
     * @param int $id Age weight ID
     */
    public function editAgeWeight($id) {
        // Get age weight
        $this->db = new Database();
        $this->db->query('SELECT * FROM age_weights WHERE id = :id');
        $this->db->bind(':id', $id);
        $ageWeight = $this->db->single();
        
        if (!$ageWeight) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($ageWeight->show_id);
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'min_age' => intval($_POST['min_age']),
                'max_age' => intval($_POST['max_age']),
                'multiplier' => floatval($_POST['multiplier']),
                'min_age_err' => '',
                'max_age_err' => '',
                'multiplier_err' => '',
                'title' => 'Edit Age Weight',
                'show' => $show,
                'age_weight' => $ageWeight
            ];
            
            // Validate min age
            if ($data['min_age'] < 1900 || $data['min_age'] > gmdate('Y')) {
                $data['min_age_err'] = 'Min age must be between 1900 and current year';
            }
            
            // Validate max age
            if ($data['max_age'] < 1900 || $data['max_age'] > gmdate('Y')) {
                $data['max_age_err'] = 'Max age must be between 1900 and current year';
            } elseif ($data['max_age'] < $data['min_age']) {
                $data['max_age_err'] = 'Max age must be greater than or equal to min age';
            }
            
            // Validate multiplier
            if ($data['multiplier'] <= 0) {
                $data['multiplier_err'] = 'Multiplier must be greater than 0';
            }
            
            // Check for errors
            if (empty($data['min_age_err']) && empty($data['max_age_err']) && 
                empty($data['multiplier_err'])) {
                
                // Update age weight
                if ($this->showModel->updateAgeWeight($data)) {
                    // Redirect to age weights page
                    $this->redirect('coordinator/age_weights/' . $ageWeight->show_id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('coordinator/age_weights/edit', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $ageWeight->id,
                'min_age' => $ageWeight->min_age,
                'max_age' => $ageWeight->max_age,
                'multiplier' => $ageWeight->multiplier,
                'min_age_err' => '',
                'max_age_err' => '',
                'multiplier_err' => '',
                'title' => 'Edit Age Weight',
                'show' => $show,
                'age_weight' => $ageWeight
            ];
            
            // Load view
            $this->view('coordinator/age_weights/edit', $data);
        }
    }
    
    /**
     * Add default categories to show
     * 
     * @param int $id Show ID
     */
    public function addDefaultCategoriesToShow($id) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('coordinator/editShow/' . $id);
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Add default categories to show
        if ($this->defaultCategoryModel->addDefaultCategoriesToShow($id)) {
            // Set flash message
            $this->setFlashMessage('categories_added', 'Default categories added successfully');
        } else {
            $this->setFlashMessage('categories_error', 'Failed to add default categories to the show', 'alert alert-danger');
        }
        
        // Redirect to edit show page with categories section anchor
        $this->redirect('coordinator/editShow/' . $id . '#categories');
    }
    
    /**
     * Delete all categories for a show
     * 
     * @param int $id Show ID
     */
    public function deleteAllCategories($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete all categories
            $this->db = new Database();
            $this->db->query('DELETE FROM show_categories WHERE show_id = :show_id');
            $this->db->bind(':show_id', $id);
            
            if ($this->db->execute()) {
                // Set flash message
                $this->setFlashMessage('categories_deleted', 'All categories deleted successfully');
            } else {
                $this->setFlashMessage('categories_error', 'Failed to delete categories', 'alert alert-danger');
            }
            
            // Redirect to edit show page with categories section anchor
            $this->redirect('coordinator/editShow/' . $id . '#categories');
        } else {
            $this->redirect('coordinator/editShow/' . $id . '#categories');
        }
    }
    
    /**
     * Add default metrics to show
     * 
     * @param int $id Show ID
     */
    public function addDefaultMetricsToShow($id) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('coordinator/editShow/' . $id . '#metrics');
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if show already has metrics
        $existingMetrics = $this->showModel->getJudgingMetrics($id);
        
        if (!empty($existingMetrics)) {
            // Set flash message with more detailed instructions
            $this->setFlashMessage('metrics_error', 
                '<strong>This show already has metrics.</strong> Please use the "Delete All Metrics" button first, then try adding default metrics again.', 
                'alert alert-warning');
            
            $this->redirect('coordinator/editShow/' . $id . '#metrics');
            return;
        }
        
        // Add default metrics to show
        if ($this->defaultMetricModel->addDefaultMetricsToShow($id)) {
            // Set flash message
            $this->setFlashMessage('metrics_added', 'Default metrics added successfully');
        } else {
            $this->setFlashMessage('metrics_error', 'Failed to add default metrics to the show', 'alert alert-danger');
        }
        
        // Redirect to edit show page with metrics section anchor
        $this->redirect('coordinator/editShow/' . $id . '#metrics');
    }
    
    /**
     * Delete all metrics for a show
     * 
     * @param int $id Show ID
     */
    public function deleteAllMetrics($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete all metrics
            $this->db = new Database();
            $this->db->query('DELETE FROM judging_metrics WHERE show_id = :show_id');
            $this->db->bind(':show_id', $id);
            
            if ($this->db->execute()) {
                // Set flash message
                $this->setFlashMessage('metrics_deleted', 'All metrics deleted successfully');
            } else {
                $this->setFlashMessage('metrics_error', 'Failed to delete metrics', 'alert alert-danger');
            }
            
            // Redirect to edit show page with metrics section anchor
            $this->redirect('coordinator/editShow/' . $id . '#metrics');
        } else {
            $this->redirect('coordinator/editShow/' . $id . '#metrics');
        }
    }
    
    /**
     * Add default age weights to show
     * 
     * @param int $id Show ID
     */
    public function addDefaultAgeWeightsToShow($id) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('coordinator/editShow/' . $id . '#age-weights');
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if show already has age weights
        $existingAgeWeights = $this->judgingModel->getAgeWeightsByShowId($id);
        
        if (!empty($existingAgeWeights)) {
            // Set flash message with more detailed instructions
            $this->setFlashMessage('age_weights_error', 
                '<strong>This show already has age weights.</strong> Please use the "Delete All Age Weights" button first, then try adding default age weights again.', 
                'alert alert-warning');
            
            $this->redirect('coordinator/editShow/' . $id . '#age-weights');
            return;
        }
        
        // Add default age weights to show
        if ($this->defaultAgeWeightModel->addDefaultAgeWeightsToShow($id)) {
            // Set flash message
            $this->setFlashMessage('age_weights_added', 'Default age weights added successfully');
        } else {
            $this->setFlashMessage('age_weights_error', 'Failed to add default age weights to the show', 'alert alert-danger');
        }
        
        // Redirect to edit show page with age weights section anchor
        $this->redirect('coordinator/editShow/' . $id . '#age-weights');
    }
    
    /**
     * Delete all age weights for a show
     * 
     * @param int $id Show ID
     */
    public function deleteAllAgeWeights($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete all age weights
            $this->db = new Database();
            $this->db->query('DELETE FROM age_weights WHERE show_id = :show_id');
            $this->db->bind(':show_id', $id);
            
            if ($this->db->execute()) {
                // Set flash message
                $this->setFlashMessage('age_weights_deleted', 'All age weights deleted successfully');
            } else {
                $this->setFlashMessage('age_weights_error', 'Failed to delete age weights', 'alert alert-danger');
            }
            
            // Redirect to edit show page
            $this->redirect('coordinator/editShow/' . $id);
        } else {
            $this->redirect('coordinator/editShow/' . $id);
        }
    }
    
    /**
     * Delete age weight
     * 
     * @param int $id Age weight ID
     */
    public function deleteAgeWeight($id) {
        // Get age weight
        $this->db = new Database();
        $this->db->query('SELECT * FROM age_weights WHERE id = :id');
        $this->db->bind(':id', $id);
        $ageWeight = $this->db->single();
        
        if (!$ageWeight) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($ageWeight->show_id);
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete age weight
            if ($this->showModel->deleteAgeWeight($id)) {
                // Set flash message
                $this->setFlashMessage('age_weight_deleted', 'Age weight deleted successfully');
                // Redirect to edit show page with age weights section anchor
                $this->redirect('coordinator/editShow/' . $ageWeight->show_id . '#age-weights');
            } else {
                $this->setFlashMessage('age_weight_error', 'Failed to delete age weight', 'alert alert-danger');
                $this->redirect('coordinator/editShow/' . $ageWeight->show_id . '#age-weights');
            }
        } else {
            $this->redirect('coordinator/editShow/' . $ageWeight->show_id . '#age-weights');
        }
    }
    
    // REMOVED: judges method - replaced by unified JudgeManagementController
    
    // REMOVED: assignJudge and removeJudgeAssignment methods - replaced by unified JudgeManagementController
    
    /**
     * Manage registrations
     * 
     * @param int $showId Show ID
     */
    public function registrations($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get registrations
        $registrations = $this->registrationModel->getShowRegistrations($showId);
        
        // Get show categories
        $categories = $this->showModel->getShowCategories($showId);
        
        $data = [
            'title' => 'Manage Registrations',
            'show' => $show,
            'registrations' => $registrations,
            'categories' => $categories
        ];
        
        $this->view('coordinator/registrations/index', $data);
    }
    
    /**
     * View registration details
     * 
     * @param int $id Registration ID
     */
    public function viewRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get vehicle images
        $images = $this->vehicleModel->getVehicleImages($registration->vehicle_id);
        
        // Get categories
        $categories = $this->showModel->getShowCategories($registration->show_id);
        
        $data = [
            'title' => 'Registration Details',
            'registration' => $registration,
            'show' => $show,
            'images' => $images,
            'categories' => $categories
        ];
        
        $this->view('coordinator/registrations/view', $data);
    }
    
    /**
     * Update registration status
     * 
     * @param int $id Registration ID
     */
    public function updateRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'category_id' => intval($_POST['category_id']),
                'status' => trim($_POST['status']),
                'display_number' => trim($_POST['display_number']),
                'category_id_err' => '',
                'status_err' => ''
            ];
            
            // Validate category
            if (empty($data['category_id'])) {
                $data['category_id_err'] = 'Please select a category';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select a status';
            }
            
            // Check for errors
            if (empty($data['category_id_err']) && empty($data['status_err'])) {
                // Update registration
                if ($this->registrationModel->updateRegistration($data)) {
                    // Redirect to registration details
                    $this->redirect('coordinator/viewRegistration/' . $id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Get vehicle images
                $images = $this->vehicleModel->getVehicleImages($registration->vehicle_id);
                
                // Get categories
                $categories = $this->showModel->getShowCategories($registration->show_id);
                
                $data['title'] = 'Registration Details';
                $data['registration'] = $registration;
                $data['show'] = $show;
                $data['images'] = $images;
                $data['categories'] = $categories;
                
                // Load view with errors
                $this->view('coordinator/registrations/view', $data);
            }
        } else {
            $this->redirect('coordinator/view_registration/' . $id);
        }
    }
    
    /**
     * Save coordinator notes for a registration
     * 
     * @param int $id Registration ID
     */
    public function saveNotes($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'notes' => trim($_POST['notes'])
            ];
            
            // Update notes
            if ($this->registrationModel->updateNotes($data)) {
                // Set flash message
                $_SESSION['flash_message'] = [
                    'type' => 'success',
                    'message' => 'Notes updated successfully.'
                ];
                
                // Redirect back to registration details
                $this->redirect('coordinator/viewRegistration/' . $id);
            } else {
                // Set flash message
                $_SESSION['flash_message'] = [
                    'type' => 'danger',
                    'message' => 'Failed to update notes.'
                ];
                
                // Redirect back to registration details
                $this->redirect('coordinator/viewRegistration/' . $id);
            }
        } else {
            $this->redirect('coordinator/viewRegistration/' . $id);
        }
    }
    
    /**
     * Update payment status
     * 
     * @param int $id Registration ID
     */
    public function updatePayment($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'payment_status' => trim($_POST['payment_status']),
                'payment_reference' => trim($_POST['payment_reference'] ?? ''),
                'payment_status_err' => ''
            ];
            
            // Validate payment status
            if (empty($data['payment_status'])) {
                $data['payment_status_err'] = 'Please select a payment status';
            }
            
            // Check for errors
            if (empty($data['payment_status_err'])) {
                // Create update data for the registration model
                $updateData = [
                    'id' => $id,
                    'payment_status' => $data['payment_status'],
                    'payment_reference' => $data['payment_reference']
                ];
                
                // Debug log to check what's being passed to the model
                error_log('Updating payment for registration ' . $id . ' with status: ' . $data['payment_status'] . ' and reference: ' . $data['payment_reference']);
                
                // Try direct database update as a test
                try {
                    $db = new Database();
                    $db->query("UPDATE registrations SET payment_status = :status WHERE id = :id");
                    $db->bind(':status', $data['payment_status']);
                    $db->bind(':id', $id);
                    $directResult = $db->execute();
                    error_log('Direct update result: ' . ($directResult ? 'SUCCESS' : 'FAILED'));
                    
                    // Verify the update
                    $db->query("SELECT payment_status FROM registrations WHERE id = :id");
                    $db->bind(':id', $id);
                    $checkResult = $db->single();
                    error_log('After direct update, payment_status is: ' . ($checkResult ? $checkResult->payment_status : 'NULL'));
                } catch (Exception $e) {
                    error_log('Error in direct update: ' . $e->getMessage());
                }
                
                // Update payment using the updateRegistration method which is more flexible
                $updateResult = $this->registrationModel->updateRegistration($updateData);
                error_log('updateRegistration result: ' . ($updateResult ? 'SUCCESS' : 'FAILED'));
                
                // Verify the update worked
                $verifyReg = $this->registrationModel->getRegistrationById($id);
                error_log('After updateRegistration, payment_status is: ' . ($verifyReg ? $verifyReg->payment_status : 'NULL'));
                
                // If the update didn't work, try a direct SQL update as a last resort
                if (!$updateResult || ($verifyReg && $verifyReg->payment_status != $data['payment_status'])) {
                    error_log('Attempting direct SQL update as fallback');
                    try {
                        $db = new Database();
                        // Try a more direct approach with explicit column name
                        $db->query("UPDATE `registrations` SET `payment_status` = :status WHERE `id` = :id");
                        $db->bind(':status', $data['payment_status']);
                        $db->bind(':id', $id);
                        $fallbackResult = $db->execute();
                        error_log('Fallback update result: ' . ($fallbackResult ? 'SUCCESS' : 'FAILED'));
                        
                        // Verify again
                        $db->query("SELECT payment_status FROM registrations WHERE id = :id");
                        $db->bind(':id', $id);
                        $finalCheck = $db->single();
                        error_log('After fallback update, payment_status is: ' . ($finalCheck ? $finalCheck->payment_status : 'NULL'));
                        
                        $updateResult = $fallbackResult;
                    } catch (Exception $e) {
                        error_log('Error in fallback update: ' . $e->getMessage());
                    }
                }
                
                if ($updateResult) {
                    // Set flash message
                    flash('registration_message', 'Payment status updated successfully');
                    // Redirect to registration details
                    $this->redirect('coordinator/viewRegistration/' . $id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Get vehicle images
                $images = $this->vehicleModel->getVehicleImages($registration->vehicle_id);
                
                // Get categories
                $categories = $this->showModel->getShowCategories($registration->show_id);
                
                $viewData = [
                    'title' => 'Registration Details',
                    'registration' => $registration,
                    'show' => $show,
                    'images' => $images,
                    'categories' => $categories,
                    'payment_status_err' => $data['payment_status_err']
                ];
                
                // Load view with errors
                $this->view('coordinator/registrations/view', $viewData);
            }
        } else {
            $this->redirect('coordinator/viewRegistration/' . $id);
        }
    }
    
    /**
     * Delete registration
     * 
     * @param int $id Registration ID
     */
    public function deleteRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete registration
            if ($this->registrationModel->deleteRegistration($id)) {
                // Redirect to registrations page
                $this->redirect('coordinator/registrations/' . $registration->show_id);
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('coordinator/registrations/' . $registration->show_id);
        }
    }
    
    /**
     * Delete registration (URL-friendly alias for deleteRegistration)
     * 
     * @param int $id Registration ID
     */
    public function delete_registration($id) {
        $this->deleteRegistration($id);
    }
    
    /**
     * View registration (URL-friendly alias for viewRegistration)
     * 
     * @param int $id Registration ID
     */
    public function view_registration($id) {
        $this->viewRegistration($id);
    }
    
    /**
     * Update registration (URL-friendly alias for updateRegistration)
     * 
     * @param int $id Registration ID
     */
    public function update_registration($id) {
        $this->updateRegistration($id);
    }
    
    /**
     * Update payment (URL-friendly alias for updatePayment)
     * 
     * @param int $id Registration ID
     */
    public function update_payment($id) {
        $this->updatePayment($id);
    }
    
    /**
     * View judging progress
     * 
     * @param int $showId Show ID
     */
    public function judgingProgress($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get judging progress
        $progress = $this->judgingModel->getJudgingProgress($showId);
        
        $data = [
            'title' => 'Judging Progress',
            'show' => $show,
            'progress' => $progress
        ];
        
        $this->view('coordinator/judging_progress', $data);
    }
    
    /**
     * Calculate results
     * 
     * @param int $showId Show ID
     */
    public function calculateResults($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Calculate results
            if ($this->judgingModel->calculateResults($showId)) {
                // Redirect to results page
                $this->redirect('coordinator/results/' . $showId);
            } else {
                $this->redirect('home/error/Something%20went%20wrong');
            }
        } else {
            $this->redirect('coordinator/show/' . $showId);
        }
    }
    
    /**
     * View results
     * 
     * @param int $showId Show ID
     */
    public function results($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get results
        $results = $this->judgingModel->getShowResults($showId);
        
        // Get fan vote rankings
        $fanVotes = $this->judgingModel->getFanVoteRankings($showId, 10);
        
        $data = [
            'title' => 'Show Results',
            'show' => $show,
            'results' => $results,
            'fan_votes' => $fanVotes
        ];
        
        $this->view('coordinator/results', $data);
    }
    
    /**
     * Edit show
     * 
     * @param int $id Show ID
     */
    public function editShow($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to edit this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Create backup of the original file
        $backupDir = APPROOT . '/autobackup/' . gmdate('Y-m-d');
        if (!file_exists($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        $backupFile = $backupDir . '/coordinator_controller_backup.php';
        if (!file_exists($backupFile)) {
            copy(__FILE__, $backupFile);
        }
        
        // If user is a coordinator (not admin) and listing fee is not paid, redirect to payment page
        if (!$this->auth->hasRole('admin') && !$this->showModel->isListingFeePaid($id)) {
            $this->setFlashMessage('payment_required', 'You must pay the listing fee before you can edit this show.', 'alert alert-warning');
            $this->redirect('payment/showListing/' . $id);
            return;
        }
        
        // Get categories and judging metrics for this show
        $categories = $this->showModel->getShowCategories($id);
        $metrics = $this->showModel->getJudgingMetrics($id);
        
        // Get age weights for this show
        $this->db = new Database();
        $this->judgingModel = $this->model('JudgingModel');
        $ageWeights = $this->judgingModel->getAgeWeightsByShowId($id);
        
        // Load the FormDesignerModel and EntityTemplateManager if available
        try {
            $formDesignerModel = $this->model('FormDesignerModel');
            $entityTemplateManager = $this->model('EntityTemplateManager');
            
            // First check if there's an entity-specific or default template
            $templateId = $entityTemplateManager->getBestTemplateId('show', $id);
            
            // If a template was found, use it
            if ($templateId) {
                $formTemplate = $formDesignerModel->getFormTemplateById($templateId);
                error_log("CoordinatorController::editShow - Using template ID {$templateId} for show {$id}");
            } else {
                // Fall back to the default form template
                $formTemplate = $formDesignerModel->getFormTemplateByTypeAndEntity('show', 0);
                error_log("CoordinatorController::editShow - Using default show template for show {$id}");
            }
            
            // Synchronize form fields with database columns
            try {
                require_once APPROOT . '/models/FormFieldSynchronizer.php';
                $formFieldSynchronizer = new FormFieldSynchronizer();
                $formFieldSynchronizer->synchronizeTemplate($formTemplate);
            } catch (Exception $e) {
                error_log("CoordinatorController::editShow - Error synchronizing template: " . $e->getMessage());
            }
            
            // If no template exists, create one with default fields
            if (!$formTemplate) {
                try {
                    // Get default fields and convert 'id' to 'name' for compatibility
                    $defaultFields = $formDesignerModel->getDefaultFields('show');
                    foreach ($defaultFields as &$field) {
                        if (isset($field['id']) && !isset($field['name'])) {
                            $field['name'] = $field['id'];
                        }
                    }
                    
                    $templateData = [
                        'name' => 'Default Show Form',
                        'type' => 'show',
                        'entity_id' => 0,
                        'fields' => json_encode($defaultFields)
                    ];
                    
                    $templateId = $formDesignerModel->createFormTemplate($templateData);
                    $formTemplate = $formDesignerModel->getFormTemplateById($templateId);
                } catch (Exception $e) {
                    error_log("CoordinatorController::editShow - Error creating default template: " . $e->getMessage());
                }
            }
            
            // Decode the form fields and ensure they have 'name' attribute
            $formFields = $formTemplate ? json_decode($formTemplate->fields, true) : [];
            
            // Check if form fields are valid
            if (!is_array($formFields) || empty($formFields)) {
                // Log the issue
                error_log('Invalid or empty form fields in template ID: ' . ($formTemplate ? $formTemplate->id : 'null'));
                
                try {
                    // If form fields are not valid, create default fields
                    $formFields = $formDesignerModel->getDefaultFields('show');
                    
                    if ($formTemplate) {
                        // Update the template with valid fields
                        $updateData = [
                            'id' => $formTemplate->id,
                            'fields' => json_encode($formFields)
                        ];
                        $formDesignerModel->updateFormTemplate($formTemplate->id, $updateData);
                    }
                } catch (Exception $e) {
                    error_log("CoordinatorController::editShow - Error creating default fields: " . $e->getMessage());
                    $formFields = [];
                }
            }
            
            // Ensure all fields have a name attribute
            if (is_array($formFields)) {
                foreach ($formFields as &$field) {
                    if (isset($field['id']) && !isset($field['name'])) {
                        $field['name'] = $field['id'];
                    }
                }
            }
            
            // Ensure all fields have a name attribute
            if (is_array($formFields)) {
                foreach ($formFields as &$field) {
                    if (isset($field['id']) && !isset($field['name'])) {
                        $field['name'] = $field['id'];
                    }
                }
            }
        } catch (Exception $e) {
            error_log("CoordinatorController::editShow - Error loading form template: " . $e->getMessage());
            $formTemplate = null;
            $formFields = null;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // If user is a coordinator (not admin) and listing fee is not paid, redirect to payment page
            if (!$this->auth->hasRole('admin') && !$this->showModel->isListingFeePaid($id)) {
                $this->setFlashMessage('payment_required', 'You must pay the listing fee before you can update this show.', 'alert alert-warning');
                $this->redirect('payment/showListing/' . $id);
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data - start with standard fields
            $data = [
                'id' => $id,
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'location' => trim($_POST['location']),
                'start_date' => convertUserDateTimeToUTC(trim($_POST['start_date']), $this->auth->getCurrentUserId()),
                'end_date' => convertUserDateTimeToUTC(trim($_POST['end_date']), $this->auth->getCurrentUserId()),
                'registration_start' => convertUserDateTimeToUTC(trim($_POST['registration_start']), $this->auth->getCurrentUserId()),
                'registration_end' => convertUserDateTimeToUTC(trim($_POST['registration_end']), $this->auth->getCurrentUserId()),
                'coordinator_id' => $show->coordinator_id, // Coordinator can't change the coordinator
                'status' => trim($_POST['status']),
                'fan_voting_enabled' => isset($_POST['fan_voting_enabled']) ? (bool)$_POST['fan_voting_enabled'] : true,
                'is_free' => isset($_POST['is_free']) ? (bool)$_POST['is_free'] : false,
                // If is_free is checked, always set registration_fee to 0
                'registration_fee' => (isset($_POST['is_free']) && $_POST['is_free']) ? 0.00 : (isset($_POST['registration_fee']) ? $_POST['registration_fee'] : 0.00),
                'listing_fee' => isset($_POST['listing_fee']) ? floatval($_POST['listing_fee']) : 0.00,
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_start_err' => '',
                'registration_end_err' => '',
                'status_err' => '',
                'title' => 'Edit Show',
                'show' => $show,
                'categories' => $categories,
                'metrics' => $metrics,
                'ageWeights' => $ageWeights
            ];
            
            // Add form fields and template to data if available
            if (isset($formFields) && is_array($formFields)) {
                $data['formFields'] = $formFields;
                $data['formTemplate'] = $formTemplate;
                
                // Get custom fields from the form fields
                foreach ($formFields as $field) {
                    // Skip if field doesn't have an ID or is a system field
                    if (!isset($field['id']) || in_array($field['id'], [
                        'id', 'created_at', 'updated_at', 'name', 'description', 'location', 
                        'start_date', 'end_date', 'registration_start', 'registration_end', 
                        'coordinator_id', 'status', 'fan_voting_enabled', 'registration_fee', 
                        'is_free', 'featured_image_id'
                    ])) {
                        continue;
                    }
                    
                    // Skip section dividers and HTML fields (they don't need database columns)
                    if (isset($field['type']) && in_array($field['type'], ['section', 'html'])) {
                        continue;
                    }
                    
                    // Add the field to the data array
                    $fieldId = $field['id'];
                    if (isset($_POST[$fieldId])) {
                        // Handle different field types
                        switch ($field['type']) {
                            case 'checkbox':
                                $data[$fieldId] = isset($_POST[$fieldId]) ? true : false;
                                break;
                            case 'number':
                                $data[$fieldId] = is_numeric($_POST[$fieldId]) ? $_POST[$fieldId] : 0;
                                break;
                            default:
                                $data[$fieldId] = trim($_POST[$fieldId]);
                        }
                    } else {
                        // Set default value if field is not in POST data
                        $data[$fieldId] = isset($field['default']) ? $field['default'] : null;
                    }
                }
                
                // Process any fields that might be in POST but not in the form data
                foreach ($_POST as $key => $value) {
                    // Skip special fields and fields already in data
                    if (strpos($key, '_') === 0 || isset($data[$key])) {
                        continue;
                    }
                    
                    // If this is a form field (starts with 'field_'), add it to the data
                    if (strpos($key, 'field_') === 0) {
                        $data[$key] = $value;
                    }
                }
                
                // Check for hidden marker fields
                foreach ($_POST as $key => $value) {
                    if (strpos($key, '_has_') === 0) {
                        $fieldName = str_replace('_has_', '', $key);
                        if (!isset($data[$fieldName])) {
                            $data[$fieldName] = '';
                        }
                    }
                }
            }
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate location
            if (empty($data['location'])) {
                $data['location_err'] = 'Please enter a location';
            }
            
            // Validate dates
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif (!empty($data['start_date']) && !empty($data['end_date'])) {
                // Use proper DateTime comparison instead of string comparison
                try {
                    $startDateTime = new DateTime($data['start_date']);
                    $endDateTime = new DateTime($data['end_date']);
                    
                    if ($endDateTime <= $startDateTime) {
                        $data['end_date_err'] = 'End date must be after start date';
                    }
                } catch (Exception $e) {
                    // Fallback to string comparison if DateTime fails
                    if ($data['end_date'] <= $data['start_date']) {
                        $data['end_date_err'] = 'End date must be after start date';
                    }
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("CoordinatorController::editShow - DateTime comparison error: " . $e->getMessage());
                    }
                }
            }
            
            if (empty($data['registration_start'])) {
                $data['registration_start_err'] = 'Please enter a registration start date';
            }
            
            if (empty($data['registration_end'])) {
                $data['registration_end_err'] = 'Please enter a registration end date';
            } elseif (!empty($data['registration_start']) && !empty($data['registration_end'])) {
                // Use proper DateTime comparison for registration dates
                try {
                    $regStartDateTime = new DateTime($data['registration_start']);
                    $regEndDateTime = new DateTime($data['registration_end']);
                    
                    if ($regEndDateTime <= $regStartDateTime) {
                        $data['registration_end_err'] = 'Registration end date must be after registration start date';
                    }
                } catch (Exception $e) {
                    // Fallback to string comparison if DateTime fails
                    if ($data['registration_end'] <= $data['registration_start']) {
                        $data['registration_end_err'] = 'Registration end date must be after registration start date';
                    }
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("CoordinatorController::editShow - Registration DateTime comparison error: " . $e->getMessage());
                    }
                }
                
                // Check if registration ends before show starts
                if (empty($data['registration_end_err']) && !empty($data['start_date'])) {
                    try {
                        $regEndDateTime = new DateTime($data['registration_end']);
                        $showStartDateTime = new DateTime($data['start_date']);
                        
                        if ($regEndDateTime > $showStartDateTime) {
                            $data['registration_end_err'] = 'Registration must end before the show starts';
                        }
                    } catch (Exception $e) {
                        // Fallback to string comparison if DateTime fails
                        if ($data['registration_end'] > $data['start_date']) {
                            $data['registration_end_err'] = 'Registration must end before the show starts';
                        }
                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log("CoordinatorController::editShow - Registration vs Show DateTime comparison error: " . $e->getMessage());
                        }
                    }
                }
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select a status';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['location_err']) && 
                empty($data['start_date_err']) && empty($data['end_date_err']) && 
                empty($data['registration_start_err']) && empty($data['registration_end_err']) && 
                empty($data['status_err'])) {
                
                // Update show
                if ($this->showModel->updateShow($data)) {
                    // Set flash message
                    $this->setFlashMessage('show_updated', 'Show updated successfully');
                    $this->redirect('coordinator/show/' . $id);
                    return;
                } else {
                    $this->setFlashMessage('show_error', 'Something went wrong', 'alert alert-danger');
                }
            }
            
            // Load view with errors
            $this->view('coordinator/edit_show', $data);
        } else {
            // Try to load the CustomFieldRetriever to map database columns to form fields
            try {
                require_once APPROOT . '/models/CustomFieldRetriever.php';
                $customFieldRetriever = new CustomFieldRetriever();
                
                // Map database columns to form fields
                $mappedData = $customFieldRetriever->mapDatabaseToForm($show);
            } catch (Exception $e) {
                error_log("CoordinatorController::editShow - Error loading CustomFieldRetriever: " . $e->getMessage());
                $mappedData = [];
            }
            
            // Convert UTC dates to user timezone for display in datetime-local format
            $userStartDate = convertUTCToUserDateTime($show->start_date, $this->auth->getCurrentUserId(), 'Y-m-d\TH:i');
            $userEndDate = convertUTCToUserDateTime($show->end_date, $this->auth->getCurrentUserId(), 'Y-m-d\TH:i');
            $userRegStart = convertUTCToUserDateTime($show->registration_start, $this->auth->getCurrentUserId(), 'Y-m-d\TH:i');
            $userRegEnd = convertUTCToUserDateTime($show->registration_end, $this->auth->getCurrentUserId(), 'Y-m-d\TH:i');

            // Convert custom date/time fields from form template
            $customDateTimeFields = $this->getCustomDateTimeFields($formFields);
            $convertedCustomFields = [];
            foreach ($customDateTimeFields as $fieldName => $fieldType) {
                if (isset($show->$fieldName) && !empty($show->$fieldName)) {
                    $format = ($fieldType === 'date') ? 'Y-m-d' :
                             (($fieldType === 'time') ? 'H:i' : 'Y-m-d\TH:i');
                    $convertedCustomFields[$fieldName] = convertUTCToUserDateTime($show->$fieldName, $this->auth->getCurrentUserId(), $format);

                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("CoordinatorController::editShow - Custom field timezone conversion: {$fieldName} ({$fieldType})");
                        error_log("  UTC: {$show->$fieldName} -> User: {$convertedCustomFields[$fieldName]}");
                    }
                }
            }

            // Extract all properties from the show object (excluding standard date fields)
            $showData = [];
            foreach ($show as $key => $value) {
                // Skip standard date fields that need timezone conversion - they're handled separately
                if (!in_array($key, ['start_date', 'end_date', 'registration_start', 'registration_end'])) {
                    $showData[$key] = $value;
                }
                // Log all show data for debugging
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("CoordinatorController::editShow - Show data: {$key} = " . (is_string($value) ? $value : var_export($value, true)));
                }
            }

            // Debug timezone conversion
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CoordinatorController::editShow - Timezone conversion for show {$show->id}:");
                error_log("  UTC start_date: {$show->start_date} -> User: {$userStartDate}");
                error_log("  UTC end_date: {$show->end_date} -> User: {$userEndDate}");
                error_log("  UTC reg_start: {$show->registration_start} -> User: {$userRegStart}");
                error_log("  UTC reg_end: {$show->registration_end} -> User: {$userRegEnd}");
                error_log("  Custom date/time fields converted: " . count($convertedCustomFields));
            }

            // Merge with basic data, but ensure timezone-converted dates are preserved
            $baseData = [
                'id' => $id,
                'name' => $show->name,
                'description' => $show->description,
                'location' => $show->location,
                'start_date' => $userStartDate,
                'end_date' => $userEndDate,
                'registration_start' => $userRegStart,
                'registration_end' => $userRegEnd,
                'coordinator_id' => $show->coordinator_id,
                'status' => $show->status,
                'fan_voting_enabled' => isset($show->fan_voting_enabled) ? $show->fan_voting_enabled : true,
                'registration_fee' => isset($show->registration_fee) ? $show->registration_fee : 0,
                'is_free' => isset($show->is_free) ? $show->is_free : 0,
                'listing_fee' => isset($show->listing_fee) ? $show->listing_fee : 0,
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_start_err' => '',
                'registration_end_err' => '',
                'status_err' => '',
                'title' => 'Edit Show',
                'show' => $show,
                'categories' => $categories,
                'metrics' => $metrics,
                'ageWeights' => $ageWeights,
                'formFields' => $formFields,
                'formTemplate' => $formTemplate
            ];

            // First merge the base data with mapped data
            $data = array_merge($baseData, $mappedData);

            // Then merge with show data, but preserve the timezone-converted date fields
            $data = array_merge($showData, $data);

            // Ensure timezone-converted dates are preserved by explicitly setting them again
            $data['start_date'] = $userStartDate;
            $data['end_date'] = $userEndDate;
            $data['registration_start'] = $userRegStart;
            $data['registration_end'] = $userRegEnd;

            // Apply converted custom date/time fields to the data array
            foreach ($convertedCustomFields as $fieldName => $convertedValue) {
                $data[$fieldName] = $convertedValue;
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("CoordinatorController::editShow - Applied converted custom field: {$fieldName} = {$convertedValue}");
                }
            }

            // Debug the final data array
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CoordinatorController::editShow - Final data array keys: " . implode(', ', array_keys($data)));
                error_log("CoordinatorController::editShow - Final timezone values being sent to view:");
                error_log("  start_date: " . $data['start_date']);
                error_log("  end_date: " . $data['end_date']);
                error_log("  registration_start: " . $data['registration_start']);
                error_log("  registration_end: " . $data['registration_end']);
                foreach ($convertedCustomFields as $fieldName => $convertedValue) {
                    error_log("  {$fieldName}: {$convertedValue}");
                }
            }
            
            $this->view('coordinator/edit_show', $data);
        }
    }
    
    /**
     * Fan votes monitoring
     * 
     * @param int $showId Show ID
     * @return void
     */
    public function fanVotes($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('coordinator/dashboard');
            return;
        }
        
        // Check if this coordinator is assigned to this show or if user is an admin
        if ($show->coordinator_id != $_SESSION['user_id'] && !$this->auth->hasRole('admin')) {
            $this->redirect('coordinator/dashboard');
            return;
        }
        
        // Get all votes for this show
        $votes = $this->judgingModel->getShowVotes($showId);
        
        // Get vote rankings
        $rankings = $this->judgingModel->getFanVoteRankings($showId);
        
        // Calculate statistics
        $uniqueIps = [];
        $facebookVotes = 0;
        $vehiclesWithVotes = [];
        
        foreach ($votes as $vote) {
            // Count unique IPs
            if (!in_array($vote->voter_ip, $uniqueIps)) {
                $uniqueIps[] = $vote->voter_ip;
            }
            
            // Count Facebook votes
            if (!empty($vote->fb_user_id)) {
                $facebookVotes++;
            }
            
            // Count vehicles with votes
            if (!in_array($vote->registration_id, $vehiclesWithVotes)) {
                $vehiclesWithVotes[] = $vote->registration_id;
            }
        }
        
        $data = [
            'title' => 'Fan Votes - ' . $show->name,
            'show' => $show,
            'votes' => $votes,
            'rankings' => $rankings,
            'unique_ips' => count($uniqueIps),
            'facebook_votes' => $facebookVotes,
            'vehicles_with_votes' => count($vehiclesWithVotes)
        ];
        
        $this->view('coordinator/fan_votes', $data);
    }
    
    /**
     * Generate QR codes for fan voting
     * 
     * @param int $showId Show ID
     * @return void
     */
    public function qrCodes($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('coordinator/dashboard');
            return;
        }
        
        // Check if this coordinator is assigned to this show or if user is an admin
        if ($show->coordinator_id != $_SESSION['user_id'] && !$this->auth->hasRole('admin')) {
            $this->redirect('coordinator/dashboard');
            return;
        }
        
        // Check if fan voting is enabled for this show
        if (!$show->fan_voting_enabled) {
            flash('coordinator_message', 'Fan voting is not enabled for this show', 'alert alert-warning');
            $this->redirect('coordinator/dashboard');
            return;
        }
        
        // Get all approved vehicles for this show
        $vehicles = $this->vehicleModel->getShowVehiclesWithImages($showId);
        
        $data = [
            'title' => 'Registration Cards - ' . $show->name,
            'show' => $show,
            'vehicles' => $vehicles
        ];
        
        $this->view('coordinator/qr_codes', $data);
    }

    // ============================================================================
    // SECTION 10: STAFF MANAGEMENT
    // ============================================================================

    // ============================================================================
    // SECTION 11: QR CODE MANAGEMENT
    // ============================================================================

    /**
     * QR Code Management
     *
     * @return void
     */
    public function manageQrCodes() {
        // Get all active shows assigned to this coordinator
        $shows = $this->showModel->getCoordinatorActiveShows($_SESSION['user_id']);
        
        // Default data
        $data = [
            'title' => 'Registration Card Management',
            'shows' => $shows,
            'selected_show' => null,
            'vehicles' => [],
            'error' => null
        ];
        
        // Check if a show was selected
        if (isset($_GET['show_id']) && !empty($_GET['show_id'])) {
            $showId = (int)$_GET['show_id'];
            $show = $this->showModel->getShowById($showId);
            
            // Verify this coordinator is assigned to this show
            if ($show && $show->coordinator_id == $_SESSION['user_id']) {
                $data['selected_show'] = $show;
                
                // Get all registrations for this show with payment status
                $registrations = $this->registrationModel->getShowRegistrations($showId);
                
                // Filter to only include registrations with completed payment
                $paidRegistrations = array_filter($registrations, function($reg) {
                    return isset($reg->payment_status) && $reg->payment_status === 'completed';
                });
                
                $data['vehicles'] = $paidRegistrations;
            } else {
                $data['error'] = 'Show not found or you do not have permission to access it';
            }
        }
        
        $this->view('coordinator/manage_qr_codes', $data);
    }
    
    /**
     * Download QR code for a vehicle registration
     * 
     * @param int $registrationId Registration ID
     * @return void
     */
    public function downloadQrCode($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('coordinator/manageQrCodes');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        if (!$show) {
            $this->redirect('coordinator/manageQrCodes');
            return;
        }
        
        // Verify this coordinator is assigned to this show
        if ($show->coordinator_id != $_SESSION['user_id']) {
            $this->redirect('coordinator/manageQrCodes');
            return;
        }
        
        // Get vehicle
        $vehicle = $this->vehicleModel->getVehicleById($registration->vehicle_id);
        
        if (!$vehicle) {
            $this->redirect('coordinator/manageQrCodes');
            return;
        }
        
        // Generate QR code URL with registration number as security token
        $registrationNumber = !empty($registration->registration_number) ? 
            $registration->registration_number : 
            'RER-' . str_pad($registration->id, 10, '0', STR_PAD_LEFT);
            
        // Get or generate display number with category prefix
        $displayNumber = !empty($registration->display_number) ? 
            $registration->display_number : 
            $this->generateDisplayNumber($registration->id, $registration->category_id);
            
        $qrUrl = URLROOT . '/show/vote/' . $registration->show_id . '/' . $registration->id;
        
        // Set headers for download
        header('Content-Type: image/png');
        header('Content-Disposition: attachment; filename="qr_' . $show->id . '_' . $registration->id . '.png"');
        
        // Generate and output QR code
        $qrImage = file_get_contents('https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' . urlencode($qrUrl));
        echo $qrImage;
        exit;
    }
    
    /**
     * Lookup QR code by registration number
     * 
     * @return void
     */
    public function lookupQrCode() {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('coordinator/manageQrCodes');
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Sanitize POST data
        $_POST = $this->sanitizeInput($_POST);
        
        // Get form data
        $registrationNumber = trim($_POST['registration_number']);
        $showId = !empty($_POST['lookup_show_id']) ? (int)$_POST['lookup_show_id'] : null;
        
        // Validate registration number
        if (empty($registrationNumber)) {
            flash('coordinator_message', 'Please enter a registration number', 'alert alert-danger');
            $this->redirect('coordinator/manageQrCodes');
            return;
        }
        
        // Find registration
        $registration = null;
        
        if ($showId) {
            // Look for registration in specific show
            $this->db->query('SELECT r.* FROM registrations r 
                             JOIN shows s ON r.show_id = s.id
                             WHERE r.registration_number = :reg_number 
                             AND r.show_id = :show_id
                             AND s.coordinator_id = :coordinator_id');
            $this->db->bind(':reg_number', $registrationNumber);
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':coordinator_id', $_SESSION['user_id']);
            $registration = $this->db->single();
        } else {
            // Look for registration in any show assigned to this coordinator
            $this->db->query('SELECT r.* FROM registrations r 
                             JOIN shows s ON r.show_id = s.id
                             WHERE r.registration_number = :reg_number
                             AND s.coordinator_id = :coordinator_id');
            $this->db->bind(':reg_number', $registrationNumber);
            $this->db->bind(':coordinator_id', $_SESSION['user_id']);
            $registration = $this->db->single();
        }
        
        if (!$registration) {
            flash('coordinator_message', 'Registration not found or you do not have permission to access it', 'alert alert-danger');
            $this->redirect('coordinator/manageQrCodes');
            return;
        }
        
        // Redirect to print QR code
        $this->redirect('coordinator/printQrCodes/' . $registration->show_id . '?vehicle_id=' . $registration->id);
    }
    
    /**
     * Print QR codes for fan voting
     * 
     * @param int $showId Show ID
     * @return void
     */
    public function printQrCodes($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('coordinator/dashboard');
            return;
        }
        
        // Check if this coordinator is assigned to this show or if user is an admin
        if ($show->coordinator_id != $_SESSION['user_id'] && !$this->auth->hasRole('admin')) {
            $this->redirect('coordinator/dashboard');
            return;
        }
        
        // Check if a specific vehicle was requested
        $vehicleId = isset($_GET['vehicle_id']) ? (int)$_GET['vehicle_id'] : null;
        
        // Get vehicles for this show
        if ($vehicleId) {
            // Get specific registration
            $registration = $this->registrationModel->getRegistrationById($vehicleId);
            
            if (!$registration || $registration->show_id != $showId) {
                flash('coordinator_message', 'Vehicle not found for this show', 'alert alert-warning');
                $this->redirect('coordinator/manageQrCodes?show_id=' . $showId);
                return;
            }
            
            // Get vehicle details
            $this->db->query('SELECT v.*, r.id as registration_id, r.registration_number, r.status, 
                            sc.name as category_name, sc.id as category_id, 
                            u.name as owner_name
                            FROM vehicles v 
                            JOIN registrations r ON v.id = r.vehicle_id 
                            JOIN show_categories sc ON r.category_id = sc.id 
                            JOIN users u ON v.owner_id = u.id 
                            WHERE r.id = :registration_id');
            $this->db->bind(':registration_id', $vehicleId);
            $vehicle = $this->db->single();
            
            if (!$vehicle) {
                flash('coordinator_message', 'Vehicle not found', 'alert alert-warning');
                $this->redirect('coordinator/manageQrCodes?show_id=' . $showId);
                return;
            }
            
            // Get images for the vehicle
            $this->db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id ORDER BY is_primary DESC, id');
            $this->db->bind(':entity_type', 'vehicle');
            $this->db->bind(':entity_id', $vehicle->id);
            $vehicle->images = $this->db->resultSet();
            
            $vehicles = [$vehicle];
        } else {
            // Get all approved vehicles for this show
            $vehicles = $this->vehicleModel->getShowVehiclesWithImages($showId);
        }
        
        $data = [
            'title' => 'Print QR Codes - ' . $show->name,
            'show' => $show,
            'vehicles' => $vehicles,
            'single_vehicle' => $vehicleId ? true : false
        ];
        
        $this->view('shared/print_qr_codes', $data);
    }
    
    /**
     * Export fan votes to CSV
     * 
     * @param int $showId Show ID
     * @return void
     */
    public function exportFanVotes($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('coordinator/dashboard');
            return;
        }
        
        // Check if this coordinator is assigned to this show or if user is an admin
        if ($show->coordinator_id != $_SESSION['user_id'] && !$this->auth->hasRole('admin')) {
            $this->redirect('coordinator/dashboard');
            return;
        }
        
        // Get all votes for this show
        $votes = $this->judgingModel->getShowVotes($showId);
        
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="fan_votes_' . $showId . '_' . gmdate('Y-m-d') . '.csv"');
        
        // Create output stream
        $output = fopen('php://output', 'w');
        
        // Add CSV headers
        fputcsv($output, ['ID', 'IP Address', 'Facebook User ID', 'Facebook User Name', 'Facebook User Email', 
                         'Registration ID', 'Registration Number', 'Vehicle', 'Owner', 'Category', 'Date/Time']);
        
        // Add data rows
        foreach ($votes as $vote) {
            fputcsv($output, [
                $vote->id,
                $vote->voter_ip,
                $vote->fb_user_id ?? '',
                $vote->fb_user_name ?? '',
                $vote->fb_user_email ?? '',
                $vote->registration_id,
                $vote->registration_number,
                $vote->year . ' ' . $vote->make . ' ' . $vote->model,
                $vote->owner_name,
                $vote->category_name,
                $vote->created_at
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Show list of shows for registration
     * 
     * @return void
     */
    public function registerVehicle() {
        // Get filter status from query parameters
        $status = isset($_GET['status']) ? $_GET['status'] : null;
        $sort = isset($_GET['sort']) ? $_GET['sort'] : null;
        $order = isset($_GET['order']) ? $_GET['order'] : 'asc';
        
        // Get shows for this coordinator excluding completed and cancelled by default
        $userId = $_SESSION['user_id'];
        $shows = $this->showModel->getShowsByCoordinator($userId, null, null, $status, true);
        
        // Apply sorting if specified
        if ($sort) {
            usort($shows, function($a, $b) use ($sort, $order) {
                $aVal = $a->$sort;
                $bVal = $b->$sort;
                
                // Handle date fields
                if (in_array($sort, ['start_date', 'end_date', 'registration_start', 'registration_end'])) {
                    $aVal = strtotime($aVal);
                    $bVal = strtotime($bVal);
                }
                
                if ($order === 'asc') {
                    return $aVal <=> $bVal;
                } else {
                    return $bVal <=> $aVal;
                }
            });
        }
        
        $data = [
            'title' => 'Register Vehicle for User',
            'shows' => $shows,
            'current_status' => $status,
            'current_sort' => $sort,
            'current_order' => $order
        ];
        
        $this->view('coordinator/register_vehicle', $data);
    }
    
    /**
     * Register a vehicle for a user after registration end date
     * 
     * @param int $showId Show ID
     * @return void
     */
    public function registerVehicleForUser($showId = 0) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            flash('coordinator_message', 'Show not found', 'alert alert-danger');
            $this->redirect('coordinator/dashboard');
            return;
        }
        
        // Check if this coordinator is assigned to this show
        if ($show->coordinator_id != $_SESSION['user_id']) {
            flash('coordinator_message', 'You are not authorized to manage this show', 'alert alert-danger');
            $this->redirect('coordinator/dashboard');
            return;
        }
        
        // Get all users
        $users = $this->userModel->getUsers();
        
        // Get show categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get payment methods
        $paymentModel = $this->model('PaymentModel');
        $paymentMethods = $paymentModel->getPaymentMethods();
        
        $data = [
            'title' => 'Register Vehicle for User - ' . $show->name,
            'show' => $show,
            'users' => $users,
            'categories' => $categories,
            'payment_methods' => $paymentMethods,
            'user_id' => '',
            'vehicle_id' => '',
            'category_id' => '',
            'payment_method_id' => '',
            'fee' => $show->registration_fee ?? 0,
            'status' => 'approved', // Default to approved for coordinator registrations
            'user_id_err' => '',
            'vehicle_id_err' => '',
            'category_id_err' => '',
            'payment_method_id_err' => ''
        ];
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Get form data
            $data['user_id'] = $_POST['user_id'] ?? '';
            $data['vehicle_id'] = $_POST['vehicle_id'] ?? '';
            $data['category_id'] = $_POST['category_id'] ?? '';
            $data['payment_method_id'] = $_POST['payment_method_id'] ?? '';
            $data['fee'] = $_POST['fee'] ?? $show->registration_fee ?? 0;
            $data['status'] = $_POST['status'] ?? 'approved';
            
            // Validate user
            if (empty($data['user_id'])) {
                $data['user_id_err'] = 'Please select a user';
            }
            
            // Validate vehicle if provided
            if (empty($data['vehicle_id']) && $data['vehicle_id'] !== 'new') {
                $data['vehicle_id_err'] = 'Please select a vehicle or choose to create a new one';
            }
            
            // Validate category
            if (empty($data['category_id'])) {
                $data['category_id_err'] = 'Please select a category';
            }
            
            // Validate payment method
            if (empty($data['payment_method_id'])) {
                $data['payment_method_id_err'] = 'Please select a payment method';
            }
            
            // Check for errors
            if (empty($data['user_id_err']) && empty($data['vehicle_id_err']) && 
                empty($data['category_id_err']) && empty($data['payment_method_id_err'])) {
                
                // If creating a new vehicle
                if ($data['vehicle_id'] === 'new') {
                    // Create new vehicle
                    $vehicleData = [
                        'owner_id' => $data['user_id'],
                        'make' => $_POST['make'] ?? '',
                        'model' => $_POST['model'] ?? '',
                        'year' => $_POST['year'] ?? '',
                        'color' => $_POST['color'] ?? '',
                        'license_plate' => $_POST['license_plate'] ?? '',
                        'vin' => $_POST['vin'] ?? '',
                        'description' => $_POST['description'] ?? ''
                    ];
                    
                    // Validate vehicle data
                    if (empty($vehicleData['make'])) {
                        $data['vehicle_id_err'] = 'Please enter a make';
                    } elseif (empty($vehicleData['model'])) {
                        $data['vehicle_id_err'] = 'Please enter a model';
                    } elseif (empty($vehicleData['year'])) {
                        $data['vehicle_id_err'] = 'Please enter a year';
                    }
                    
                    if (empty($data['vehicle_id_err'])) {
                        // Create vehicle
                        $vehicleId = $this->vehicleModel->createVehicle($vehicleData);
                        
                        if ($vehicleId) {
                            $data['vehicle_id'] = $vehicleId;
                        } else {
                            $data['vehicle_id_err'] = 'Error creating vehicle';
                        }
                    }
                }
                
                // If no errors, create registration
                if (empty($data['vehicle_id_err'])) {
                    $registrationData = [
                        'show_id' => $showId,
                        'user_id' => $data['user_id'],
                        'vehicle_id' => $data['vehicle_id'],
                        'category_id' => $data['category_id'],
                        'status' => $data['status'],
                        'fee' => $data['fee'],
                        'payment_method_id' => $data['payment_method_id'],
                        'payment_status' => 'completed', // Default to completed for coordinator registrations
                        'payment_reference' => 'Coordinator registration'
                    ];
                    
                    // Create registration
                    $registrationId = $this->registrationModel->createRegistration($registrationData);
                    
                    if ($registrationId) {
                        flash('coordinator_message', 'Vehicle registered successfully', 'alert alert-success');
                        $this->redirect('coordinator/show/' . $showId);
                    } else {
                        flash('coordinator_message', 'Error registering vehicle', 'alert alert-danger');
                    }
                }
            }
        }
        
        // Load view
        $this->view('coordinator/register_vehicle_for_user', $data);
    }
    
    /**
     * Get user vehicles via AJAX
     * 
     * @return void
     */
    public function getUserVehicles() {
        // Check if request is AJAX
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
            http_response_code(403);
            echo json_encode(['error' => 'Forbidden']);
            return;
        }
        
        // Get user ID from POST
        $userId = $_POST['user_id'] ?? 0;
        
        if (empty($userId)) {
            http_response_code(400);
            echo json_encode(['error' => 'User ID is required']);
            return;
        }
        
        // Get user vehicles
        $vehicles = $this->vehicleModel->getUserVehicles($userId);
        
        // Return vehicles as JSON
        header('Content-Type: application/json');
        echo json_encode(['vehicles' => $vehicles]);
    }
    
    /**
     * Check in a vehicle registration
     * 
     * @param int $id Registration ID
     * @return void
     */
    public function checkIn($id) {
        // Verify CSRF token
        if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if already checked in
        if ($registration->checked_in) {
            $this->redirect('coordinator/viewRegistration/' . $id);
            return;
        }
        
        // Verify coordinator has access to this show
        $show = $this->showModel->getShowById($registration->show_id);
        if (!$show || (!$this->auth->hasRole('admin') && $show->coordinator_id != $_SESSION['user_id'])) {
            $this->redirect('home/error/You%20do%20not%20have%20permission%20to%20check%20in%20this%20vehicle');
            return;
        }
        
        // Update check-in status
        $data = [
            'id' => $id,
            'checked_in' => 1,
            'check_in_time' => gmdate('Y-m-d H:i:s')
        ];
        
        // Update registration
        if ($this->registrationModel->updateRegistration($id, $data)) {
            // Log the check-in
            error_log('Coordinator checked in registration #' . $id . ' for ' . $registration->year . ' ' . 
                      $registration->make . ' ' . $registration->model);
            
            // Set success message
            $_SESSION['success_message'] = 'Vehicle successfully checked in';
        } else {
            // Set error message
            $_SESSION['error_message'] = 'Failed to check in vehicle';
        }
        
        // Redirect back to registration details
        $this->redirect('coordinator/viewRegistration/' . $id);
    }
    
    /**
     * Undo check-in for a vehicle registration
     * 
     * @param int $id Registration ID
     * @return void
     */
    public function undoCheckIn($id) {
        // Verify CSRF token
        if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if not checked in
        if (!$registration->checked_in) {
            $this->redirect('coordinator/viewRegistration/' . $id);
            return;
        }
        
        // Verify coordinator has access to this show
        $show = $this->showModel->getShowById($registration->show_id);
        if (!$show || (!$this->auth->hasRole('admin') && $show->coordinator_id != $_SESSION['user_id'])) {
            $this->redirect('home/error/You%20do%20not%20have%20permission%20to%20undo%20check-in%20for%20this%20vehicle');
            return;
        }
        
        // Update check-in status
        $data = [
            'id' => $id,
            'checked_in' => 0,
            'check_in_time' => null
        ];
        
        // Update registration
        if ($this->registrationModel->updateRegistration($id, $data)) {
            // Log the action
            error_log('Coordinator undid check-in for registration #' . $id . ' for ' . $registration->year . ' ' . 
                      $registration->make . ' ' . $registration->model);
            
            // Set success message
            $_SESSION['success_message'] = 'Check-in has been undone';
        } else {
            // Set error message
            $_SESSION['error_message'] = 'Failed to undo check-in';
        }
        
        // Redirect back to registration details
        $this->redirect('coordinator/viewRegistration/' . $id);
    }
    
    /**
     * Generate display number with category prefix
     * 
     * @param int $registrationId Registration ID
     * @param int $categoryId Category ID
     * @return string Display number with category prefix
     */
    private function generateDisplayNumber($registrationId, $categoryId) {
        // Get category name
        $this->db->query('SELECT name FROM show_categories WHERE id = :id');
        $this->db->bind(':id', $categoryId);
        $category = $this->db->single();
        
        if (!$category) {
            return 'X-' . $registrationId; // Default prefix if category not found
        }
        
        // Clean the category name - remove parentheses and special characters
        $cleanName = preg_replace('/\([^)]*\)/', '', $category->name); // Remove content in parentheses
        $cleanName = trim(preg_replace('/[^a-zA-Z0-9\s]/', '', $cleanName)); // Remove special characters
        
        // Get words from the cleaned name
        $words = explode(' ', $cleanName);
        $words = array_filter($words); // Remove empty elements
        $prefix = '';
        
        // Handle different category naming patterns
        if (count($words) == 0) {
            // If no valid words after cleaning, use first two letters of original name
            $prefix = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $category->name), 0, 2));
        }
        else if (count($words) == 1) {
            // For single words, use first two letters
            $prefix = strtoupper(substr($words[array_key_first($words)], 0, 2));
        }
        else if (count($words) == 2) {
            // For two words, use first letter of each
            $prefix = strtoupper(
                substr($words[array_key_first($words)], 0, 1) . 
                substr($words[array_key_first($words) + 1], 0, 1)
            );
        }
        else {
            // For multiple words, use a more distinctive approach
            // If first word is short (like "The", "A", etc.), use first letter of first word and first letter of second word
            if (strlen($words[array_key_first($words)]) <= 3) {
                $prefix = strtoupper(
                    substr($words[array_key_first($words)], 0, 1) . 
                    substr($words[array_key_first($words) + 1], 0, 1)
                );
            } 
            // Otherwise use first letter of first word and first letter of last significant word
            else {
                $prefix = strtoupper(
                    substr($words[array_key_first($words)], 0, 1) . 
                    substr($words[array_key_last($words)], 0, 1)
                );
            }
        }
        
        // Special case handling for common categories
        $lowerName = strtolower($category->name);
        if (strpos($lowerName, 'classic') !== false) {
            $prefix = 'CL';
        } else if (strpos($lowerName, 'muscle') !== false) {
            $prefix = 'MU';
        } else if (strpos($lowerName, 'truck') !== false || strpos($lowerName, 'suv') !== false) {
            $prefix = 'TR';
        } else if (strpos($lowerName, 'motorcycle') !== false || strpos($lowerName, 'bike') !== false) {
            $prefix = 'MC';
        } else if (strpos($lowerName, 'import') !== false && strpos($lowerName, 'asian') !== false) {
            $prefix = 'AI';
        } else if (strpos($lowerName, 'import') !== false && strpos($lowerName, 'european') !== false) {
            $prefix = 'EI';
        } else if (strpos($lowerName, 'modern') !== false) {
            $prefix = 'MO';
        } else if (strpos($lowerName, 'custom') !== false || strpos($lowerName, 'modified') !== false) {
            $prefix = 'CM';
        } else if (strpos($lowerName, 'contemporary') !== false) {
            $prefix = 'CT';
        } else if (strpos($lowerName, 'antique') !== false) {
            $prefix = 'AN';
        } else if (strpos($lowerName, 'vintage') !== false) {
            $prefix = 'VN';
        } else if (strpos($lowerName, 'sport') !== false) {
            $prefix = 'SP';
        } else if (strpos($lowerName, 'luxury') !== false) {
            $prefix = 'LX';
        } else if (strpos($lowerName, 'electric') !== false || strpos($lowerName, 'ev') !== false) {
            $prefix = 'EV';
        } else if (strpos($lowerName, 'hybrid') !== false) {
            $prefix = 'HY';
        } else if (strpos($lowerName, 'convertible') !== false) {
            $prefix = 'CV';
        } else if (strpos($lowerName, 'coupe') !== false) {
            $prefix = 'CP';
        } else if (strpos($lowerName, 'sedan') !== false) {
            $prefix = 'SD';
        } else if (strpos($lowerName, 'race') !== false || strpos($lowerName, 'racing') !== false) {
            $prefix = 'RC';
        } else if (strpos($lowerName, 'off') !== false && strpos($lowerName, 'road') !== false) {
            $prefix = 'OR';
        }
        
        // For custom categories, ensure we have a meaningful 2-letter prefix
        if (strlen($prefix) != 2) {
            // If we have a single letter, add the next most significant letter
            if (strlen($prefix) == 1) {
                // Try to find a second significant letter
                if (count($words) > 1) {
                    // Use the first letter of the second word
                    $prefix .= strtoupper(substr($words[array_key_first($words) + 1], 0, 1));
                } else if (strlen($words[array_key_first($words)]) > 1) {
                    // Use the second letter of the first word
                    $prefix .= strtoupper(substr($words[array_key_first($words)], 1, 1));
                } else {
                    // Fallback: add X
                    $prefix .= 'X';
                }
            } else if (strlen($prefix) > 2) {
                // If we have more than 2 letters, truncate to 2
                $prefix = substr($prefix, 0, 2);
            } else {
                // If we somehow have no prefix, use the first two letters of the category name
                // or the first letter + X if the name is only one character
                $categoryName = preg_replace('/[^a-zA-Z0-9]/', '', $category->name);
                if (strlen($categoryName) > 1) {
                    $prefix = strtoupper(substr($categoryName, 0, 2));
                } else if (strlen($categoryName) == 1) {
                    $prefix = strtoupper($categoryName) . 'X';
                } else {
                    $prefix = 'XX'; // Last resort fallback
                }
            }
        }
        
        return $prefix . '-' . $registrationId;
    }
    
    /**
     * Manage staff for a show
     * 
     * @param int $showId Show ID
     */
    public function manageStaff($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage staff for this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get staff model
        $staffModel = $this->model('StaffModel');
        
        // Get all staff assigned to this show
        $assignedStaff = $staffModel->getShowStaff($showId);
        
        // Get all available staff (not yet assigned to this show)
        $availableStaff = $staffModel->getAvailableStaff($showId);
        
        $data = [
            'title' => $show->name . ' - Manage Staff',
            'show' => $show,
            'assigned_staff' => $assignedStaff,
            'available_staff' => $availableStaff
        ];
        
        $this->view('coordinator/shows/manage_staff', $data);
    }
    
    /**
     * Assign staff to a show
     * 
     * @param int $showId Show ID
     */
    public function assignStaff($showId) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('coordinator/manageStaff/' . $showId);
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage staff for this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get staff ID from POST
        $staffId = isset($_POST['staff_id']) ? (int)$_POST['staff_id'] : 0;
        
        if ($staffId <= 0) {
            $this->setFlashMessage('coordinator_message', 'Invalid staff selection', 'danger');
            $this->redirect('coordinator/manageStaff/' . $showId);
            return;
        }
        
        // Get staff model
        $staffModel = $this->model('StaffModel');
        
        // Get current user ID (coordinator who is making the assignment)
        $userId = $this->auth->getCurrentUserId();
        
        // Assign staff to show
        if ($staffModel->assignStaffToShow($staffId, $showId, $userId)) {
            $this->setFlashMessage('coordinator_message', 'Staff assigned successfully', 'success');
        } else {
            $this->setFlashMessage('coordinator_message', 'Failed to assign staff', 'danger');
        }
        
        $this->redirect('coordinator/manageStaff/' . $showId);
    }
    
    /**
     * Remove staff from a show
     * 
     * @param int $showId Show ID
     * @param int $staffId Staff ID
     */
    public function removeStaff($showId, $staffId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to manage staff for this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get staff model
        $staffModel = $this->model('StaffModel');
        
        // Remove staff from show
        if ($staffModel->removeStaffFromShow($staffId, $showId)) {
            $this->setFlashMessage('coordinator_message', 'Staff removed successfully', 'success');
        } else {
            $this->setFlashMessage('coordinator_message', 'Failed to remove staff', 'danger');
        }
        
        $this->redirect('coordinator/manageStaff/' . $showId);
    }

    // ============================================================================
    // SECTION 12: RESULTS AND ANALYTICS
    // ============================================================================

    // ============================================================================
    // SECTION 13: MESSAGING AND COMMUNICATION
    // ============================================================================

    /**
     * Send bulk message to all registered users of a show
     */
    public function sendBulkMessage() {
        // Check if user is coordinator or admin
        if (!$this->auth->hasRole(['coordinator', 'admin'])) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Check if request is POST and AJAX
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        try {
            // Get JSON input
            $input = json_decode(file_get_contents('php://input'), true);

            if (!$input) {
                throw new Exception('Invalid JSON input');
            }

            $showId = $input['show_id'] ?? null;
            $subject = trim($input['subject'] ?? '');
            $message = trim($input['message'] ?? '');
            $requiresReply = $input['requires_reply'] ?? false;

            // Validate input
            if (!$showId || !$subject || !$message) {
                throw new Exception('Missing required fields');
            }

            // Get show to verify it exists and user has access
            $show = $this->showModel->getShowById($showId);
            if (!$show) {
                throw new Exception('Show not found');
            }

            $currentUserId = $this->auth->getCurrentUserId();

            // Check if user has access to this show
            if (!$this->auth->hasRole('admin') && $show->coordinator_id != $currentUserId) {
                throw new Exception('Access denied to this show');
            }

            // Get all registered users for this show
            $registrations = $this->registrationModel->getShowRegistrations($showId);

            if (empty($registrations)) {
                throw new Exception('No registered users found for this show');
            }

            // Load unified message model
            $messageModel = $this->model('UnifiedMessageModel');

            $sentCount = 0;

            // Send message to each registered user
            foreach ($registrations as $registration) {
                if (isset($registration->user_id) && $registration->user_id != $currentUserId) {
                    $messageId = $messageModel->sendMessage(
                        $currentUserId,
                        $registration->user_id,
                        $subject,
                        $message,
                        $showId,
                        'coordinator', // Use coordinator type for coordinator messages
                        $requiresReply,
                        null
                    );

                    if ($messageId) {
                        $sentCount++;
                    }
                }
            }

            echo json_encode([
                'success' => true,
                'count' => $sentCount,
                'message' => "Messages sent successfully to {$sentCount} users"
            ]);

        } catch (Exception $e) {
            error_log('Error in CoordinatorController::sendBulkMessage: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }

    /**
     * Send individual message to a specific user
     */
    public function sendIndividualMessage() {
        // Check if user is coordinator or admin
        if (!$this->auth->hasRole(['coordinator', 'admin'])) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Check if request is POST and AJAX
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        try {
            // Get JSON input
            $input = json_decode(file_get_contents('php://input'), true);

            if (!$input) {
                throw new Exception('Invalid JSON input');
            }

            $toUserId = $input['to_user_id'] ?? null;
            $showId = $input['show_id'] ?? null;
            $subject = trim($input['subject'] ?? '');
            $message = trim($input['message'] ?? '');
            $requiresReply = $input['requires_reply'] ?? false;

            // Validate input
            if (!$toUserId || !$subject || !$message) {
                throw new Exception('Missing required fields');
            }

            // If show ID provided, verify access
            if ($showId) {
                $show = $this->showModel->getShowById($showId);
                if (!$show) {
                    throw new Exception('Show not found');
                }

                $currentUserId = $this->auth->getCurrentUserId();

                // Check if user has access to this show
                if (!$this->auth->hasRole('admin') && $show->coordinator_id != $currentUserId) {
                    throw new Exception('Access denied to this show');
                }
            }

            // Load unified message model
            $messageModel = $this->model('UnifiedMessageModel');

            $currentUserId = $this->auth->getCurrentUserId();

            // Send message
            $messageId = $messageModel->sendMessage(
                $currentUserId,
                $toUserId,
                $subject,
                $message,
                $showId,
                'coordinator', // Use coordinator type for coordinator messages
                $requiresReply,
                null
            );

            if ($messageId) {
                echo json_encode([
                    'success' => true,
                    'message_id' => $messageId,
                    'message' => 'Message sent successfully'
                ]);
            } else {
                throw new Exception('Failed to send message');
            }

        } catch (Exception $e) {
            error_log('Error in CoordinatorController::sendIndividualMessage: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }

    /**
     * Get custom date/time fields from form template
     *
     * @param array $formFields Form fields array from template
     * @return array Array of field names and their types (date, time, datetime)
     */
    private function getCustomDateTimeFields($formFields) {
        $dateTimeFields = [];

        if (!is_array($formFields)) {
            return $dateTimeFields;
        }

        foreach ($formFields as $field) {
            if (!isset($field['type'])) {
                continue;
            }

            // Check if this is a date/time field type
            if (in_array($field['type'], ['date', 'time', 'datetime'])) {
                // Get field name (prefer 'name' over 'id')
                $fieldName = isset($field['name']) ? $field['name'] : (isset($field['id']) ? $field['id'] : null);

                if ($fieldName) {
                    // Skip standard date fields that are already handled
                    if (!in_array($fieldName, ['start_date', 'end_date', 'registration_start', 'registration_end'])) {
                        $dateTimeFields[$fieldName] = $field['type'];

                        if (defined('DEBUG_MODE') && DEBUG_MODE) {
                            error_log("CoordinatorController::getCustomDateTimeFields - Found custom field: {$fieldName} ({$field['type']})");
                        }
                    }
                }
            }
        }

        return $dateTimeFields;
    }

    /**
     * Voting Security Dashboard
     */
    public function votingSecurityDashboard($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);

        if (!$show) {
            $this->redirect('coordinator/dashboard');
            return;
        }

        // Check if this coordinator is assigned to this show or if user is an admin
        if ($show->coordinator_id != $_SESSION['user_id'] && !$this->auth->hasRole('admin')) {
            $this->redirect('coordinator/dashboard');
            return;
        }

        // Initialize security model
        require_once APPROOT . '/models/VotingSecurityModel.php';
        $securityModel = new VotingSecurityModel();

        // Get voting statistics
        $stats = $securityModel->getVotingStats($showId);

        // Get security alerts
        $alerts = $securityModel->getAlertsForShow($showId, true); // unacknowledged only

        // Get pagination and filter parameters
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(10, intval($_GET['limit']))) : 25;
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        $status = isset($_GET['status']) ? $_GET['status'] : 'all';
        $riskLevel = isset($_GET['risk']) ? $_GET['risk'] : 'all';
        $dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : '';
        $dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : '';

        // Get paginated votes with filters
        $filters = [
            'show_id' => $showId,
            'search' => $search,
            'status' => $status,
            'risk_level' => $riskLevel,
            'date_from' => $dateFrom,
            'date_to' => $dateTo
        ];

        $votesData = $securityModel->getPaginatedVotes($page, $limit, $filters);

        // Get flagged votes count (for priority display)
        $flaggedCount = $securityModel->getFlaggedVotesCount($showId);

        // Get pending appeals count
        $pendingAppealsCount = $securityModel->getPendingAppealsCount($showId);

        // Get vehicle count for manual vote dropdown (don't load all)
        $this->db->query('SELECT COUNT(*) as count FROM registrations WHERE show_id = :show_id');
        $this->db->bind(':show_id', $showId);
        $vehicleCount = $this->db->single()->count;

        $data = [
            'title' => 'Voting Security Dashboard - ' . $show->name,
            'show' => $show,
            'stats' => $stats,
            'alerts' => $alerts,
            'votes' => $votesData['votes'],
            'total_votes' => $votesData['total'],
            'current_page' => $page,
            'total_pages' => ceil($votesData['total'] / $limit),
            'limit' => $limit,
            'flagged_count' => $flaggedCount,
            'pending_appeals_count' => $pendingAppealsCount,
            'vehicle_count' => $vehicleCount,
            'filters' => $filters
        ];

        $this->view('coordinator/voting_security_dashboard', $data);
    }

    /**
     * AJAX endpoint for searching vehicles - optimized for large datasets
     */
    public function searchVehicles($showId) {
        // Check access
        $show = $this->showModel->getShowById($showId);
        if (!$show || ($show->coordinator_id != $_SESSION['user_id'] && !$this->auth->hasRole('admin'))) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        $query = isset($_GET['q']) ? trim($_GET['q']) : '';
        $limit = 20; // Limit results for performance

        try {
            if (empty($query)) {
                // Return recent registrations if no query
                $this->db->query('SELECT r.id, r.registration_number,
                                 CONCAT(v.year, " ", v.make, " ", v.model) as vehicle_info,
                                 COUNT(fv.id) as vote_count
                                 FROM registrations r
                                 LEFT JOIN vehicles v ON r.vehicle_id = v.id
                                 LEFT JOIN fan_votes fv ON r.id = fv.registration_id
                                 WHERE r.show_id = :show_id
                                 GROUP BY r.id
                                 ORDER BY r.created_at DESC
                                 LIMIT :limit');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':limit', $limit);
            } else {
                // Search vehicles by registration number, make, model, year
                $searchTerm = '%' . $query . '%';
                $this->db->query('SELECT r.id, r.registration_number,
                                 CONCAT(v.year, " ", v.make, " ", v.model) as vehicle_info,
                                 COUNT(fv.id) as vote_count
                                 FROM registrations r
                                 LEFT JOIN vehicles v ON r.vehicle_id = v.id
                                 LEFT JOIN fan_votes fv ON r.id = fv.registration_id
                                 WHERE r.show_id = :show_id
                                    AND (r.registration_number LIKE :search
                                         OR v.make LIKE :search2
                                         OR v.model LIKE :search3
                                         OR v.year LIKE :search4)
                                 GROUP BY r.id
                                 ORDER BY r.created_at DESC
                                 LIMIT :limit');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':search', $searchTerm);
                $this->db->bind(':search2', $searchTerm);
                $this->db->bind(':search3', $searchTerm);
                $this->db->bind(':search4', $searchTerm);
                $this->db->bind(':limit', $limit);
            }

            $vehicles = $this->db->resultSet();

            header('Content-Type: application/json');
            echo json_encode(['vehicles' => $vehicles]);

        } catch (Exception $e) {
            error_log('Error searching vehicles: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Search failed']);
        }
    }

    /**
     * Add manual vote
     */
    public function addManualVote($showId) {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('coordinator/votingSecurityDashboard/' . $showId);
            return;
        }

        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            flash('coordinator_message', 'Invalid request. Please try again.', 'alert alert-danger');
            $this->redirect('coordinator/votingSecurityDashboard/' . $showId);
            return;
        }

        // Get show and verify access
        $show = $this->showModel->getShowById($showId);
        if (!$show || ($show->coordinator_id != $_SESSION['user_id'] && !$this->auth->hasRole('admin'))) {
            $this->redirect('coordinator/dashboard');
            return;
        }

        // Sanitize input
        $_POST = $this->sanitizeInput($_POST);

        $registrationId = intval($_POST['registration_id']);
        $voterName = trim($_POST['voter_name']);
        $voterContact = trim($_POST['voter_contact']);
        $notes = trim($_POST['notes']);

        // Validate input
        if (empty($registrationId) || empty($voterName) || empty($voterContact)) {
            flash('coordinator_message', 'All fields are required.', 'alert alert-danger');
            $this->redirect('coordinator/votingSecurityDashboard/' . $showId);
            return;
        }

        // Record manual vote
        $result = $this->judgingModel->recordManualFanVote(
            $showId,
            $registrationId,
            $voterName,
            $voterContact,
            $_SESSION['user_id'],
            'coordinator',
            $notes
        );

        if ($result) {
            flash('coordinator_message', 'Manual vote added successfully.', 'alert alert-success');
        } else {
            flash('coordinator_message', 'Error adding manual vote. Please try again.', 'alert alert-danger');
        }

        $this->redirect('coordinator/votingSecurityDashboard/' . $showId);
    }

    /**
     * Bulk vote actions (approve, flag, delete)
     */
    public function bulkVoteAction($showId) {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
            return;
        }

        // Get show and verify access
        $show = $this->showModel->getShowById($showId);
        if (!$show || ($show->coordinator_id != $_SESSION['user_id'] && !$this->auth->hasRole('admin'))) {
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        $action = $_POST['action'] ?? '';
        $voteIds = json_decode($_POST['vote_ids'] ?? '[]', true);
        $reason = $_POST['reason'] ?? '';

        if (empty($action) || empty($voteIds)) {
            echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
            return;
        }

        try {
            $success = false;
            $message = '';

            switch ($action) {
                case 'approve':
                    $success = $this->approveVotes($voteIds, $_SESSION['user_id']);
                    $message = $success ? 'Votes approved successfully' : 'Error approving votes';
                    break;

                case 'flag':
                    $success = $this->flagVotes($voteIds, $_SESSION['user_id'], $reason);
                    $message = $success ? 'Votes flagged successfully' : 'Error flagging votes';
                    break;

                case 'delete':
                    $success = $this->deleteVotes($voteIds, $_SESSION['user_id']);
                    $message = $success ? 'Votes deleted successfully' : 'Error deleting votes';
                    break;

                default:
                    $message = 'Invalid action';
            }

            echo json_encode(['success' => $success, 'message' => $message]);

        } catch (Exception $e) {
            error_log('Bulk vote action error: ' . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'An error occurred']);
        }
    }

    /**
     * Approve votes
     */
    private function approveVotes($voteIds, $userId) {
        try {
            $placeholders = implode(',', array_fill(0, count($voteIds), '?'));
            $this->db->query("UPDATE fan_votes SET is_approved = TRUE, is_flagged = FALSE,
                             reviewed_by = ?, reviewed_at = NOW()
                             WHERE id IN ($placeholders)");

            $this->db->bind(1, $userId);
            foreach ($voteIds as $index => $voteId) {
                $this->db->bind($index + 2, $voteId);
            }

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error approving votes: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Flag votes
     */
    private function flagVotes($voteIds, $userId, $reason = '') {
        try {
            $placeholders = implode(',', array_fill(0, count($voteIds), '?'));
            $this->db->query("UPDATE fan_votes SET is_flagged = TRUE,
                             reviewed_by = ?, reviewed_at = NOW(), review_notes = ?
                             WHERE id IN ($placeholders)");

            $this->db->bind(1, $userId);
            $this->db->bind(2, $reason);
            foreach ($voteIds as $index => $voteId) {
                $this->db->bind($index + 3, $voteId);
            }

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error flagging votes: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete votes
     */
    private function deleteVotes($voteIds, $userId) {
        try {
            $placeholders = implode(',', array_fill(0, count($voteIds), '?'));
            $this->db->query("DELETE FROM fan_votes WHERE id IN ($placeholders)");

            foreach ($voteIds as $index => $voteId) {
                $this->db->bind($index + 1, $voteId);
            }

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('Error deleting votes: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Acknowledge security alert
     */
    public function acknowledgeAlert($alertId) {
        require_once APPROOT . '/models/VotingSecurityModel.php';
        $securityModel = new VotingSecurityModel();

        $result = $securityModel->acknowledgeAlert($alertId, $_SESSION['user_id']);

        if ($result) {
            flash('coordinator_message', 'Alert acknowledged.', 'alert alert-success');
        } else {
            flash('coordinator_message', 'Error acknowledging alert.', 'alert alert-danger');
        }

        // Redirect back to the referring page
        $referer = $_SERVER['HTTP_REFERER'] ?? URLROOT . '/coordinator/dashboard';
        header('Location: ' . $referer);
        exit;
    }

    /**
     * Bulk import manual votes from CSV (coordinator)
     */
    public function bulkImportManualVotes($showId) {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('coordinator/votingSecurityDashboard/' . $showId);
            return;
        }

        // Get show and verify access
        $show = $this->showModel->getShowById($showId);
        if (!$show || ($show->coordinator_id != $_SESSION['user_id'] && !$this->auth->hasRole('admin'))) {
            $this->redirect('coordinator/dashboard');
            return;
        }

        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->setFlashMessage('coordinator_message', 'Invalid request. Please try again.', 'danger');
            $this->redirect('coordinator/votingSecurityDashboard/' . $showId);
            return;
        }

        // Check if file was uploaded
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            $this->setFlashMessage('coordinator_message', 'Please select a valid CSV file.', 'danger');
            $this->redirect('coordinator/votingSecurityDashboard/' . $showId);
            return;
        }

        $skipDuplicates = isset($_POST['skip_duplicates']);
        $bulkNotes = trim($_POST['bulk_notes'] ?? '');

        try {
            // Read CSV file
            $csvFile = $_FILES['csv_file']['tmp_name'];
            $handle = fopen($csvFile, 'r');

            if (!$handle) {
                throw new Exception('Could not read CSV file');
            }

            // Get header row
            $headers = fgetcsv($handle);
            if (!$headers) {
                throw new Exception('CSV file is empty or invalid');
            }

            // Validate required columns
            $requiredColumns = ['registration_number', 'voter_name', 'voter_contact'];
            $columnMap = [];

            foreach ($requiredColumns as $required) {
                $index = array_search($required, $headers);
                if ($index === false) {
                    throw new Exception("Missing required column: {$required}");
                }
                $columnMap[$required] = $index;
            }

            // Optional columns
            $notesIndex = array_search('notes', $headers);

            // Get judging model
            require_once APPROOT . '/models/JudgingModel.php';
            $judgingModel = new JudgingModel();

            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            // Process each row
            while (($row = fgetcsv($handle)) !== false) {
                try {
                    $registrationNumber = trim($row[$columnMap['registration_number']]);
                    $voterName = trim($row[$columnMap['voter_name']]);
                    $voterContact = trim($row[$columnMap['voter_contact']]);
                    $notes = $notesIndex !== false ? trim($row[$notesIndex]) : '';

                    // Add bulk notes if provided
                    if (!empty($bulkNotes)) {
                        $notes = empty($notes) ? $bulkNotes : $notes . ' | ' . $bulkNotes;
                    }

                    // Validate row data
                    if (empty($registrationNumber) || empty($voterName) || empty($voterContact)) {
                        $errors[] = "Row skipped: Missing required data for registration {$registrationNumber}";
                        $errorCount++;
                        continue;
                    }

                    // Find registration ID by registration number
                    $this->db->query('SELECT id FROM registrations WHERE show_id = :show_id AND registration_number = :reg_number');
                    $this->db->bind(':show_id', $showId);
                    $this->db->bind(':reg_number', $registrationNumber);
                    $registration = $this->db->single();

                    if (!$registration) {
                        $errors[] = "Registration number {$registrationNumber} not found";
                        $errorCount++;
                        continue;
                    }

                    // Check for duplicates if skip_duplicates is enabled
                    if ($skipDuplicates) {
                        $this->db->query('SELECT id FROM fan_votes WHERE show_id = :show_id AND registration_id = :reg_id AND manual_voter_name = :voter_name');
                        $this->db->bind(':show_id', $showId);
                        $this->db->bind(':reg_id', $registration->id);
                        $this->db->bind(':voter_name', $voterName);

                        if ($this->db->single()) {
                            $errors[] = "Duplicate vote skipped for {$voterName} on registration {$registrationNumber}";
                            continue;
                        }
                    }

                    // Record manual vote
                    $result = $judgingModel->recordManualFanVote(
                        $showId,
                        $registration->id,
                        $voterName,
                        $voterContact,
                        $_SESSION['user_id'],
                        'coordinator_bulk',
                        $notes
                    );

                    if ($result) {
                        $successCount++;
                    } else {
                        $errors[] = "Failed to add vote for {$voterName} on registration {$registrationNumber}";
                        $errorCount++;
                    }

                } catch (Exception $e) {
                    $errors[] = "Error processing row: " . $e->getMessage();
                    $errorCount++;
                }
            }

            fclose($handle);

            // Set success message
            $message = "Bulk import completed: {$successCount} votes added";
            if ($errorCount > 0) {
                $message .= ", {$errorCount} errors";
            }

            $this->setFlashMessage('coordinator_message', $message, $errorCount > 0 ? 'warning' : 'success');

            // Log errors if any
            if (!empty($errors)) {
                error_log('Coordinator bulk vote import errors: ' . implode('; ', $errors));
            }

        } catch (Exception $e) {
            error_log('Coordinator bulk import error: ' . $e->getMessage());
            $this->setFlashMessage('coordinator_message', 'Import failed: ' . $e->getMessage(), 'danger');
        }

        $this->redirect('coordinator/votingSecurityDashboard/' . $showId);
    }

    /**
     * Download CSV template for manual vote import (coordinator)
     */
    public function downloadManualVoteTemplate() {
        $filename = 'manual_vote_template.csv';
        $headers = ['registration_number', 'voter_name', 'voter_contact', 'notes'];

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $output = fopen('php://output', 'w');
        fputcsv($output, $headers);

        // Add sample row
        fputcsv($output, ['REG001', 'John Doe', '<EMAIL>', 'Sample vote entry']);

        fclose($output);
        exit;
    }
}
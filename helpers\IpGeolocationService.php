<?php
/**
 * IP Geolocation Service
 * Provides IP-based location detection for voting security
 */

class IpGeolocationService {
    
    private $providers = [
        'ipapi' => 'http://ip-api.com/json/',
        'ipinfo' => 'https://ipinfo.io/',
        'freegeoip' => 'https://freegeoip.app/json/'
    ];
    
    private $cache = [];
    private $cacheFile;
    
    public function __construct() {
        $this->cacheFile = APPROOT . '/cache/ip_geolocation_cache.json';
        $this->loadCache();
    }
    
    /**
     * Get geolocation data for an IP address
     */
    public function getLocation($ipAddress) {
        // Skip private/local IPs
        if ($this->isPrivateIp($ipAddress)) {
            return $this->getDefaultLocation();
        }
        
        // Check cache first
        if (isset($this->cache[$ipAddress])) {
            $cached = $this->cache[$ipAddress];
            // Cache for 24 hours
            if (time() - $cached['timestamp'] < 86400) {
                return $cached['data'];
            }
        }
        
        // Try each provider until we get a result
        foreach ($this->providers as $provider => $baseUrl) {
            try {
                $result = $this->queryProvider($provider, $baseUrl, $ipAddress);
                if ($result) {
                    // Cache the result
                    $this->cache[$ipAddress] = [
                        'data' => $result,
                        'timestamp' => time()
                    ];
                    $this->saveCache();
                    return $result;
                }
            } catch (Exception $e) {
                error_log("IP Geolocation error with {$provider}: " . $e->getMessage());
                continue;
            }
        }
        
        // Return default if all providers fail
        return $this->getDefaultLocation();
    }
    
    /**
     * Query a specific geolocation provider
     */
    private function queryProvider($provider, $baseUrl, $ipAddress) {
        $timeout = 5; // 5 second timeout
        
        switch ($provider) {
            case 'ipapi':
                return $this->queryIpApi($baseUrl, $ipAddress, $timeout);
            case 'ipinfo':
                return $this->queryIpInfo($baseUrl, $ipAddress, $timeout);
            case 'freegeoip':
                return $this->queryFreeGeoIp($baseUrl, $ipAddress, $timeout);
            default:
                return null;
        }
    }
    
    /**
     * Query ip-api.com (free, no API key required)
     */
    private function queryIpApi($baseUrl, $ipAddress, $timeout) {
        $url = $baseUrl . $ipAddress . '?fields=status,country,countryCode,region,regionName,city,lat,lon,proxy,query';
        
        $context = stream_context_create([
            'http' => [
                'timeout' => $timeout,
                'user_agent' => 'Events System IP Geolocation'
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        if (!$response) {
            return null;
        }
        
        $data = json_decode($response, true);
        if (!$data || $data['status'] !== 'success') {
            return null;
        }
        
        return [
            'country' => $data['countryCode'] ?? 'US',
            'region' => $data['regionName'] ?? '',
            'city' => $data['city'] ?? '',
            'latitude' => floatval($data['lat'] ?? 0),
            'longitude' => floatval($data['lon'] ?? 0),
            'is_vpn' => $data['proxy'] ?? false,
            'accuracy' => 'city',
            'provider' => 'ip-api'
        ];
    }
    
    /**
     * Query ipinfo.io (free tier available)
     */
    private function queryIpInfo($baseUrl, $ipAddress, $timeout) {
        $url = $baseUrl . $ipAddress . '/json';
        
        $context = stream_context_create([
            'http' => [
                'timeout' => $timeout,
                'user_agent' => 'Events System IP Geolocation'
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        if (!$response) {
            return null;
        }
        
        $data = json_decode($response, true);
        if (!$data || isset($data['error'])) {
            return null;
        }
        
        // Parse location coordinates
        $coords = explode(',', $data['loc'] ?? '0,0');
        
        return [
            'country' => $data['country'] ?? 'US',
            'region' => $data['region'] ?? '',
            'city' => $data['city'] ?? '',
            'latitude' => floatval($coords[0] ?? 0),
            'longitude' => floatval($coords[1] ?? 0),
            'is_vpn' => false, // ipinfo doesn't provide VPN detection in free tier
            'accuracy' => 'city',
            'provider' => 'ipinfo'
        ];
    }
    
    /**
     * Query freegeoip.app (free)
     */
    private function queryFreeGeoIp($baseUrl, $ipAddress, $timeout) {
        $url = $baseUrl . $ipAddress;
        
        $context = stream_context_create([
            'http' => [
                'timeout' => $timeout,
                'user_agent' => 'Events System IP Geolocation'
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        if (!$response) {
            return null;
        }
        
        $data = json_decode($response, true);
        if (!$data) {
            return null;
        }
        
        return [
            'country' => $data['country_code'] ?? 'US',
            'region' => $data['region_name'] ?? '',
            'city' => $data['city'] ?? '',
            'latitude' => floatval($data['latitude'] ?? 0),
            'longitude' => floatval($data['longitude'] ?? 0),
            'is_vpn' => false,
            'accuracy' => 'city',
            'provider' => 'freegeoip'
        ];
    }
    
    /**
     * Check if IP is private/local
     */
    private function isPrivateIp($ipAddress) {
        return !filter_var(
            $ipAddress,
            FILTER_VALIDATE_IP,
            FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE
        );
    }
    
    /**
     * Get default location for private IPs or failures
     */
    private function getDefaultLocation() {
        return [
            'country' => 'US',
            'region' => 'Unknown',
            'city' => 'Unknown',
            'latitude' => 0,
            'longitude' => 0,
            'is_vpn' => false,
            'accuracy' => 'unknown',
            'provider' => 'default'
        ];
    }
    
    /**
     * Load cache from file
     */
    private function loadCache() {
        if (file_exists($this->cacheFile)) {
            $content = file_get_contents($this->cacheFile);
            $this->cache = json_decode($content, true) ?: [];
        }
    }
    
    /**
     * Save cache to file
     */
    private function saveCache() {
        try {
            // Ensure cache directory exists
            $cacheDir = dirname($this->cacheFile);
            if (!is_dir($cacheDir)) {
                mkdir($cacheDir, 0755, true);
            }
            
            // Clean old entries (older than 7 days)
            $cutoff = time() - (7 * 86400);
            foreach ($this->cache as $ip => $data) {
                if ($data['timestamp'] < $cutoff) {
                    unset($this->cache[$ip]);
                }
            }
            
            // Save to file
            file_put_contents($this->cacheFile, json_encode($this->cache));
        } catch (Exception $e) {
            error_log('Error saving IP geolocation cache: ' . $e->getMessage());
        }
    }
    
    /**
     * Clear cache
     */
    public function clearCache() {
        $this->cache = [];
        if (file_exists($this->cacheFile)) {
            unlink($this->cacheFile);
        }
    }
    
    /**
     * Get cache statistics
     */
    public function getCacheStats() {
        return [
            'entries' => count($this->cache),
            'file_exists' => file_exists($this->cacheFile),
            'file_size' => file_exists($this->cacheFile) ? filesize($this->cacheFile) : 0
        ];
    }
}
?>

<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <div class="row mb-4 align-items-center">
        <div class="col-md-8">
            <h1 class="h2 mb-0">
                <i class="fas fa-shield-alt text-success me-2"></i>
                <?php echo $data['title']; ?>
            </h1>
            <p class="text-muted mb-0">Comprehensive vote management and security oversight</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group">
                <?php if ($data['show_id']): ?>
                <button class="btn btn-primary" onclick="openAdminManualVoteModal()">
                    <i class="fas fa-plus me-2"></i>Add Manual Vote
                </button>
                <button class="btn btn-success" onclick="openBulkManualVoteModal()">
                    <i class="fas fa-upload me-2"></i>Bulk Import
                </button>
                <?php endif; ?>
                <a href="<?php echo BASE_URL; ?>/admin/votingSecuritySettings" class="btn btn-outline-primary">
                    <i class="fas fa-cog me-2"></i>Security Settings
                </a>
                <?php if ($data['flagged_count'] > 0): ?>
                <a href="<?php echo BASE_URL; ?>/admin/flaggedVotes<?php echo $data['show_id'] ? '/' . $data['show_id'] : ''; ?>" class="btn btn-warning">
                    <i class="fas fa-flag me-2"></i>Flagged (<?php echo $data['flagged_count']; ?>)
                </a>
                <?php endif; ?>
                <?php if ($data['pending_appeals_count'] > 0): ?>
                <a href="<?php echo BASE_URL; ?>/admin/pendingAppeals<?php echo $data['show_id'] ? '/' . $data['show_id'] : ''; ?>" class="btn btn-info">
                    <i class="fas fa-gavel me-2"></i>Appeals (<?php echo $data['pending_appeals_count']; ?>)
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php flash('vote_message'); ?>

    <!-- Advanced Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Advanced Filters
                        <button class="btn btn-sm btn-outline-secondary float-end" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </h6>
                </div>
                <div class="collapse" id="filtersCollapse">
                    <div class="card-body">
                        <form method="GET" action="<?php echo BASE_URL; ?>/admin/voteManagement<?php echo $data['show_id'] ? '/' . $data['show_id'] : ''; ?>">
                            <div class="row g-3">
                                <!-- Show Search (AJAX) -->
                                <div class="col-md-3">
                                    <label for="showSearch" class="form-label">Show</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="showSearch" placeholder="Search shows..."
                                               value="<?php echo $data['selected_show'] ? htmlspecialchars($data['selected_show']->name) : 'All Shows'; ?>" readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="openShowSelector()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                    <input type="hidden" name="show_id" value="<?php echo $data['show_id']; ?>">
                                </div>

                                <!-- Search -->
                                <div class="col-md-3">
                                    <label for="search" class="form-label">Search</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                           placeholder="Show, vehicle, IP, voter..."
                                           value="<?php echo htmlspecialchars($data['filters']['search']); ?>">
                                </div>

                                <!-- Status Filter -->
                                <div class="col-md-2">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="all" <?php echo $data['filters']['status'] === 'all' ? 'selected' : ''; ?>>All Status</option>
                                        <option value="approved" <?php echo $data['filters']['status'] === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                        <option value="flagged" <?php echo $data['filters']['status'] === 'flagged' ? 'selected' : ''; ?>>Flagged</option>
                                        <option value="pending" <?php echo $data['filters']['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    </select>
                                </div>

                                <!-- Risk Level Filter -->
                                <div class="col-md-2">
                                    <label for="risk" class="form-label">Risk Level</label>
                                    <select class="form-select" id="risk" name="risk">
                                        <option value="all" <?php echo $data['filters']['risk_level'] === 'all' ? 'selected' : ''; ?>>All Risk</option>
                                        <option value="high" <?php echo $data['filters']['risk_level'] === 'high' ? 'selected' : ''; ?>>High (70+)</option>
                                        <option value="medium" <?php echo $data['filters']['risk_level'] === 'medium' ? 'selected' : ''; ?>>Medium (40-69)</option>
                                        <option value="low" <?php echo $data['filters']['risk_level'] === 'low' ? 'selected' : ''; ?>>Low (0-39)</option>
                                    </select>
                                </div>

                                <!-- Results Per Page -->
                                <div class="col-md-2">
                                    <label for="limit" class="form-label">Per Page</label>
                                    <select class="form-select" id="limit" name="limit">
                                        <option value="10" <?php echo $data['limit'] == 10 ? 'selected' : ''; ?>>10</option>
                                        <option value="25" <?php echo $data['limit'] == 25 ? 'selected' : ''; ?>>25</option>
                                        <option value="50" <?php echo $data['limit'] == 50 ? 'selected' : ''; ?>>50</option>
                                        <option value="100" <?php echo $data['limit'] == 100 ? 'selected' : ''; ?>>100</option>
                                    </select>
                                </div>

                                <!-- Date Range -->
                                <div class="col-md-3">
                                    <label for="date_from" class="form-label">Date From</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from"
                                           value="<?php echo htmlspecialchars($data['filters']['date_from']); ?>">
                                </div>

                                <div class="col-md-3">
                                    <label for="date_to" class="form-label">Date To</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to"
                                           value="<?php echo htmlspecialchars($data['filters']['date_to']); ?>">
                                </div>

                                <!-- Filter Actions -->
                                <div class="col-md-6 d-flex align-items-end">
                                    <div class="btn-group w-100">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i>Apply Filters
                                        </button>
                                        <a href="<?php echo BASE_URL; ?>/admin/voteManagement<?php echo $data['show_id'] ? '/' . $data['show_id'] : ''; ?>" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($data['stats']['total_votes'] ?? 0); ?></h4>
                            <p class="mb-0">Total Votes</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-vote-yea fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($data['stats']['flagged_votes'] ?? 0); ?></h4>
                            <p class="mb-0">Flagged Votes</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-flag fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($data['stats']['high_risk_votes'] ?? 0); ?></h4>
                            <p class="mb-0">High Risk</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($data['stats']['pending_appeals'] ?? 0); ?></h4>
                            <p class="mb-0">Pending Appeals</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-gavel fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Appeals Section -->
    <?php if (!empty($data['pending_appeals'])): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-gavel me-2"></i>Pending Vote Appeals
                        <span class="badge bg-dark ms-2"><?php echo count($data['pending_appeals']); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach ($data['pending_appeals'] as $appeal): ?>
                    <div class="border rounded p-3 mb-3">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-1">
                                    <?php echo htmlspecialchars($appeal->voter_name); ?>
                                    <small class="text-muted">(<?php echo htmlspecialchars($appeal->voter_email); ?>)</small>
                                </h6>
                                <p class="mb-1"><strong>Show:</strong> <?php echo htmlspecialchars($appeal->show_name ?? 'Unknown'); ?></p>
                                <p class="mb-1"><strong>Reason:</strong> <?php echo ucfirst(str_replace('_', ' ', $appeal->appeal_reason)); ?></p>
                                <p class="mb-1"><strong>Explanation:</strong> <?php echo htmlspecialchars($appeal->appeal_explanation); ?></p>
                                <small class="text-muted">Submitted: <?php echo date('M j, Y g:i A', strtotime($appeal->created_at)); ?></small>
                            </div>
                            <div class="col-md-4 text-end">
                                <form method="POST" action="<?php echo BASE_URL; ?>/admin/processVoteAppeal" class="d-inline">
                                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                                    <input type="hidden" name="appeal_id" value="<?php echo $appeal->id; ?>">
                                    <input type="hidden" name="show_id" value="<?php echo $data['show_id']; ?>">
                                    <div class="mb-2">
                                        <textarea name="admin_response" class="form-control form-control-sm" placeholder="Response to voter..." rows="2"></textarea>
                                    </div>
                                    <button type="submit" name="appeal_action" value="approve" class="btn btn-success btn-sm me-1">
                                        <i class="fas fa-check me-1"></i>Approve
                                    </button>
                                    <button type="submit" name="appeal_action" value="deny" class="btn btn-danger btn-sm">
                                        <i class="fas fa-times me-1"></i>Deny
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Flagged Votes Section -->
    <?php if (!empty($data['flagged_votes'])): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-flag me-2"></i>Flagged Votes Requiring Review
                        <span class="badge bg-dark ms-2"><?php echo count($data['flagged_votes']); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo BASE_URL; ?>/admin/processBulkVoteAction">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="show_id" value="<?php echo $data['show_id']; ?>">
                        
                        <div class="mb-3">
                            <div class="btn-group">
                                <button type="submit" name="bulk_action" value="approve" class="btn btn-success btn-sm">
                                    <i class="fas fa-check me-1"></i>Approve Selected
                                </button>
                                <button type="submit" name="bulk_action" value="delete" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash me-1"></i>Delete Selected
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><input type="checkbox" id="selectAllFlagged"></th>
                                        <th>Show</th>
                                        <th>Vehicle</th>
                                        <th>Voter Info</th>
                                        <th>Risk Score</th>
                                        <th>Flags</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['flagged_votes'] as $vote): ?>
                                    <tr>
                                        <td><input type="checkbox" name="vote_ids[]" value="<?php echo $vote->id; ?>"></td>
                                        <td><?php echo htmlspecialchars($vote->show_name ?? 'Unknown'); ?></td>
                                        <td><?php echo htmlspecialchars($vote->vehicle_info ?? 'Unknown'); ?></td>
                                        <td>
                                            <?php if ($vote->vote_method === 'facebook'): ?>
                                                <i class="fab fa-facebook text-primary me-1"></i><?php echo htmlspecialchars($vote->fb_user_name ?? 'Facebook User'); ?>
                                            <?php elseif ($vote->manual_entered_by): ?>
                                                <i class="fas fa-user-edit text-info me-1"></i>Manual Entry
                                            <?php else: ?>
                                                <i class="fas fa-globe text-secondary me-1"></i><?php echo htmlspecialchars($vote->voter_ip); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $vote->risk_score >= 90 ? 'danger' : ($vote->risk_score >= 70 ? 'warning' : 'success'); ?>">
                                                <?php echo $vote->risk_score; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php 
                                            $flags = json_decode($vote->security_flags ?? '[]', true);
                                            if (!empty($flags)) {
                                                foreach (array_slice($flags, 0, 2) as $flag) {
                                                    echo '<span class="badge bg-warning text-dark me-1">' . htmlspecialchars($flag) . '</span>';
                                                }
                                                if (count($flags) > 2) {
                                                    echo '<span class="badge bg-secondary">+' . (count($flags) - 2) . '</span>';
                                                }
                                            }
                                            ?>
                                        </td>
                                        <td><?php echo date('M j, g:i A', strtotime($vote->created_at)); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Paginated Votes Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-vote-yea me-2"></i>Votes
                        <span class="badge bg-dark ms-2"><?php echo number_format($data['total_votes']); ?> total</span>
                    </h5>
                    <div class="text-end">
                        <small>
                            Page <?php echo $data['current_page']; ?> of <?php echo $data['total_pages']; ?>
                            (<?php echo $data['limit']; ?> per page)
                        </small>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($data['votes'])): ?>
                    <form method="POST" action="<?php echo BASE_URL; ?>/admin/processBulkVoteAction">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="show_id" value="<?php echo $data['show_id']; ?>">

                        <div class="mb-3 d-flex justify-content-between align-items-center">
                            <div class="btn-group">
                                <button type="submit" name="bulk_action" value="approve" class="btn btn-success btn-sm">
                                    <i class="fas fa-check me-1"></i>Approve Selected
                                </button>
                                <button type="submit" name="bulk_action" value="flag" class="btn btn-warning btn-sm">
                                    <i class="fas fa-flag me-1"></i>Flag Selected
                                </button>
                                <button type="submit" name="bulk_action" value="delete" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash me-1"></i>Delete Selected
                                </button>
                            </div>

                            <!-- Pagination Controls -->
                            <?php if ($data['total_pages'] > 1): ?>
                            <nav aria-label="Vote pagination">
                                <ul class="pagination pagination-sm mb-0">
                                    <?php if ($data['current_page'] > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $data['current_page'] - 1; ?>&<?php echo http_build_query(array_filter($data['filters'])); ?>&limit=<?php echo $data['limit']; ?>">Previous</a>
                                        </li>
                                    <?php endif; ?>

                                    <?php
                                    $start = max(1, $data['current_page'] - 2);
                                    $end = min($data['total_pages'], $data['current_page'] + 2);
                                    ?>

                                    <?php for ($i = $start; $i <= $end; $i++): ?>
                                        <li class="page-item <?php echo $i == $data['current_page'] ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($data['filters'])); ?>&limit=<?php echo $data['limit']; ?>"><?php echo $i; ?></a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($data['current_page'] < $data['total_pages']): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $data['current_page'] + 1; ?>&<?php echo http_build_query(array_filter($data['filters'])); ?>&limit=<?php echo $data['limit']; ?>">Next</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                            <?php endif; ?>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th><input type="checkbox" id="selectAll"></th>
                                        <th>Show</th>
                                        <th>Vehicle</th>
                                        <th>Voter</th>
                                        <th>Method</th>
                                        <th>Risk</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['votes'] as $vote): ?>
                                    <tr class="<?php echo $vote->is_flagged ? 'table-warning' : ($vote->risk_score >= 70 ? 'table-danger' : ''); ?>">
                                        <td><input type="checkbox" name="vote_ids[]" value="<?php echo $vote->id; ?>"></td>
                                        <td>
                                            <small><?php echo htmlspecialchars($vote->show_name ?? 'Unknown'); ?></small>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($vote->vehicle_info ?? 'Unknown'); ?></small>
                                        </td>
                                        <td>
                                            <?php if ($vote->vote_method === 'facebook'): ?>
                                                <i class="fab fa-facebook text-primary me-1"></i>
                                                <small><?php echo htmlspecialchars($vote->fb_user_name ?? 'Facebook User'); ?></small>
                                            <?php elseif ($vote->manual_entered_by): ?>
                                                <i class="fas fa-user-edit text-info me-1"></i>
                                                <small>Manual by <?php echo htmlspecialchars($vote->entered_by_name ?? 'Staff'); ?></small>
                                            <?php else: ?>
                                                <i class="fas fa-globe text-secondary me-1"></i>
                                                <small><?php echo htmlspecialchars($vote->voter_ip); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $vote->vote_method === 'facebook' ? 'primary' : ($vote->manual_entered_by ? 'info' : 'secondary'); ?>">
                                                <?php echo ucfirst($vote->vote_method ?? 'ip'); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $vote->risk_score >= 90 ? 'danger' : ($vote->risk_score >= 70 ? 'warning' : 'success'); ?>">
                                                <?php echo $vote->risk_score; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($vote->is_flagged): ?>
                                                <span class="badge bg-warning text-dark">Flagged</span>
                                            <?php elseif ($vote->is_approved): ?>
                                                <span class="badge bg-success">Approved</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Pending</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo date('M j, g:i A', strtotime($vote->created_at)); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if ($vote->is_flagged): ?>
                                                    <button type="button" class="btn btn-success btn-sm" onclick="approveVote(<?php echo $vote->id; ?>)">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button type="button" class="btn btn-danger btn-sm" onclick="deleteVote(<?php echo $vote->id; ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Bottom Pagination -->
                        <?php if ($data['total_pages'] > 1): ?>
                        <div class="d-flex justify-content-center mt-3">
                            <nav aria-label="Vote pagination">
                                <ul class="pagination">
                                    <?php if ($data['current_page'] > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $data['current_page'] - 1; ?>&<?php echo http_build_query(array_filter($data['filters'])); ?>&limit=<?php echo $data['limit']; ?>">Previous</a>
                                        </li>
                                    <?php endif; ?>

                                    <?php
                                    $start = max(1, $data['current_page'] - 5);
                                    $end = min($data['total_pages'], $data['current_page'] + 5);
                                    ?>

                                    <?php if ($start > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1&<?php echo http_build_query(array_filter($data['filters'])); ?>&limit=<?php echo $data['limit']; ?>">1</a>
                                        </li>
                                        <?php if ($start > 2): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php for ($i = $start; $i <= $end; $i++): ?>
                                        <li class="page-item <?php echo $i == $data['current_page'] ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($data['filters'])); ?>&limit=<?php echo $data['limit']; ?>"><?php echo $i; ?></a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($end < $data['total_pages']): ?>
                                        <?php if ($end < $data['total_pages'] - 1): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif; ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $data['total_pages']; ?>&<?php echo http_build_query(array_filter($data['filters'])); ?>&limit=<?php echo $data['limit']; ?>"><?php echo $data['total_pages']; ?></a>
                                        </li>
                                    <?php endif; ?>

                                    <?php if ($data['current_page'] < $data['total_pages']): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $data['current_page'] + 1; ?>&<?php echo http_build_query(array_filter($data['filters'])); ?>&limit=<?php echo $data['limit']; ?>">Next</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                        <?php endif; ?>
                    </form>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-vote-yea fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No votes found</h5>
                        <p class="text-muted">No votes match the selected criteria.</p>
                        <a href="?<?php echo http_build_query(['show_id' => $data['show_id']]); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-times me-2"></i>Clear Filters
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Admin Manual Vote Modal -->
<div class="modal fade" id="adminManualVoteModal" tabindex="-1" aria-labelledby="adminManualVoteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="adminManualVoteModalLabel">Add Manual Vote (Admin)</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="<?php echo BASE_URL; ?>/admin/addManualVote/<?php echo $data['show_id']; ?>">
                <div class="modal-body">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">

                    <div class="mb-3">
                        <label for="admin_vehicle_search" class="form-label">Select Vehicle</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="admin_vehicle_search" placeholder="Search vehicles..." readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="openAdminVehicleSelector()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <input type="hidden" name="registration_id" id="admin_selected_vehicle_id" required>
                        <small class="text-muted">Search by registration number, make, model, or year</small>
                    </div>

                    <div class="mb-3">
                        <label for="admin_voter_name" class="form-label">Voter Name</label>
                        <input type="text" class="form-control" id="admin_voter_name" name="voter_name" required>
                        <small class="text-muted">Full name of the person voting</small>
                    </div>

                    <div class="mb-3">
                        <label for="admin_voter_contact" class="form-label">Voter Contact</label>
                        <input type="text" class="form-control" id="admin_voter_contact" name="voter_contact" required>
                        <small class="text-muted">Phone number or email address</small>
                    </div>

                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="admin_notes" name="notes" rows="3"></textarea>
                        <small class="text-muted">Any additional information about this vote</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Vote</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Manual Vote Import Modal -->
<div class="modal fade" id="bulkManualVoteModal" tabindex="-1" aria-labelledby="bulkManualVoteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkManualVoteModalLabel">Bulk Import Manual Votes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="<?php echo BASE_URL; ?>/admin/bulkImportManualVotes/<?php echo $data['show_id']; ?>" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>CSV Format Requirements</h6>
                        <p class="mb-2">Your CSV file must include these columns:</p>
                        <ul class="mb-0">
                            <li><strong>registration_number</strong> - Vehicle registration number</li>
                            <li><strong>voter_name</strong> - Full name of voter</li>
                            <li><strong>voter_contact</strong> - Phone or email</li>
                            <li><strong>notes</strong> - Optional notes</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <label for="bulk_csv_file" class="form-label">CSV File</label>
                        <input type="file" class="form-control" id="bulk_csv_file" name="csv_file" accept=".csv" required>
                        <small class="text-muted">Upload a CSV file with manual votes to import</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skip_duplicates" name="skip_duplicates" checked>
                            <label class="form-check-label" for="skip_duplicates">
                                Skip duplicate votes (same voter for same vehicle)
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="bulk_notes" class="form-label">Bulk Notes (Optional)</label>
                        <textarea class="form-control" id="bulk_notes" name="bulk_notes" rows="2" placeholder="Notes to add to all imported votes"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <a href="<?php echo BASE_URL; ?>/admin/downloadManualVoteTemplate" class="btn btn-outline-primary w-100">
                                <i class="fas fa-download me-2"></i>Download CSV Template
                            </a>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-outline-info w-100" onclick="showBulkPreview()">
                                <i class="fas fa-eye me-2"></i>Preview Import
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload me-2"></i>Import Votes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Admin Vehicle Selector Modal -->
<div class="modal fade" id="adminVehicleSelectorModal" tabindex="-1" aria-labelledby="adminVehicleSelectorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="adminVehicleSelectorModalLabel">Select Vehicle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="adminVehicleSearchInput" placeholder="Search vehicles by registration, make, model, or year...">
                </div>
                <div id="adminVehicleSearchResults" class="list-group" style="max-height: 400px; overflow-y: auto;">
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Show Selector Modal -->
<div class="modal fade" id="showSelectorModal" tabindex="-1" aria-labelledby="showSelectorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="showSelectorModalLabel">Select Show</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="showSearchInput" placeholder="Search shows by name, date, or location...">
                </div>
                <div id="showSearchResults" class="list-group" style="max-height: 400px; overflow-y: auto;">
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Select all checkboxes functionality
document.getElementById('selectAll')?.addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('input[name="vote_ids[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = this.checked);
});

// Confirm bulk actions
document.querySelectorAll('button[name="bulk_action"]').forEach(button => {
    button.addEventListener('click', function(e) {
        const action = this.value;
        const checkedBoxes = document.querySelectorAll('input[name="vote_ids[]"]:checked');

        if (checkedBoxes.length === 0) {
            e.preventDefault();
            alert('Please select at least one vote.');
            return;
        }

        if (action === 'delete') {
            if (!confirm(`Are you sure you want to delete ${checkedBoxes.length} vote(s)? This action cannot be undone.`)) {
                e.preventDefault();
            }
        }
    });
});

// Show selector functionality
function openShowSelector() {
    const modal = new bootstrap.Modal(document.getElementById('showSelectorModal'));
    modal.show();
    loadShows('');
}

// AJAX show search
let searchTimeout;
document.getElementById('showSearchInput')?.addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const query = this.value;

    searchTimeout = setTimeout(() => {
        loadShows(query);
    }, 300);
});

function loadShows(query) {
    const resultsContainer = document.getElementById('showSearchResults');
    resultsContainer.innerHTML = '<div class="text-center py-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

    // AJAX call to search shows
    fetch(`<?php echo BASE_URL; ?>/admin/searchShows?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            let html = '';

            // Add "All Shows" option
            html += `<a href="<?php echo BASE_URL; ?>/admin/voteManagement" class="list-group-item list-group-item-action ${!<?php echo $data['show_id'] ? 'false' : 'true'; ?> ? 'active' : ''}">
                        <i class="fas fa-globe me-2"></i>All Shows
                        <small class="text-muted d-block">View votes from all shows</small>
                     </a>`;

            if (data.shows && data.shows.length > 0) {
                data.shows.forEach(show => {
                    const isActive = <?php echo $data['show_id']; ?> == show.id;
                    html += `<a href="<?php echo BASE_URL; ?>/admin/voteManagement/${show.id}" class="list-group-item list-group-item-action ${isActive ? 'active' : ''}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${show.name}</h6>
                                        <small class="text-muted">${show.start_date} - ${show.location || 'No location'}</small>
                                    </div>
                                    <small class="text-muted">${show.vote_count || 0} votes</small>
                                </div>
                             </a>`;
                });
            } else {
                html += '<div class="text-center py-3 text-muted">No shows found</div>';
            }

            resultsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading shows:', error);
            resultsContainer.innerHTML = '<div class="text-center py-3 text-danger">Error loading shows</div>';
        });
}

// Individual vote actions
function approveVote(voteId) {
    if (confirm('Approve this vote?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo BASE_URL; ?>/admin/processBulkVoteAction';

        form.innerHTML = `
            <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
            <input type="hidden" name="show_id" value="<?php echo $data['show_id']; ?>">
            <input type="hidden" name="bulk_action" value="approve">
            <input type="hidden" name="vote_ids[]" value="${voteId}">
        `;

        document.body.appendChild(form);
        form.submit();
    }
}

function deleteVote(voteId) {
    if (confirm('Are you sure you want to delete this vote? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo BASE_URL; ?>/admin/processBulkVoteAction';

        form.innerHTML = `
            <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
            <input type="hidden" name="show_id" value="<?php echo $data['show_id']; ?>">
            <input type="hidden" name="bulk_action" value="delete">
            <input type="hidden" name="vote_ids[]" value="${voteId}">
        `;

        document.body.appendChild(form);
        form.submit();
    }
}

// Admin manual vote functions
function openAdminManualVoteModal() {
    const modal = new bootstrap.Modal(document.getElementById('adminManualVoteModal'));
    modal.show();
}

function openBulkManualVoteModal() {
    const modal = new bootstrap.Modal(document.getElementById('bulkManualVoteModal'));
    modal.show();
}

// Admin vehicle selector functionality
function openAdminVehicleSelector() {
    const modal = new bootstrap.Modal(document.getElementById('adminVehicleSelectorModal'));
    modal.show();
    loadAdminVehicles('');
}

// AJAX admin vehicle search
let adminVehicleSearchTimeout;
document.getElementById('adminVehicleSearchInput')?.addEventListener('input', function() {
    clearTimeout(adminVehicleSearchTimeout);
    const query = this.value;

    adminVehicleSearchTimeout = setTimeout(() => {
        loadAdminVehicles(query);
    }, 300);
});

function loadAdminVehicles(query) {
    const resultsContainer = document.getElementById('adminVehicleSearchResults');
    resultsContainer.innerHTML = '<div class="text-center py-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

    // AJAX call to search vehicles for current show
    const showId = <?php echo $data['show_id'] ?? 'null'; ?>;
    if (!showId) {
        resultsContainer.innerHTML = '<div class="text-center py-3 text-danger">Please select a show first</div>';
        return;
    }

    fetch(`<?php echo BASE_URL; ?>/admin/searchShowVehicles/${showId}?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            let html = '';

            if (data.vehicles && data.vehicles.length > 0) {
                data.vehicles.forEach(vehicle => {
                    html += `<a href="#" class="list-group-item list-group-item-action" onclick="selectAdminVehicle(${vehicle.registration_id}, '${vehicle.vehicle_info}', '${vehicle.registration_number}')">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${vehicle.vehicle_info}</h6>
                                        <small class="text-muted">Registration #${vehicle.registration_number}</small>
                                    </div>
                                    <small class="text-muted">${vehicle.vote_count || 0} votes</small>
                                </div>
                             </a>`;
                });
            } else {
                html += '<div class="text-center py-3 text-muted">No vehicles found</div>';
            }

            resultsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading vehicles:', error);
            resultsContainer.innerHTML = '<div class="text-center py-3 text-danger">Error loading vehicles</div>';
        });
}

function selectAdminVehicle(vehicleId, vehicleInfo, regNumber) {
    document.getElementById('admin_vehicle_search').value = `${vehicleInfo} (Reg #${regNumber})`;
    document.getElementById('admin_selected_vehicle_id').value = vehicleId;

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('adminVehicleSelectorModal'));
    modal.hide();
}

function showBulkPreview() {
    const fileInput = document.getElementById('bulk_csv_file');
    if (!fileInput.files[0]) {
        alert('Please select a CSV file first');
        return;
    }

    // TODO: Implement CSV preview functionality
    alert('CSV preview functionality coming soon!');
}

// Auto-expand filters if any are set
document.addEventListener('DOMContentLoaded', function() {
    const hasFilters = <?php echo json_encode(array_filter($data['filters'], function($v) { return !empty($v); })); ?>;
    if (Object.keys(hasFilters).length > 0) {
        const filtersCollapse = new bootstrap.Collapse(document.getElementById('filtersCollapse'), {
            show: true
        });
    }
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>

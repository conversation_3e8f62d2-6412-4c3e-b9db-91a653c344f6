<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h1 class="h4 mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Appeal Blocked Vote
                    </h1>
                </div>
                <div class="card-body">
                    <?php flash('appeal_message'); ?>
                    
                    <!-- Appeal Information -->
                    <div class="alert alert-info">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            Why Was My Vote Blocked?
                        </h5>
                        <p class="mb-2">Your vote was automatically blocked by our security system due to one or more of these factors:</p>
                        <ul class="mb-0">
                            <li><strong>Location:</strong> You appeared to be outside the show area</li>
                            <li><strong>Device:</strong> Multiple votes detected from the same device</li>
                            <li><strong>Network:</strong> VPN, proxy, or suspicious IP address detected</li>
                            <li><strong>Timing:</strong> Vote submitted too quickly or outside voting hours</li>
                            <li><strong>Pattern:</strong> Voting behavior matched known fraud patterns</li>
                        </ul>
                    </div>

                    <!-- Appeal Form -->
                    <form method="post" action="<?php echo URLROOT; ?>/show/submitVoteAppeal">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" name="show_id" value="<?php echo $data['show_id']; ?>">
                        <input type="hidden" name="registration_id" value="<?php echo $data['registration_id']; ?>">
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Your Information</h5>
                                
                                <div class="mb-3">
                                    <label for="voter_name" class="form-label">Your Full Name *</label>
                                    <input type="text" class="form-control" id="voter_name" name="voter_name" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="voter_email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="voter_email" name="voter_email" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="voter_phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="voter_phone" name="voter_phone" placeholder="Optional">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Vehicle Information</h5>
                                
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title"><?php echo $data['vehicle']->year . ' ' . $data['vehicle']->make . ' ' . $data['vehicle']->model; ?></h6>
                                        <p class="card-text">
                                            <strong>Registration #:</strong> <?php echo $data['registration']->registration_number; ?><br>
                                            <strong>Show:</strong> <?php echo $data['show']->name; ?><br>
                                            <strong>Date:</strong> <?php echo date('M j, Y', strtotime($data['show']->start_date)); ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5 class="text-primary mb-3">Appeal Details</h5>
                            
                            <div class="mb-3">
                                <label for="appeal_reason" class="form-label">Why should your vote be counted? *</label>
                                <select class="form-select" id="appeal_reason" name="appeal_reason" required onchange="toggleOtherReason()">
                                    <option value="">Select a reason...</option>
                                    <option value="was_at_show">I was physically at the show</option>
                                    <option value="gps_error">GPS/location error on my device</option>
                                    <option value="network_issue">Network or connectivity issue</option>
                                    <option value="shared_device">Shared device with family/friends</option>
                                    <option value="technical_difficulty">Technical difficulty with voting</option>
                                    <option value="other">Other (please explain)</option>
                                </select>
                            </div>
                            
                            <div class="mb-3" id="otherReasonDiv" style="display: none;">
                                <label for="other_reason" class="form-label">Please explain:</label>
                                <textarea class="form-control" id="other_reason" name="other_reason" rows="3"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="appeal_explanation" class="form-label">Additional Details *</label>
                                <textarea class="form-control" id="appeal_explanation" name="appeal_explanation" rows="4" 
                                         placeholder="Please provide any additional information that would help us verify your legitimate vote. For example: what time you arrived at the show, who you came with, what you remember about the event, etc." required></textarea>
                                <small class="text-muted">The more details you provide, the easier it is for us to verify your appeal.</small>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5 class="text-primary mb-3">Verification (Optional)</h5>
                            <p class="text-muted mb-3">Providing any of the following can help verify your attendance:</p>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="witness_name" class="form-label">Witness Name</label>
                                        <input type="text" class="form-control" id="witness_name" name="witness_name" 
                                               placeholder="Someone who saw you at the show">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="witness_contact" class="form-label">Witness Contact</label>
                                        <input type="text" class="form-control" id="witness_contact" name="witness_contact" 
                                               placeholder="Phone or email">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="social_media_proof" class="form-label">Social Media Post</label>
                                <input type="url" class="form-control" id="social_media_proof" name="social_media_proof" 
                                       placeholder="Link to Facebook, Instagram, etc. post from the show">
                            </div>
                            
                            <div class="mb-3">
                                <label for="receipt_or_ticket" class="form-label">Receipt/Ticket Number</label>
                                <input type="text" class="form-control" id="receipt_or_ticket" name="receipt_or_ticket" 
                                       placeholder="Entry fee receipt, parking ticket, etc.">
                            </div>
                        </div>

                        <!-- Terms -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="appeal_terms" name="appeal_terms" required>
                                <label class="form-check-label" for="appeal_terms">
                                    I certify that the information provided is true and accurate. I understand that false appeals may result in permanent voting restrictions. *
                                </label>
                            </div>
                        </div>

                        <!-- Submit -->
                        <div class="d-flex justify-content-between">
                            <a href="<?php echo URLROOT; ?>/show/details/<?php echo $data['show']->id; ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i> Back to Show
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-paper-plane me-2"></i> Submit Appeal
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Appeal Process Info -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        What Happens Next?
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Review Process</h6>
                            <ul class="mb-0">
                                <li>Appeals are reviewed within 24-48 hours</li>
                                <li>Show coordinators verify your information</li>
                                <li>Additional verification may be requested</li>
                                <li>You'll be notified of the decision via email</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Possible Outcomes</h6>
                            <ul class="mb-0">
                                <li><strong>Approved:</strong> Your vote will be counted</li>
                                <li><strong>Denied:</strong> Vote remains blocked with explanation</li>
                                <li><strong>Pending:</strong> More information needed</li>
                                <li><strong>Manual Entry:</strong> Staff will enter your vote</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleOtherReason() {
    const select = document.getElementById('appeal_reason');
    const otherDiv = document.getElementById('otherReasonDiv');
    const otherTextarea = document.getElementById('other_reason');
    
    if (select.value === 'other') {
        otherDiv.style.display = 'block';
        otherTextarea.required = true;
    } else {
        otherDiv.style.display = 'none';
        otherTextarea.required = false;
        otherTextarea.value = '';
    }
}

// Prevent double submission
document.querySelector('form').addEventListener('submit', function(e) {
    const submitBtn = document.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Submitting...';
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>

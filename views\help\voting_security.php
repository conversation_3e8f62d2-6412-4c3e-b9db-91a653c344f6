<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Fan Voting Security & Fair Play
                    </h1>
                </div>
                <div class="card-body">
                    
                    <!-- Vote Appeal Section -->
                    <div class="alert alert-warning mb-4">
                        <h5 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Was Your Vote Blocked?
                        </h5>
                        <p class="mb-3">
                            If our security system blocked your legitimate vote, you can appeal the decision.
                            We review all appeals manually to ensure fair treatment.
                        </p>
                        <div class="d-grid gap-2 d-md-flex">
                            <a href="#appeal-process" class="btn btn-warning me-md-2">
                                <i class="fas fa-scroll me-2"></i>Learn About Appeals
                            </a>
                            <button type="button" class="btn btn-outline-warning" onclick="showAppealForm()">
                                <i class="fas fa-plus me-2"></i>Submit Appeal
                            </button>
                        </div>
                    </div>

                    <!-- Introduction -->
                    <div class="alert alert-info">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            Our Commitment to Fair Voting
                        </h5>
                        <p class="mb-0">
                            We use advanced security measures to ensure every vote is legitimate and fair.
                            Our multi-layer protection system prevents fraud while protecting your privacy.
                        </p>
                    </div>

                    <!-- How Voting Security Works -->
                    <div class="row mb-5">
                        <div class="col-12">
                            <h3 class="text-primary mb-4">
                                <i class="fas fa-cogs me-2"></i>
                                How Our Voting Security Works
                            </h3>
                            
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100 border-success">
                                        <div class="card-body">
                                            <h5 class="card-title text-success">
                                                <i class="fas fa-map-marker-alt me-2"></i>
                                                Location Verification
                                            </h5>
                                            <p class="card-text">
                                                We verify you're actually at the show using GPS technology. 
                                                This prevents remote vote manipulation and ensures only 
                                                attendees can vote.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100 border-info">
                                        <div class="card-body">
                                            <h5 class="card-title text-info">
                                                <i class="fas fa-fingerprint me-2"></i>
                                                Device Fingerprinting
                                            </h5>
                                            <p class="card-text">
                                                Each device gets a unique "fingerprint" to prevent 
                                                multiple votes from the same device, even if using 
                                                different browsers or incognito mode.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100 border-warning">
                                        <div class="card-body">
                                            <h5 class="card-title text-warning">
                                                <i class="fas fa-tachometer-alt me-2"></i>
                                                Rate Limiting
                                            </h5>
                                            <p class="card-text">
                                                Our system detects and prevents rapid-fire voting 
                                                attempts and suspicious patterns that indicate 
                                                automated voting bots.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100 border-primary">
                                        <div class="card-body">
                                            <h5 class="card-title text-primary">
                                                <i class="fab fa-facebook me-2"></i>
                                                Social Verification
                                            </h5>
                                            <p class="card-text">
                                                Facebook votes are verified for authentic profiles 
                                                with sufficient account age and friend connections 
                                                to prevent fake account voting.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- What Happens When You Vote -->
                    <div class="row mb-5">
                        <div class="col-12">
                            <h3 class="text-primary mb-4">
                                <i class="fas fa-vote-yea me-2"></i>
                                What Happens When You Vote
                            </h3>
                            
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h5>1. Location Check</h5>
                                        <p>We verify you're within the show area using GPS (with your permission).</p>
                                    </div>
                                </div>
                                
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-info"></div>
                                    <div class="timeline-content">
                                        <h5>2. Device Analysis</h5>
                                        <p>Your device gets a unique fingerprint to prevent duplicate voting.</p>
                                    </div>
                                </div>
                                
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-warning"></div>
                                    <div class="timeline-content">
                                        <h5>3. Security Scoring</h5>
                                        <p>Our AI analyzes your vote for any suspicious patterns or behaviors.</p>
                                    </div>
                                </div>
                                
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h5>4. Vote Recorded</h5>
                                        <p>If everything checks out, your vote is counted and you'll see a confirmation.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Privacy Protection -->
                    <div class="row mb-5">
                        <div class="col-12">
                            <h3 class="text-primary mb-4">
                                <i class="fas fa-user-shield me-2"></i>
                                Your Privacy is Protected
                            </h3>
                            
                            <div class="alert alert-success">
                                <h5 class="alert-heading">What We Collect vs. What We Show</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-success">✅ For Security (Private)</h6>
                                        <ul class="mb-0">
                                            <li>Your exact GPS location</li>
                                            <li>Your IP address</li>
                                            <li>Device characteristics</li>
                                            <li>Voting timestamps</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-info">👁️ What Others See (Public)</h6>
                                        <ul class="mb-0">
                                            <li>Approximate distance from show</li>
                                            <li>Verification status (GPS/Facebook)</li>
                                            <li>Vote method used</li>
                                            <li>General time of vote</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Troubleshooting -->
                    <div class="row mb-5">
                        <div class="col-12">
                            <h3 class="text-primary mb-4">
                                <i class="fas fa-question-circle me-2"></i>
                                Troubleshooting Common Issues
                            </h3>
                            
                            <div class="accordion" id="troubleshootingAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#gpsIssue">
                                            My vote was blocked - "Location verification failed"
                                        </button>
                                    </h2>
                                    <div id="gpsIssue" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                        <div class="accordion-body">
                                            <p><strong>This usually means:</strong></p>
                                            <ul>
                                                <li>You're outside the show area (must be within 0.5 miles)</li>
                                                <li>GPS is disabled on your device</li>
                                                <li>You're using a VPN or proxy</li>
                                            </ul>
                                            <p><strong>Solutions:</strong></p>
                                            <ul>
                                                <li>Make sure you're physically at the show</li>
                                                <li>Enable location services for your browser</li>
                                                <li>Disable any VPN or proxy services</li>
                                                <li>Ask show staff for manual vote assistance</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#duplicateVote">
                                            "You have already voted" error
                                        </button>
                                    </h2>
                                    <div id="duplicateVote" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                        <div class="accordion-body">
                                            <p><strong>This means:</strong></p>
                                            <ul>
                                                <li>Your device or IP address has already been used to vote</li>
                                                <li>Someone else at your location may have voted</li>
                                            </ul>
                                            <p><strong>Solutions:</strong></p>
                                            <ul>
                                                <li>Each person needs their own device to vote</li>
                                                <li>Use Facebook login for additional verification</li>
                                                <li>Ask show staff for manual vote assistance</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flaggedVote">
                                            My vote is "under review"
                                        </button>
                                    </h2>
                                    <div id="flaggedVote" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                        <div class="accordion-body">
                                            <p><strong>This means:</strong></p>
                                            <ul>
                                                <li>Our security system detected unusual patterns</li>
                                                <li>Your vote is being reviewed by show staff</li>
                                                <li>This is a precautionary measure to ensure fairness</li>
                                            </ul>
                                            <p><strong>What happens next:</strong></p>
                                            <ul>
                                                <li>Show coordinators will review your vote</li>
                                                <li>Legitimate votes are typically approved within 24 hours</li>
                                                <li>You'll be notified of the decision</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Manual Vote Assistance -->
                    <div class="row mb-5">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h5 class="alert-heading">
                                    <i class="fas fa-hands-helping me-2"></i>
                                    Need Help Voting?
                                </h5>
                                <p>
                                    If you're having trouble voting online, show staff can help you submit a manual vote. 
                                    Just find any staff member, coordinator, or admin at the show and they can assist you.
                                </p>
                                <p class="mb-0">
                                    <strong>Manual votes require:</strong> Your name and phone number or email address for verification.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Contact -->
                    <div class="row">
                        <div class="col-12 text-center">
                            <h4 class="text-primary mb-3">Still Have Questions?</h4>
                            <p class="mb-4">Our security measures are designed to be fair and transparent while protecting everyone's privacy.</p>
                            <div class="btn-group">
                                <a href="<?php echo URLROOT; ?>/contact" class="btn btn-primary">
                                    <i class="fas fa-envelope me-2"></i> Contact Support
                                </a>
                                <a href="<?php echo URLROOT; ?>/help" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i> Back to Help
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -19px;
    top: 30px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #dee2e6;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-content h5 {
    margin-bottom: 10px;
    color: #495057;
}

.timeline-content p {
    margin-bottom: 0;
    color: #6c757d;
}
</style>

<!-- Appeal Form Modal -->
<div class="modal fade" id="appealModal" tabindex="-1" aria-labelledby="appealModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="appealModalLabel">
                    <i class="fas fa-gavel me-2"></i>Appeal a Blocked Vote
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Before You Appeal</h6>
                    <p class="mb-0">To submit an appeal, you'll need the show name and vehicle information. You'll be redirected to the appeal form.</p>
                </div>

                <form id="appealRedirectForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="showSelect" class="form-label">Select Show *</label>
                            <select class="form-select" id="showSelect" required>
                                <option value="">Choose a show...</option>
                                <!-- Shows will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="vehicleSelect" class="form-label">Select Vehicle *</label>
                            <select class="form-select" id="vehicleSelect" required disabled>
                                <option value="">First select a show...</option>
                            </select>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-external-link-alt me-2"></i>Go to Appeal Form
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function showAppealForm() {
    // Load current shows for appeal
    loadShowsForAppeal();

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('appealModal'));
    modal.show();
}

function loadShowsForAppeal() {
    // For now, show a simple message. In production, you'd load actual shows via AJAX
    const showSelect = document.getElementById('showSelect');
    showSelect.innerHTML = '<option value="">Loading shows...</option>';

    // Simulate loading shows (replace with actual AJAX call)
    setTimeout(() => {
        showSelect.innerHTML = `
            <option value="">Choose a show...</option>
            <option value="contact">I need help finding my show</option>
        `;
    }, 500);
}

// Handle show selection
document.getElementById('showSelect').addEventListener('change', function() {
    const vehicleSelect = document.getElementById('vehicleSelect');

    if (this.value === 'contact') {
        vehicleSelect.innerHTML = '<option value="contact">Contact support for assistance</option>';
        vehicleSelect.disabled = false;
    } else if (this.value) {
        vehicleSelect.innerHTML = '<option value="">Loading vehicles...</option>';
        vehicleSelect.disabled = false;

        // Simulate loading vehicles
        setTimeout(() => {
            vehicleSelect.innerHTML = `
                <option value="">Choose a vehicle...</option>
                <option value="help">I need help finding my vehicle</option>
            `;
        }, 500);
    } else {
        vehicleSelect.innerHTML = '<option value="">First select a show...</option>';
        vehicleSelect.disabled = true;
    }
});

// Handle form submission
document.getElementById('appealRedirectForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const showValue = document.getElementById('showSelect').value;
    const vehicleValue = document.getElementById('vehicleSelect').value;

    if (showValue === 'contact' || vehicleValue === 'help') {
        // Redirect to contact/support
        window.open('<?php echo BASE_URL; ?>/user/help', '_blank');
    } else if (showValue && vehicleValue) {
        // Redirect to appeal form (you'll need to implement the actual appeal form routing)
        alert('Appeal form functionality will be available once you complete the database setup. For now, please contact support.');
    }
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>

<?php
/**
 * Voting Security Data Cleanup Cron Job
 * Cleans up old voting security data based on retention settings
 * 
 * Run this script daily via cron:
 * 0 2 * * * /usr/bin/php /path/to/your/site/cron/cleanup_voting_security_data.php
 */

// Set the path to the application root
define('APPROOT', dirname(dirname(__FILE__)));

// Include necessary files
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/models/VotingSecurityModel.php';

// Start logging
$logFile = APPROOT . '/logs/voting_security_cleanup.log';
$startTime = date('Y-m-d H:i:s');

function logMessage($message) {
    global $logFile, $startTime;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[{$timestamp}] {$message}\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    echo $logEntry;
}

logMessage("Starting voting security data cleanup...");

try {
    $db = new Database();
    $securityModel = new VotingSecurityModel();
    
    // Get all shows with their security settings
    $db->query('SELECT s.id, s.name, vss.data_retention_days 
               FROM shows s 
               LEFT JOIN voting_security_settings vss ON s.id = vss.show_id 
               WHERE s.end_date < NOW()');
    $shows = $db->resultSet();
    
    $totalCleaned = 0;
    $showsCleaned = 0;
    
    foreach ($shows as $show) {
        $retentionDays = $show->data_retention_days ?? 30; // Default to 30 days
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$retentionDays} days"));
        
        logMessage("Processing show: {$show->name} (ID: {$show->id}) - Retention: {$retentionDays} days");
        
        // Clean up old voting sessions
        $db->query('DELETE FROM voting_sessions 
                   WHERE show_id = :show_id AND created_at < :cutoff_date');
        $db->bind(':show_id', $show->id);
        $db->bind(':cutoff_date', $cutoffDate);
        $db->execute();
        $sessionsDeleted = $db->rowCount();
        
        // Clean up old fraud detection patterns
        $db->query('DELETE FROM fraud_detection_patterns 
                   WHERE show_id = :show_id AND created_at < :cutoff_date');
        $db->bind(':show_id', $show->id);
        $db->bind(':cutoff_date', $cutoffDate);
        $db->execute();
        $patternsDeleted = $db->rowCount();
        
        // Clean up old voting alerts (keep acknowledged ones longer)
        $db->query('DELETE FROM voting_alerts 
                   WHERE show_id = :show_id 
                   AND created_at < :cutoff_date 
                   AND acknowledged = TRUE');
        $db->bind(':show_id', $show->id);
        $db->bind(':cutoff_date', $cutoffDate);
        $db->execute();
        $alertsDeleted = $db->rowCount();
        
        // Clean up old honeypot interactions
        $db->query('DELETE FROM honeypot_votes 
                   WHERE show_id = :show_id AND detected_at < :cutoff_date');
        $db->bind(':show_id', $show->id);
        $db->bind(':cutoff_date', $cutoffDate);
        $db->execute();
        $honeypotDeleted = $db->rowCount();
        
        // Anonymize old fan votes (keep vote counts but remove personal data)
        $db->query('UPDATE fan_votes SET 
                   voter_ip = "XXX.XXX.XXX.XXX",
                   device_fingerprint = NULL,
                   user_agent = NULL,
                   latitude = NULL,
                   longitude = NULL,
                   manual_voter_name = "Anonymous",
                   manual_voter_contact = "Anonymized",
                   session_id = NULL
                   WHERE show_id = :show_id 
                   AND created_at < :cutoff_date
                   AND voter_ip != "XXX.XXX.XXX.XXX"');
        $db->bind(':show_id', $show->id);
        $db->bind(':cutoff_date', $cutoffDate);
        $db->execute();
        $votesAnonymized = $db->rowCount();
        
        $showTotal = $sessionsDeleted + $patternsDeleted + $alertsDeleted + $honeypotDeleted;
        $totalCleaned += $showTotal;
        
        if ($showTotal > 0 || $votesAnonymized > 0) {
            $showsCleaned++;
            logMessage("  - Deleted: {$sessionsDeleted} sessions, {$patternsDeleted} patterns, {$alertsDeleted} alerts, {$honeypotDeleted} honeypot records");
            logMessage("  - Anonymized: {$votesAnonymized} fan votes");
        } else {
            logMessage("  - No data to clean for this show");
        }
    }
    
    // Clean up old IP geolocation cache
    $cacheFile = APPROOT . '/cache/ip_geolocation_cache.json';
    if (file_exists($cacheFile)) {
        $cacheData = json_decode(file_get_contents($cacheFile), true);
        if ($cacheData) {
            $originalCount = count($cacheData);
            $cutoff = time() - (7 * 86400); // 7 days
            
            foreach ($cacheData as $ip => $data) {
                if ($data['timestamp'] < $cutoff) {
                    unset($cacheData[$ip]);
                }
            }
            
            $cleanedCount = $originalCount - count($cacheData);
            if ($cleanedCount > 0) {
                file_put_contents($cacheFile, json_encode($cacheData));
                logMessage("Cleaned {$cleanedCount} old IP geolocation cache entries");
            }
        }
    }
    
    // Clean up old security reports
    $db->query('DELETE FROM security_reports 
               WHERE generated_at < DATE_SUB(NOW(), INTERVAL 90 DAY)');
    $db->execute();
    $reportsDeleted = $db->rowCount();
    
    if ($reportsDeleted > 0) {
        logMessage("Deleted {$reportsDeleted} old security reports");
    }
    
    // Clean up old vote appeals (keep for 1 year)
    $db->query('DELETE FROM vote_appeals 
               WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR)');
    $db->execute();
    $appealsDeleted = $db->rowCount();
    
    if ($appealsDeleted > 0) {
        logMessage("Deleted {$appealsDeleted} old vote appeals");
    }
    
    // Summary
    logMessage("Cleanup completed successfully!");
    logMessage("Summary:");
    logMessage("  - Shows processed: " . count($shows));
    logMessage("  - Shows with data cleaned: {$showsCleaned}");
    logMessage("  - Total security records cleaned: {$totalCleaned}");
    logMessage("  - Security reports deleted: {$reportsDeleted}");
    logMessage("  - Vote appeals deleted: {$appealsDeleted}");
    
    // Update cleanup statistics
    $db->query('INSERT INTO system_logs (log_type, message, created_at) 
               VALUES ("voting_security_cleanup", :message, NOW())');
    $db->bind(':message', "Cleaned {$totalCleaned} security records from {$showsCleaned} shows");
    $db->execute();
    
} catch (Exception $e) {
    $errorMessage = "Error during voting security cleanup: " . $e->getMessage();
    logMessage($errorMessage);
    
    // Log error to database if possible
    try {
        $db->query('INSERT INTO system_logs (log_type, message, created_at) 
                   VALUES ("voting_security_cleanup_error", :message, NOW())');
        $db->bind(':message', $errorMessage);
        $db->execute();
    } catch (Exception $dbError) {
        logMessage("Failed to log error to database: " . $dbError->getMessage());
    }
    
    exit(1);
}

$endTime = date('Y-m-d H:i:s');
$duration = strtotime($endTime) - strtotime($startTime);
logMessage("Cleanup completed in {$duration} seconds");

exit(0);
?>

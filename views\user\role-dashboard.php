<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row">
        <div class="col-12">
            <!-- Role Header -->
            <div class="role-header mb-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <?php
                            $userRole = $_SESSION['user_role'] ?? 'attendee';
                            $roleConfig = [
                                'attendee' => [
                                    'icon' => 'fas fa-user',
                                    'color' => 'primary',
                                    'title' => 'Car Show Attendee',
                                    'description' => 'Find events, register vehicles, and compete in car shows'
                                ],
                                'coordinator' => [
                                    'icon' => 'fas fa-crown',
                                    'color' => 'success',
                                    'title' => 'Show Coordinator',
                                    'description' => 'Host and manage car shows, assign judges, and oversee events'
                                ],
                                'judge' => [
                                    'icon' => 'fas fa-gavel',
                                    'color' => 'warning',
                                    'title' => 'Car Show Judge',
                                    'description' => 'Evaluate vehicles and score competitions professionally'
                                ],
                                'admin' => [
                                    'icon' => 'fas fa-cog',
                                    'color' => 'danger',
                                    'title' => 'System Administrator',
                                    'description' => 'Manage platform settings and oversee all operations'
                                ]
                            ];
                            
                            $config = $roleConfig[$userRole] ?? $roleConfig['attendee'];
                            ?>
                            
                            <div class="role-badge bg-<?php echo $config['color']; ?> bg-gradient text-white rounded-circle d-inline-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                <i class="<?php echo $config['icon']; ?> fa-2x"></i>
                            </div>
                            <div>
                                <h1 class="h3 mb-1">Welcome back, <?php echo htmlspecialchars($_SESSION['user_name'] ?? 'User'); ?>!</h1>
                                <p class="text-muted mb-0">
                                    <span class="badge bg-<?php echo $config['color']; ?> me-2"><?php echo $config['title']; ?></span>
                                    <?php echo $config['description']; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="role-actions">
                            <?php if ($userRole === 'attendee'): ?>
                                <a href="<?php echo BASE_URL; ?>/calendar" class="btn btn-primary btn-sm me-2">
                                    <i class="fas fa-search me-1"></i>Find Events
                                </a>
                                <a href="<?php echo BASE_URL; ?>/home/<USER>" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-plus-circle me-1"></i>Host a Show
                                </a>
                            <?php elseif ($userRole === 'coordinator'): ?>
                                <a href="<?php echo BASE_URL; ?>/user/createShow" class="btn btn-success btn-sm me-2">
                                    <i class="fas fa-plus-circle me-1"></i>New Show
                                </a>
                                <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-tachometer-alt me-1"></i>Coordinator Panel
                                </a>
                            <?php elseif ($userRole === 'judge'): ?>
                                <a href="<?php echo BASE_URL; ?>/judge/dashboard" class="btn btn-warning btn-sm me-2">
                                    <i class="fas fa-gavel me-1"></i>Judge Panel
                                </a>
                                <a href="<?php echo BASE_URL; ?>/judge/tutorial" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-book me-1"></i>Judging Guide
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role-Specific Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-<?php echo $config['color']; ?>">
                        <div class="card-header bg-<?php echo $config['color']; ?> text-white">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions for <?php echo $config['title']; ?>
                            </h4>
                        </div>
                        <div class="card-body">
                            <?php if ($userRole === 'attendee'): ?>
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <div class="quick-action-card h-100 p-3 border rounded text-center">
                                            <i class="fas fa-calendar-alt fa-2x text-primary mb-2"></i>
                                            <h6>Find Car Shows</h6>
                                            <p class="small text-muted mb-3">Browse upcoming events near you</p>
                                            <a href="<?php echo BASE_URL; ?>/calendar" class="btn btn-primary btn-sm">Browse Events</a>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="quick-action-card h-100 p-3 border rounded text-center">
                                            <i class="fas fa-car fa-2x text-success mb-2"></i>
                                            <h6>Register Vehicle</h6>
                                            <p class="small text-muted mb-3">Enter your car in competitions</p>
                                            <a href="<?php echo BASE_URL; ?>/show" class="btn btn-success btn-sm">Register Now</a>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="quick-action-card h-100 p-3 border rounded text-center">
                                            <i class="fas fa-crown fa-2x text-warning mb-2"></i>
                                            <h6>Host Your Own Show</h6>
                                            <p class="small text-muted mb-3">Become a show coordinator</p>
                                            <a href="<?php echo BASE_URL; ?>/home/<USER>" class="btn btn-warning btn-sm">Learn How</a>
                                        </div>
                                    </div>
                                </div>
                            <?php elseif ($userRole === 'coordinator'): ?>
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <div class="quick-action-card h-100 p-3 border rounded text-center">
                                            <i class="fas fa-plus-circle fa-2x text-success mb-2"></i>
                                            <h6>Create New Show</h6>
                                            <p class="small text-muted mb-3">Start planning your next event</p>
                                            <a href="<?php echo BASE_URL; ?>/user/createShow" class="btn btn-success btn-sm">Create Show</a>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="quick-action-card h-100 p-3 border rounded text-center">
                                            <i class="fas fa-users fa-2x text-info mb-2"></i>
                                            <h6>Manage Judges</h6>
                                            <p class="small text-muted mb-3">Assign and coordinate judges</p>
                                            <a href="<?php echo BASE_URL; ?>/coordinator/judges" class="btn btn-info btn-sm">Manage Judges</a>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="quick-action-card h-100 p-3 border rounded text-center">
                                            <i class="fas fa-clipboard-list fa-2x text-primary mb-2"></i>
                                            <h6>View Registrations</h6>
                                            <p class="small text-muted mb-3">Monitor event registrations</p>
                                            <a href="<?php echo BASE_URL; ?>/coordinator/registrations" class="btn btn-primary btn-sm">View All</a>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="quick-action-card h-100 p-3 border rounded text-center">
                                            <i class="fas fa-flag-checkered fa-2x text-danger mb-2"></i>
                                            <h6>Complete Shows</h6>
                                            <p class="small text-muted mb-3">Finalize results and winners</p>
                                            <a href="<?php echo BASE_URL; ?>/coordinator/activeShows" class="btn btn-danger btn-sm">View Active</a>
                                        </div>
                                    </div>
                                </div>
                            <?php elseif ($userRole === 'judge'): ?>
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <div class="quick-action-card h-100 p-3 border rounded text-center">
                                            <i class="fas fa-gavel fa-2x text-warning mb-2"></i>
                                            <h6>Active Judging</h6>
                                            <p class="small text-muted mb-3">Shows you're currently judging</p>
                                            <a href="<?php echo BASE_URL; ?>/judge/dashboard" class="btn btn-warning btn-sm">Judge Now</a>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="quick-action-card h-100 p-3 border rounded text-center">
                                            <i class="fas fa-qrcode fa-2x text-info mb-2"></i>
                                            <h6>Scan QR Codes</h6>
                                            <p class="small text-muted mb-3">Quick access to judging forms</p>
                                            <a href="<?php echo BASE_URL; ?>/judge/scan" class="btn btn-info btn-sm">Start Scanning</a>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="quick-action-card h-100 p-3 border rounded text-center">
                                            <i class="fas fa-graduation-cap fa-2x text-success mb-2"></i>
                                            <h6>Judging Tutorial</h6>
                                            <p class="small text-muted mb-3">Learn the judging process</p>
                                            <a href="<?php echo BASE_URL; ?>/judge/tutorial" class="btn btn-success btn-sm">View Tutorial</a>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Role Goals -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-target me-2"></i>Your Current Goals & Next Steps
                            </h4>
                        </div>
                        <div class="card-body">
                            <?php if ($userRole === 'attendee'): ?>
                                <div class="goals-section">
                                    <h5 class="text-primary mb-3">As an Attendee, here's what you can accomplish:</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="goal-item mb-3">
                                                <div class="d-flex align-items-start">
                                                    <i class="fas fa-search text-primary me-3 mt-1"></i>
                                                    <div>
                                                        <h6 class="mb-1">Find Perfect Car Shows</h6>
                                                        <p class="small text-muted mb-0">Use our map and filters to discover events that match your interests and location.</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="goal-item mb-3">
                                                <div class="d-flex align-items-start">
                                                    <i class="fas fa-car text-success me-3 mt-1"></i>
                                                    <div>
                                                        <h6 class="mb-1">Showcase Your Vehicle</h6>
                                                        <p class="small text-muted mb-0">Register your car for judging and compete against other enthusiasts.</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="goal-item mb-3">
                                                <div class="d-flex align-items-start">
                                                    <i class="fas fa-trophy text-warning me-3 mt-1"></i>
                                                    <div>
                                                        <h6 class="mb-1">Win Awards</h6>
                                                        <p class="small text-muted mb-0">Get judged professionally and see instant results on your phone.</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="goal-item mb-3">
                                                <div class="d-flex align-items-start">
                                                    <i class="fas fa-level-up-alt text-info me-3 mt-1"></i>
                                                    <div>
                                                        <h6 class="mb-1">Level Up to Coordinator</h6>
                                                        <p class="small text-muted mb-0">Ready to host your own show? Create an event and become a coordinator!</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php elseif ($userRole === 'coordinator'): ?>
                                <div class="goals-section">
                                    <h5 class="text-success mb-3">As a Show Coordinator, your responsibilities include:</h5>
                                    <div class="coordinator-workflow">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6 class="text-success mb-2">Pre-Show Setup:</h6>
                                                <ul class="list-unstyled">
                                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Create and configure your show</li>
                                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Set up categories and judging criteria</li>
                                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Assign judges to categories</li>
                                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Manage staff and volunteers</li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <h6 class="text-success mb-2">Day-of-Show Management:</h6>
                                                <ul class="list-unstyled">
                                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Monitor registrations and check-ins</li>
                                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Coordinate judging activities</li>
                                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Handle issues and questions</li>
                                                    <li class="mb-2"><i class="fas fa-exclamation-triangle text-danger me-2"></i><strong>Mark show complete when judging is done</strong></li>
                                                </ul>
                                            </div>
                                        </div>
                                        
                                        <div class="alert alert-warning mt-3">
                                            <i class="fas fa-lightbulb me-2"></i>
                                            <strong>Remember:</strong> Only you can mark your shows as complete. This triggers final scoring and winner determination - and cannot be undone!
                                        </div>
                                    </div>
                                </div>
                            <?php elseif ($userRole === 'judge'): ?>
                                <div class="goals-section">
                                    <h5 class="text-warning mb-3">As a Judge, your role is crucial for fair competition:</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="judge-responsibilities">
                                                <h6 class="text-warning mb-2">Your Judging Process:</h6>
                                                <ol class="small">
                                                    <li class="mb-2">Get assigned to shows by coordinators</li>
                                                    <li class="mb-2">Review judging categories and criteria</li>
                                                    <li class="mb-2">Scan QR codes on vehicles to access judging forms</li>
                                                    <li class="mb-2">Score vehicles fairly and consistently</li>
                                                    <li class="mb-2">Submit scores for automatic calculation</li>
                                                </ol>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="judge-tips bg-light p-3 rounded">
                                                <h6 class="text-warning mb-2">Judging Tips:</h6>
                                                <ul class="small mb-0">
                                                    <li>Be consistent across all vehicles</li>
                                                    <li>Focus on the judging criteria provided</li>
                                                    <li>Use the full scoring range (1-10)</li>
                                                    <li>Take your time to evaluate thoroughly</li>
                                                    <li>Ask coordinators if you have questions</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role Transition Options -->
            <?php if ($userRole === 'attendee'): ?>
            <div class="row">
                <div class="col-12">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-arrow-up me-2"></i>Ready to Take on More Responsibility?
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="transition-option p-3 border rounded">
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="fas fa-crown fa-2x text-success me-3"></i>
                                            <div>
                                                <h5 class="mb-1 text-success">Become a Show Coordinator</h5>
                                                <p class="small text-muted mb-0">Host and manage your own car shows</p>
                                            </div>
                                        </div>
                                        <p class="small mb-3">When you create a show, you automatically become a coordinator with full management capabilities.</p>
                                        <a href="<?php echo BASE_URL; ?>/home/<USER>" class="btn btn-success">Learn How to Host</a>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="transition-option p-3 border rounded">
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="fas fa-gavel fa-2x text-warning me-3"></i>
                                            <div>
                                                <h5 class="mb-1 text-warning">Become a Judge</h5>
                                                <p class="small text-muted mb-0">Evaluate vehicles and score competitions</p>
                                            </div>
                                        </div>
                                        <p class="small mb-3">Judges are invited by show coordinators. Build your reputation in the community to get invited!</p>
                                        <a href="<?php echo BASE_URL; ?>/judge/tutorial" class="btn btn-warning">Learn About Judging</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.role-badge {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.quick-action-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    background: #fff;
}

.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.goal-item {
    padding: 0.5rem 0;
}

.transition-option {
    transition: transform 0.2s ease-in-out;
}

.transition-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.coordinator-workflow ul {
    padding-left: 0;
}

.judge-responsibilities ol {
    padding-left: 1.2rem;
}

@media (max-width: 768px) {
    .role-header .role-actions {
        margin-top: 1rem;
        text-align: center !important;
    }
    
    .quick-action-card {
        margin-bottom: 1rem;
    }
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
<?php
/**
 * Vehicle Model
 * 
 * This model handles all database operations related to vehicles.
 */
class VehicleModel {
    private $db;
    
    /**
     * Update vehicle primary image
     * 
     * @param int $vehicleId Vehicle ID
     * @param string $imageName Image filename
     * @return bool
     */
    public function updateVehiclePrimaryImage($vehicleId, $imageName) {
        try {
            // Start transaction for data consistency
            $this->db->beginTransaction();
            
            // First, reset all images for this vehicle to not be primary
            $this->db->query('UPDATE images SET is_primary = 0 WHERE entity_type = "vehicle" AND entity_id = :vehicle_id');
            $this->db->bind(':vehicle_id', $vehicleId);
            $this->db->execute();
            
            // Then, set the specified image as primary
            $this->db->query('UPDATE images SET is_primary = 1 WHERE entity_type = "vehicle" AND entity_id = :vehicle_id AND file_name = :file_name');
            $this->db->bind(':vehicle_id', $vehicleId);
            $this->db->bind(':file_name', $imageName);
            $this->db->execute();
            
            // Get the full image data to ensure we have the correct path
            $this->db->query('SELECT * FROM images WHERE entity_type = "vehicle" AND entity_id = :vehicle_id AND file_name = :file_name');
            $this->db->bind(':vehicle_id', $vehicleId);
            $this->db->bind(':file_name', $imageName);
            $image = $this->db->single();
            
            // Check if primary_image column exists in vehicles table
            $this->db->query("SHOW COLUMNS FROM vehicles LIKE 'primary_image'");
            $columnExists = $this->db->rowCount() > 0;
            
            if (!$columnExists) {
                // Add primary_image column to vehicles table if it doesn't exist
                $this->db->query("ALTER TABLE vehicles ADD COLUMN primary_image VARCHAR(255) NULL AFTER description");
                $this->db->execute();
            }
            
            // Update the vehicle record with the primary image filename
            $this->db->query('UPDATE vehicles SET primary_image = :primary_image, updated_at = NOW() WHERE id = :id');
            $this->db->bind(':id', $vehicleId);
            $this->db->bind(':primary_image', $imageName);
            $this->db->execute();
            
            // Commit the transaction
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            // Rollback on error
            $this->db->rollBack();
            error_log('Error updating vehicle primary image: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Load a model
     * 
     * @param string $model Model name
     * @return object Model instance
     */
    private function loadModel($model) {
        // Require model file
        $modelFile = dirname(dirname(__FILE__)) . '/models/' . $model . '.php';
        
        if (!file_exists($modelFile)) {
            error_log("VehicleModel::loadModel - Model file not found: $modelFile");
            return null;
        }
        
        require_once $modelFile;
        
        // Check if class exists
        if (!class_exists($model)) {
            error_log("VehicleModel::loadModel - Model class not found: $model");
            return null;
        }
        
        // Instantiate model
        return new $model();
    }
    
    /**
     * Get all vehicles for a user
     * 
     * @param int $ownerId User ID
     * @return array
     */
    public function getUserVehicles($ownerId) {
        try {
            // First get all vehicles
            $this->db->query('SELECT v.*, u.name as owner_name 
                            FROM vehicles v 
                            JOIN users u ON v.owner_id = u.id
                            WHERE v.owner_id = :owner_id 
                            ORDER BY v.year DESC, v.make, v.model');
            $this->db->bind(':owner_id', $ownerId);
            
            $vehicles = $this->db->resultSet();
            
            if (!is_array($vehicles) || empty($vehicles)) {
                return [];
            }
            
            // Get primary images for each vehicle
            // Use the Controller's model method to load the ImageEditorModel
            $imageEditorModel = $this->loadModel('ImageEditorModel');
            
            foreach ($vehicles as $vehicle) {
                // Try to get primary image from the new images table
                $images = $imageEditorModel->getImagesByEntity('vehicle', $vehicle->id);
                
                $primaryImage = null;
                if (!empty($images)) {
                    foreach ($images as $image) {
                        if (isset($image->is_primary) && $image->is_primary) {
                            $primaryImage = $image;
                            break;
                        }
                    }
                    
                    // If no primary image is set, use the first one
                    if (!$primaryImage && count($images) > 0) {
                        $primaryImage = $images[0];
                    }
                    
                    if ($primaryImage) {
                        $vehicle->primary_image = isset($primaryImage->file_name) ? $primaryImage->file_name : '';
                    }
                }
            }
            
            return $vehicles;
        } catch (Exception $e) {
            // Log error if needed
            error_log('Error in getUserVehicles: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get vehicles by owner (alias for getUserVehicles for backward compatibility)
     * 
     * @param int $ownerId Owner ID
     * @return array Array of vehicle objects
     */
    public function getVehiclesByOwner($ownerId) {
        return $this->getUserVehicles($ownerId);
    }
    
    /**
     * Get vehicle by ID
     * 
     * @param int $id Vehicle ID
     * @return object|bool Vehicle object or false if not found
     */
    public function getVehicleById($id) {
        $this->db->query('SELECT v.*, u.name as owner_name 
                          FROM vehicles v 
                          JOIN users u ON v.owner_id = u.id 
                          WHERE v.id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->single();
    }
    
    /**
     * Create a new vehicle
     * 
     * @param array $data Vehicle data
     * @return bool|int False on failure, vehicle ID on success
     */
    public function createVehicle($data) {
        $this->db->query('INSERT INTO vehicles (owner_id, make, model, year, color, license_plate, vin, description, created_at) 
                          VALUES (:owner_id, :make, :model, :year, :color, :license_plate, :vin, :description, NOW())');
        
        $this->db->bind(':owner_id', $data['owner_id']);
        $this->db->bind(':make', $data['make']);
        $this->db->bind(':model', $data['model']);
        $this->db->bind(':year', $data['year']);
        $this->db->bind(':color', $data['color']);
        $this->db->bind(':license_plate', $data['license_plate']);
        $this->db->bind(':vin', $data['vin']);
        $this->db->bind(':description', $data['description']);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update a vehicle
     * 
     * @param array $data Vehicle data
     * @return bool
     */
    public function updateVehicle($data) {
        $this->db->query('UPDATE vehicles SET make = :make, model = :model, year = :year, 
                          color = :color, license_plate = :license_plate, vin = :vin, 
                          description = :description, updated_at = NOW() 
                          WHERE id = :id');
        
        $this->db->bind(':make', $data['make']);
        $this->db->bind(':model', $data['model']);
        $this->db->bind(':year', $data['year']);
        $this->db->bind(':color', $data['color']);
        $this->db->bind(':license_plate', $data['license_plate']);
        $this->db->bind(':vin', $data['vin']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':id', $data['id']);
        
        return $this->db->execute();
    }
    
    /**
     * Delete a vehicle
     * 
     * @param int $id Vehicle ID
     * @return bool
     */
    public function deleteVehicle($id) {
        $this->db->query('DELETE FROM vehicles WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Get vehicle images from the images table
     * 
     * @param int $vehicleId Vehicle ID
     * @return array
     * @deprecated Use ImageEditorModel::getImagesByEntity('vehicle', $vehicleId) instead
     */
    public function getVehicleImages($vehicleId) {
        // This method now uses the images table instead of the deprecated vehicle_images table
        // For new code, use ImageEditorModel::getImagesByEntity('vehicle', $vehicleId) directly
        $this->db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id ORDER BY is_primary DESC, id');
        $this->db->bind(':entity_type', 'vehicle');
        $this->db->bind(':entity_id', $vehicleId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get primary image for a vehicle
     * 
     * @param int $vehicleId Vehicle ID
     * @return object|bool Image object or false if not found
     */
    public function getPrimaryImage($vehicleId) {
        // Use the images table instead of the deprecated vehicle_images table
        $this->db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id AND is_primary = 1 LIMIT 1');
        $this->db->bind(':entity_type', 'vehicle');
        $this->db->bind(':entity_id', $vehicleId);
        
        return $this->db->single();
    }
    
    /**
     * Add an image to a vehicle
     * 
     * @param array $data Image data
     * @return bool|int False on failure, image ID on success
     * @deprecated Use ImageEditorModel::addImage() instead
     */
    public function addVehicleImage($data) {
        // This method is deprecated. Use ImageEditorModel::addImage() instead.
        // For backward compatibility, we'll convert the data format and call the ImageEditorModel
        
        // Get file info
        $fileInfo = pathinfo($data['image_path']);
        $fileSize = file_exists($data['image_path']) ? filesize($data['image_path']) : 0;
        
        // Get image dimensions
        $dimensions = getimagesize($data['image_path']);
        $width = $dimensions[0] ?? 0;
        $height = $dimensions[1] ?? 0;
        
        // Create image data for the ImageEditorModel
        $imageData = [
            'user_id' => $_SESSION['user_id'] ?? 1, // Default to admin if not set
            'entity_type' => 'vehicle',
            'entity_id' => $data['vehicle_id'],
            'file_name' => $fileInfo['basename'],
            'file_path' => $data['image_path'],
            'file_type' => mime_content_type($data['image_path']),
            'file_size' => $fileSize,
            'width' => $width,
            'height' => $height,
            'is_primary' => $data['is_primary'],
            'optimized' => 0
        ];
        
        // Use the ImageEditorModel to add the image
        require_once APPROOT . '/models/ImageEditorModel.php';
        $imageEditorModel = new ImageEditorModel();
        
        // If this is the primary image, reset other primary images first
        if ($data['is_primary']) {
            $this->db->query('UPDATE images SET is_primary = 0 WHERE entity_type = :entity_type AND entity_id = :entity_id');
            $this->db->bind(':entity_type', 'vehicle');
            $this->db->bind(':entity_id', $data['vehicle_id']);
            $this->db->execute();
        }
        
        return $imageEditorModel->addImage($imageData);
    }
    
    /**
     * Set an image as the primary image for a vehicle
     * 
     * @param int $imageId Image ID
     * @return bool
     * @deprecated Use ImageEditorModel::setPrimaryImage() instead
     */
    public function setPrimaryImage($imageId) {
        // This method is deprecated. Use ImageEditorModel::setPrimaryImage() instead.
        // For backward compatibility, we'll use the ImageEditorModel
        
        // Get the image from the images table
        $this->db->query('SELECT entity_id FROM images WHERE id = :id AND entity_type = :entity_type');
        $this->db->bind(':id', $imageId);
        $this->db->bind(':entity_type', 'vehicle');
        $image = $this->db->single();
        
        if (!$image) {
            return false;
        }
        
        // Reset all images for this vehicle
        $this->db->query('UPDATE images SET is_primary = 0 WHERE entity_type = :entity_type AND entity_id = :entity_id');
        $this->db->bind(':entity_type', 'vehicle');
        $this->db->bind(':entity_id', $image->entity_id);
        $this->db->execute();
        
        // Set this image as primary
        $this->db->query('UPDATE images SET is_primary = 1 WHERE id = :id');
        $this->db->bind(':id', $imageId);
        
        return $this->db->execute();
    }
    
    /**
     * Delete a vehicle image
     * 
     * @param int $imageId Image ID
     * @return bool
     * @deprecated Use ImageEditorModel::deleteImage() instead
     */
    public function deleteVehicleImage($imageId) {
        // This method is deprecated. Use ImageEditorModel::deleteImage() instead.
        // For backward compatibility, we'll use the ImageEditorModel
        
        error_log('VehicleModel::deleteVehicleImage - This method is deprecated. Using ImageEditorModel::deleteImage instead.');
        
        // Use the ImageEditorModel to delete the image
        require_once APPROOT . '/models/ImageEditorModel.php';
        $imageEditorModel = new ImageEditorModel();
        
        // Get the image first to check if it's primary
        $image = $imageEditorModel->getImageById($imageId);
        
        if (!$image || $image->entity_type !== 'vehicle') {
            error_log('VehicleModel::deleteVehicleImage - Image not found or not a vehicle image: ' . $imageId);
            return false;
        }
        
        $vehicleId = $image->entity_id;
        $isPrimary = $image->is_primary;
        
        // Delete the image
        $result = $imageEditorModel->deleteImage($imageId);
        
        // If this was the primary image, set another image as primary if available
        if ($result && $isPrimary) {
            $this->db->query('UPDATE images SET is_primary = 1 
                              WHERE entity_type = :entity_type AND entity_id = :entity_id 
                              ORDER BY id LIMIT 1');
            $this->db->bind(':entity_type', 'vehicle');
            $this->db->bind(':entity_id', $vehicleId);
            $this->db->execute();
        }
        
        return $result;
    }
    
    /**
     * Check if a vehicle belongs to a user
     * 
     * @param int $vehicleId Vehicle ID
     * @param int $userId User ID
     * @return bool
     */
    public function isVehicleOwner($vehicleId, $userId) {
        $this->db->query('SELECT id FROM vehicles WHERE id = :id AND owner_id = :owner_id');
        $this->db->bind(':id', $vehicleId);
        $this->db->bind(':owner_id', $userId);
        $this->db->single();
        
        return $this->db->rowCount() > 0;
    }
    
    /**
     * Get all vehicles
     * 
     * @return array
     */
    public function getVehicles() {
        try {
            $this->db->query('SELECT v.*, u.name as owner_name 
                              FROM vehicles v 
                              LEFT JOIN users u ON v.owner_id = u.id 
                              ORDER BY v.make, v.model');
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in getVehicles: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all vehicles with additional details (for admin use)
     * 
     * @return array
     */
    public function getAllVehicles() {
        try {
            // Check if the users table has a name column
            $this->db->query("SHOW COLUMNS FROM users LIKE 'name'");
            $nameColumnExists = $this->db->rowCount() > 0;
            
            // Build the query based on available columns
            $query = 'SELECT v.*';
            
            // Add owner name if available
            if ($nameColumnExists) {
                $query .= ', u.name as owner_name';
            } else {
                $query .= ', CONCAT("User #", v.owner_id) as owner_name';
            }
            
            // Check if vehicle_images table exists
            $this->db->query("SHOW TABLES LIKE 'vehicle_images'");
            $vehicleImagesTableExists = $this->db->rowCount() > 0;
            
            // Add primary image if available
            if ($vehicleImagesTableExists) {
                $query .= ', (SELECT image_path FROM vehicle_images WHERE vehicle_id = v.id AND is_primary = 1 LIMIT 1) as primary_image';
            }
            
            $query .= ' FROM vehicles v 
                      LEFT JOIN users u ON v.owner_id = u.id 
                      ORDER BY v.year DESC, v.make ASC, v.model ASC';
            
            $this->db->query($query);
            $result = $this->db->resultSet();
            
            // Ensure we always return an array
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            // Log the error
            error_log('Error in getAllVehicles: ' . $e->getMessage());
            
            // Fallback to a simpler query without joining users
            try {
                $this->db->query('SELECT v.*, CONCAT("User #", v.owner_id) as owner_name
                                 FROM vehicles v 
                                 ORDER BY v.year DESC, v.make ASC, v.model ASC');
                $result = $this->db->resultSet();
                return is_array($result) ? $result : [];
            } catch (Exception $e2) {
                error_log('Error in getAllVehicles fallback: ' . $e2->getMessage());
                return [];
            }
        }
    }
    
    /**
     * Get vehicles registered for a show
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getShowVehicles($showId) {
        $this->db->query('SELECT v.*, r.registration_number, r.status, sc.name as category_name, 
                          u.name as owner_name, 
                          (SELECT image_path FROM vehicle_images WHERE vehicle_id = v.id AND is_primary = 1 LIMIT 1) as primary_image 
                          FROM vehicles v 
                          JOIN registrations r ON v.id = r.vehicle_id 
                          JOIN show_categories sc ON r.category_id = sc.id 
                          JOIN users u ON v.owner_id = u.id 
                          WHERE r.show_id = :show_id');
        $this->db->bind(':show_id', $showId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get vehicles registered for a show with all images
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getShowVehiclesWithImages($showId) {
        try {
            // Get all vehicles for the show
            $this->db->query('SELECT v.*, r.id as registration_id, r.registration_number, r.display_number, r.status, 
                            sc.name as category_name, sc.id as category_id, 
                            u.name as owner_name
                            FROM vehicles v 
                            JOIN registrations r ON v.id = r.vehicle_id 
                            JOIN show_categories sc ON r.category_id = sc.id 
                            JOIN users u ON v.owner_id = u.id 
                            WHERE r.show_id = :show_id AND r.status = :status
                            ORDER BY sc.name, v.year DESC, v.make, v.model');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':status', 'approved');
            $vehicles = $this->db->resultSet();
            
            // Get images for each vehicle from the images table
            foreach ($vehicles as $vehicle) {
                $this->db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id ORDER BY is_primary DESC');
                $this->db->bind(':entity_type', 'vehicle');
                $this->db->bind(':entity_id', $vehicle->id);
                $vehicle->images = $this->db->resultSet();
                
                // If no images found, provide an empty array to avoid errors in the view
                if (empty($vehicle->images)) {
                    $vehicle->images = [];
                }
            }
            
            return $vehicles;
        } catch (Exception $e) {
            error_log('Error in getShowVehiclesWithImages: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get paginated vehicles for a specific show with images - optimized for thousands of vehicles
     */
    public function getPaginatedShowVehiclesWithImages($showId, $page = 1, $limit = 12, $search = '', $categoryId = null) {
        try {
            $offset = ($page - 1) * $limit;
            $whereConditions = ['r.show_id = :show_id', 'r.status = "approved"'];
            $params = [':show_id' => $showId];

            // Add search filter
            if (!empty($search)) {
                $whereConditions[] = "(v.make LIKE :search OR v.model LIKE :search2 OR v.year LIKE :search3 OR r.registration_number LIKE :search4)";
                $searchTerm = '%' . $search . '%';
                $params[':search'] = $searchTerm;
                $params[':search2'] = $searchTerm;
                $params[':search3'] = $searchTerm;
                $params[':search4'] = $searchTerm;
            }

            // Add category filter
            if ($categoryId) {
                $whereConditions[] = "r.category_id = :category_id";
                $params[':category_id'] = $categoryId;
            }

            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

            // Get total count for pagination
            $countQuery = "SELECT COUNT(*) as total
                          FROM registrations r
                          LEFT JOIN vehicles v ON r.vehicle_id = v.id
                          LEFT JOIN show_categories sc ON r.category_id = sc.id
                          {$whereClause}";

            $this->db->query($countQuery);
            foreach ($params as $key => $value) {
                $this->db->bind($key, $value);
            }
            $total = $this->db->single()->total;

            // Get paginated results with images and vote counts
            $dataQuery = "SELECT v.*, r.id as registration_id, r.registration_number, r.display_number, r.status,
                                 sc.name as category_name, sc.id as category_id,
                                 u.name as owner_name,
                                 COUNT(fv.id) as vote_count
                         FROM vehicles v
                         JOIN registrations r ON v.id = r.vehicle_id
                         JOIN show_categories sc ON r.category_id = sc.id
                         JOIN users u ON v.owner_id = u.id
                         LEFT JOIN fan_votes fv ON r.id = fv.registration_id AND fv.is_approved = 1
                         {$whereClause}
                         GROUP BY v.id
                         ORDER BY sc.name, v.year DESC, v.make, v.model
                         LIMIT :limit OFFSET :offset";

            $this->db->query($dataQuery);
            foreach ($params as $key => $value) {
                $this->db->bind($key, $value);
            }
            $this->db->bind(':limit', $limit);
            $this->db->bind(':offset', $offset);

            $vehicles = $this->db->resultSet();

            // Get images for each vehicle using ImageEditorModel
            require_once APPROOT . '/models/ImageEditorModel.php';
            $imageModel = new ImageEditorModel();
            foreach ($vehicles as $vehicle) {
                $vehicle->images = $imageModel->getImagesByEntity('vehicle', $vehicle->id);
            }

            return [
                'vehicles' => $vehicles,
                'total' => $total
            ];

        } catch (Exception $e) {
            error_log('Error getting paginated show vehicles: ' . $e->getMessage());
            return [
                'vehicles' => [],
                'total' => 0
            ];
        }
    }

    /**
     * Get total vehicle count for a show
     */
    public function getShowVehicleCount($showId) {
        try {
            $this->db->query('SELECT COUNT(*) as count
                             FROM registrations r
                             WHERE r.show_id = :show_id AND r.status = "approved"');
            $this->db->bind(':show_id', $showId);

            return $this->db->single()->count ?? 0;

        } catch (Exception $e) {
            error_log('Error getting show vehicle count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get vehicles registered for a show ordered by category and registration number
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getShowVehiclesByCategory($showId) {
        $this->db->query('SELECT v.*, r.registration_number, r.status, sc.name as category_name, 
                          u.name as owner_name, 
                          (SELECT image_path FROM vehicle_images WHERE vehicle_id = v.id AND is_primary = 1 LIMIT 1) as primary_image 
                          FROM vehicles v 
                          JOIN registrations r ON v.id = r.vehicle_id 
                          JOIN show_categories sc ON r.category_id = sc.id 
                          JOIN users u ON v.owner_id = u.id 
                          WHERE r.show_id = :show_id
                          ORDER BY sc.name, r.registration_number');
        $this->db->bind(':show_id', $showId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get vehicles registered for a category
     * 
     * @param int $categoryId Category ID
     * @return array
     */
    public function getCategoryVehicles($categoryId) {
        $this->db->query('SELECT v.*, r.registration_number, r.status, r.id as registration_id, 
                          u.name as owner_name, 
                          (SELECT image_path FROM vehicle_images WHERE vehicle_id = v.id AND is_primary = 1 LIMIT 1) as primary_image 
                          FROM vehicles v 
                          JOIN registrations r ON v.id = r.vehicle_id 
                          JOIN users u ON v.owner_id = u.id 
                          WHERE r.category_id = :category_id 
                          ORDER BY r.registration_number');
        $this->db->bind(':category_id', $categoryId);
        
        return $this->db->resultSet();
    }
}
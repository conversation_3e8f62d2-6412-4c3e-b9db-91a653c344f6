<?php
/**
 * Breadcrumb Navigation Component
 * Provides contextual navigation for multi-role users
 */

// Only show breadcrumbs for logged-in users
if (!isset($_SESSION['user_id'])) {
    return;
}

// Get current URL and parse it
$currentUrl = $_SERVER['REQUEST_URI'] ?? '';
$urlParts = explode('/', trim($currentUrl, '/'));
$baseUrl = BASE_URL;

// Remove query parameters
if (isset($urlParts[0]) && strpos($urlParts[0], '?') !== false) {
    $urlParts[0] = explode('?', $urlParts[0])[0];
}

// Define breadcrumb mappings
$breadcrumbMappings = [
    'home' => ['icon' => 'fa-home', 'label' => 'Home', 'url' => '/'],
    'user' => [
        'dashboard' => ['icon' => 'fa-tachometer-alt', 'label' => 'Member Dashboard', 'url' => '/user/dashboard'],
        'profile' => ['icon' => 'fa-user', 'label' => 'Profile', 'url' => '/user/profile'],
        'vehicles' => ['icon' => 'fa-car', 'label' => 'My Vehicles', 'url' => '/user/vehicles'],
        'registrations' => ['icon' => 'fa-list', 'label' => 'My Registrations', 'url' => '/user/registrations'],
        'createShow' => ['icon' => 'fa-plus-circle', 'label' => 'Create Show', 'url' => '/user/createShow']
    ],
    'admin' => [
        'dashboard' => ['icon' => 'fa-crown', 'label' => 'Admin Dashboard', 'url' => '/admin/dashboard'],
        'users' => ['icon' => 'fa-users', 'label' => 'User Management', 'url' => '/admin/users'],
        'shows' => ['icon' => 'fa-calendar-check', 'label' => 'Show Management', 'url' => '/admin/shows'],
        'settings' => ['icon' => 'fa-cog', 'label' => 'Admin Settings', 'url' => '/admin/settings'],
        'editShow' => ['icon' => 'fa-edit', 'label' => 'Edit Show', 'url' => ''],
        'viewRegistration' => ['icon' => 'fa-eye', 'label' => 'View Registration', 'url' => '']
    ],
    'coordinator' => [
        'dashboard' => ['icon' => 'fa-clipboard-list', 'label' => 'Coordinator Dashboard', 'url' => '/coordinator/dashboard'],
        'shows' => ['icon' => 'fa-calendar-check', 'label' => 'My Shows', 'url' => '/coordinator/shows'],
        'editShow' => ['icon' => 'fa-edit', 'label' => 'Edit Show', 'url' => ''],
        'registrations' => ['icon' => 'fa-list', 'label' => 'Registrations', 'url' => '']
    ],
    'judge' => [
        'dashboard' => ['icon' => 'fa-gavel', 'label' => 'Judge Dashboard', 'url' => '/judge/dashboard'],
        'assignments' => ['icon' => 'fa-tasks', 'label' => 'Assignments', 'url' => '/judge/assignments'],
        'score' => ['icon' => 'fa-star', 'label' => 'Score Vehicle', 'url' => '']
    ],
    'staff' => [
        'dashboard' => ['icon' => 'fa-users', 'label' => 'Staff Dashboard', 'url' => '/staff/dashboard'],
        'shows' => ['icon' => 'fa-calendar', 'label' => 'Assigned Shows', 'url' => '/staff/shows'],
        'registrations' => ['icon' => 'fa-list', 'label' => 'Registrations', 'url' => '']
    ],
    'show' => [
        'index' => ['icon' => 'fa-search', 'label' => 'Browse Shows', 'url' => '/show'],
        'view' => ['icon' => 'fa-eye', 'label' => 'Show Details', 'url' => ''],
        'register' => ['icon' => 'fa-user-plus', 'label' => 'Register', 'url' => '']
    ],
    'calendar' => [
        'index' => ['icon' => 'fa-calendar', 'label' => 'Event Calendar', 'url' => '/calendar'],
        'createEvent' => ['icon' => 'fa-plus-circle', 'label' => 'Create Event', 'url' => '/calendar/createEvent'],
        'event' => ['icon' => 'fa-eye', 'label' => 'Event Details', 'url' => '']
    ],
    'notification_center' => [
        'index' => ['icon' => 'fa-bell', 'label' => 'Messages', 'url' => '/notification_center']
    ],
    'registration' => [
        'index' => ['icon' => 'fa-user-plus', 'label' => 'Registration', 'url' => '/registration'],
        'register' => ['icon' => 'fa-car', 'label' => 'Register Vehicle', 'url' => '']
    ],
    'auth' => [
        'login' => ['icon' => 'fa-sign-in-alt', 'label' => 'Login', 'url' => '/auth/login'],
        'register' => ['icon' => 'fa-user-plus', 'label' => 'Sign Up', 'url' => '/auth/register'],
        'logout' => ['icon' => 'fa-sign-out-alt', 'label' => 'Logout', 'url' => '']
    ],
    'progressive_auth' => [
        'register' => ['icon' => 'fa-user-plus', 'label' => 'Create Account', 'url' => '/progressive_auth/register']
    ]
];

// Build breadcrumb trail
$breadcrumbs = [];
$currentPath = '';

// Always start with Home
$breadcrumbs[] = [
    'icon' => 'fa-home',
    'label' => 'Home',
    'url' => $baseUrl,
    'active' => false
];

// Parse URL parts and build breadcrumbs
for ($i = 0; $i < count($urlParts); $i++) {
    $part = $urlParts[$i];
    
    if (empty($part)) continue;
    
    $currentPath .= '/' . $part;
    $isLast = ($i === count($urlParts) - 1);
    
    // Check if this part has a mapping
    if ($i === 0 && isset($breadcrumbMappings[$part])) {
        // First level (controller)
        $controller = $part;
        
        // If there's a second part, look for action mapping
        if (isset($urlParts[$i + 1]) && isset($breadcrumbMappings[$part][$urlParts[$i + 1]])) {
            $action = $urlParts[$i + 1];
            $mapping = $breadcrumbMappings[$part][$action];
            
            // Add controller breadcrumb if it has a dashboard
            if (isset($breadcrumbMappings[$part]['dashboard'])) {
                $dashboardMapping = $breadcrumbMappings[$part]['dashboard'];
                $breadcrumbs[] = [
                    'icon' => $dashboardMapping['icon'],
                    'label' => $dashboardMapping['label'],
                    'url' => $baseUrl . $dashboardMapping['url'],
                    'active' => false
                ];
            }
            
            // Add action breadcrumb
            $breadcrumbs[] = [
                'icon' => $mapping['icon'],
                'label' => $mapping['label'],
                'url' => !empty($mapping['url']) ? $baseUrl . $mapping['url'] : '',
                'active' => $isLast || ($i === count($urlParts) - 2)
            ];
            
            $i++; // Skip next iteration since we processed the action
        } else {
            // Just controller, check if it has a default dashboard
            if (isset($breadcrumbMappings[$part]['dashboard'])) {
                $mapping = $breadcrumbMappings[$part]['dashboard'];
                $breadcrumbs[] = [
                    'icon' => $mapping['icon'],
                    'label' => $mapping['label'],
                    'url' => $baseUrl . $mapping['url'],
                    'active' => $isLast
                ];
            }
        }
    } else {
        // Handle unmapped controllers with generic breadcrumbs
        if ($i === 0) {
            $genericMappings = [
                'home' => ['icon' => 'fa-home', 'label' => 'Home'],
                'user' => ['icon' => 'fa-user', 'label' => 'Member Area'],
                'admin' => ['icon' => 'fa-crown', 'label' => 'Administration'],
                'coordinator' => ['icon' => 'fa-clipboard-list', 'label' => 'Coordinator Area'],
                'judge' => ['icon' => 'fa-gavel', 'label' => 'Judge Area'],
                'staff' => ['icon' => 'fa-users', 'label' => 'Staff Area'],
                'show' => ['icon' => 'fa-car', 'label' => 'Car Shows'],
                'calendar' => ['icon' => 'fa-calendar', 'label' => 'Events'],
                'notification_center' => ['icon' => 'fa-bell', 'label' => 'Messages'],
                'registration' => ['icon' => 'fa-user-plus', 'label' => 'Registration'],
                'auth' => ['icon' => 'fa-sign-in-alt', 'label' => 'Authentication']
            ];

            if (isset($genericMappings[$part])) {
                $breadcrumbs[] = [
                    'icon' => $genericMappings[$part]['icon'],
                    'label' => $genericMappings[$part]['label'],
                    'url' => '',
                    'active' => $isLast
                ];
            }
        } else {
            // Handle numeric IDs or other unmapped parts
            if (is_numeric($part)) {
                // This is likely an ID, add a generic "Details" breadcrumb
                $breadcrumbs[] = [
                    'icon' => 'fa-eye',
                    'label' => 'Details',
                    'url' => '',
                    'active' => $isLast
                ];
            } else {
                // Add a generic breadcrumb for unmapped actions
                $breadcrumbs[] = [
                    'icon' => 'fa-file',
                    'label' => ucfirst(str_replace(['_', '-'], ' ', $part)),
                    'url' => '',
                    'active' => $isLast
                ];
            }
        }
    }
}

// Get current user role for role switcher
$currentRole = $_SESSION['user_role'] ?? 'user';
$userName = $_SESSION['user_name'] ?? 'User';

// If we only have Home, try to add a generic breadcrumb based on current page
if (count($breadcrumbs) <= 1 && !empty($urlParts[0])) {
    $controller = $urlParts[0];
    $action = $urlParts[1] ?? 'index';

    // Add generic breadcrumbs for common controllers
    $genericMappings = [
        'home' => ['icon' => 'fa-home', 'label' => 'Home'],
        'user' => ['icon' => 'fa-user', 'label' => 'Member Area'],
        'admin' => ['icon' => 'fa-crown', 'label' => 'Administration'],
        'coordinator' => ['icon' => 'fa-clipboard-list', 'label' => 'Coordinator Area'],
        'judge' => ['icon' => 'fa-gavel', 'label' => 'Judge Area'],
        'staff' => ['icon' => 'fa-users', 'label' => 'Staff Area'],
        'show' => ['icon' => 'fa-car', 'label' => 'Car Shows'],
        'calendar' => ['icon' => 'fa-calendar', 'label' => 'Events'],
        'notification_center' => ['icon' => 'fa-bell', 'label' => 'Messages'],
        'registration' => ['icon' => 'fa-user-plus', 'label' => 'Registration'],
        'auth' => ['icon' => 'fa-sign-in-alt', 'label' => 'Authentication']
    ];

    if (isset($genericMappings[$controller])) {
        $breadcrumbs[] = [
            'icon' => $genericMappings[$controller]['icon'],
            'label' => $genericMappings[$controller]['label'],
            'url' => '',
            'active' => true
        ];
    }
}

// Mark the last breadcrumb as active
if (!empty($breadcrumbs)) {
    $breadcrumbs[count($breadcrumbs) - 1]['active'] = true;
}
?>

<!-- Mobile Compact Role Bar (always show for logged-in users) -->
<?php if (isset($_SESSION['user_id'])): ?>
<div class="mobile-role-bar d-lg-none">
    <div class="container">
        <div class="mobile-role-content">
            <span class="mobile-role-badge role-<?php echo $currentRole; ?>"
                  data-bs-toggle="tooltip"
                  data-bs-placement="bottom"
                  data-bs-html="true"
                  title="<?php
                  $roleDescriptions = [
                      'user' => '<strong>Member Capabilities:</strong><br>• Register vehicles<br>• View calendars<br>• Manage profile',
                      'staff' => '<strong>Staff Capabilities:</strong><br>• Check-in vehicles<br>• Assist registration<br>• Access dashboard',
                      'judge' => '<strong>Judge Capabilities:</strong><br>• Score vehicles<br>• Mobile judging<br>• View assignments',
                      'coordinator' => '<strong>Coordinator Capabilities:</strong><br>• Create shows<br>• Manage registrations<br>• Assign staff',
                      'admin' => '<strong>Administrator:</strong><br>• Full system access<br>• All capabilities'
                  ];
                  echo $roleDescriptions[$currentRole] ?? 'User capabilities';
                  ?>">
                <?php
                $roleConfig = [
                    'user' => ['label' => 'Member', 'icon' => 'fa-user-circle'],
                    'staff' => ['label' => 'Staff', 'icon' => 'fa-users'],
                    'judge' => ['label' => 'Judge', 'icon' => 'fa-gavel'],
                    'coordinator' => ['label' => 'Coordinator', 'icon' => 'fa-clipboard-list'],
                    'admin' => ['label' => 'Admin', 'icon' => 'fa-crown']
                ];
                $roleInfo = $roleConfig[$currentRole] ?? ['label' => ucfirst($currentRole), 'icon' => 'fa-user'];
                ?>
                <i class="fas <?php echo $roleInfo['icon']; ?> me-1"></i>
                <?php echo $roleInfo['label']; ?>
            </span>

            <?php if ($currentRole === 'admin'): ?>
                <div class="mobile-role-switcher dropdown">
                    <button class="btn btn-sm mobile-switch-btn dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-exchange-alt"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end racing-dropdown-menu">
                        <li><a class="dropdown-item racing-dropdown-item" href="<?php echo BASE_URL; ?>/user/dashboard">
                            <i class="fas fa-user-circle me-2"></i> Member
                        </a></li>
                        <li><a class="dropdown-item racing-dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/dashboard">
                            <i class="fas fa-clipboard-list me-2"></i> Coordinator
                        </a></li>
                        <li><a class="dropdown-item racing-dropdown-item" href="<?php echo BASE_URL; ?>/judge/dashboard">
                            <i class="fas fa-gavel me-2"></i> Judge
                        </a></li>
                        <li><a class="dropdown-item racing-dropdown-item" href="<?php echo BASE_URL; ?>/staff/dashboard">
                            <i class="fas fa-users me-2"></i> Staff
                        </a></li>
                        <li><hr class="dropdown-divider racing-divider"></li>
                        <li><a class="dropdown-item racing-dropdown-item" href="<?php echo BASE_URL; ?>/admin/dashboard">
                            <i class="fas fa-crown me-2"></i> Admin
                        </a></li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Show breadcrumbs on home page for logged-in users (for role indicator)
// Don't show breadcrumbs if we still only have Home AND we're not on home page
if (count($breadcrumbs) <= 1 && !($currentUrl === '/' || $currentUrl === '' || strpos($currentUrl, '/home') !== false)) {
    return;
}
?>

<div class="breadcrumb-navigation">
    <div class="container">
        <div class="breadcrumb-content d-none d-lg-flex">
            <!-- Breadcrumb Trail -->
            <nav aria-label="breadcrumb" class="breadcrumb-trail">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $crumb): ?>
                        <li class="breadcrumb-item <?php echo $crumb['active'] ? 'active' : ''; ?>">
                            <?php if (!$crumb['active'] && !empty($crumb['url'])): ?>
                                <a href="<?php echo $crumb['url']; ?>">
                                    <i class="fas <?php echo $crumb['icon']; ?>"></i>
                                    <?php echo $crumb['label']; ?>
                                </a>
                            <?php else: ?>
                                <span>
                                    <i class="fas <?php echo $crumb['icon']; ?>"></i>
                                    <?php echo $crumb['label']; ?>
                                </span>
                            <?php endif; ?>
                        </li>
                    <?php endforeach; ?>
                </ol>
            </nav>
            
            <!-- Role Context & Quick Actions -->
            <div class="breadcrumb-actions">
                <!-- Current Role Context -->
                <div class="role-context">
                    <span class="role-label">Viewing as:</span>
                    <span class="role-badge role-<?php echo $currentRole; ?>"
                          data-bs-toggle="tooltip"
                          data-bs-placement="bottom"
                          data-bs-html="true"
                          title="<?php
                          // Role descriptions with capabilities
                          $roleDescriptions = [
                              'user' => '<strong>Member Capabilities:</strong><br>• Register vehicles for shows<br>• View event calendars<br>• Manage personal profile<br>• Submit vehicle photos<br>• Receive notifications',
                              'staff' => '<strong>Staff Capabilities:</strong><br>• Check-in vehicles at shows<br>• Assist with registration<br>• Access staff dashboard<br>• View show reports<br>• Help coordinate events',
                              'judge' => '<strong>Judge Capabilities:</strong><br>• Score vehicles in assigned categories<br>• Access mobile judging interface<br>• View judging assignments<br>• Submit scores and notes<br>• Access judge dashboard',
                              'coordinator' => '<strong>Coordinator Capabilities:</strong><br>• Create and manage shows<br>• Set up judging categories<br>• Manage registrations<br>• Assign judges and staff<br>• Generate reports',
                              'admin' => '<strong>Administrator Capabilities:</strong><br>• Full system access<br>• Manage all users and shows<br>• Access system settings<br>• View all dashboards<br>• Generate system reports<br>• All role capabilities'
                          ];
                          echo $roleDescriptions[$currentRole] ?? 'User capabilities';
                          ?>">
                        <?php
                        // Use consistent role labels with icons
                        $roleConfig = [
                            'user' => ['label' => 'Member', 'icon' => 'fa-user-circle'],
                            'staff' => ['label' => 'Staff', 'icon' => 'fa-users'],
                            'judge' => ['label' => 'Judge', 'icon' => 'fa-gavel'],
                            'coordinator' => ['label' => 'Coordinator', 'icon' => 'fa-clipboard-list'],
                            'admin' => ['label' => 'Administrator', 'icon' => 'fa-crown']
                        ];
                        $roleInfo = $roleConfig[$currentRole] ?? ['label' => ucfirst($currentRole), 'icon' => 'fa-user'];
                        ?>
                        <i class="fas <?php echo $roleInfo['icon']; ?> me-1"></i>
                        <?php echo $roleInfo['label']; ?>
                    </span>
                </div>
                
                <!-- Role Switcher for Admins -->
                <?php if ($currentRole === 'admin'): ?>
                    <div class="role-switcher dropdown">
                        <button class="btn btn-sm racing-switch-btn dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-exchange-alt"></i> Switch View
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end racing-dropdown-menu">
                            <li><h6 class="dropdown-header racing-dropdown-header">Switch Dashboard</h6></li>
                            <li><a class="dropdown-item racing-dropdown-item" href="<?php echo BASE_URL; ?>/user/dashboard">
                                <i class="fas fa-user-circle text-purple me-2"></i> Member Dashboard
                            </a></li>
                            <li><a class="dropdown-item racing-dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/dashboard">
                                <i class="fas fa-clipboard-list text-pink me-2"></i> Coordinator Dashboard
                            </a></li>
                            <li><a class="dropdown-item racing-dropdown-item" href="<?php echo BASE_URL; ?>/judge/dashboard">
                                <i class="fas fa-gavel text-orange me-2"></i> Judge Dashboard
                            </a></li>
                            <li><a class="dropdown-item racing-dropdown-item" href="<?php echo BASE_URL; ?>/staff/dashboard">
                                <i class="fas fa-users text-green me-2"></i> Staff Dashboard
                            </a></li>
                            <li><hr class="dropdown-divider racing-divider"></li>
                            <li><a class="dropdown-item racing-dropdown-item" href="<?php echo BASE_URL; ?>/admin/dashboard">
                                <i class="fas fa-crown text-red me-2"></i> Administrator Dashboard
                            </a></li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Breadcrumb Navigation Styles -->
<style>
.breadcrumb-navigation {
    background:
        linear-gradient(rgba(30, 30, 30, 0.95), rgba(10, 10, 10, 0.98)),
        linear-gradient(45deg, #2a2a2a 25%, transparent 25%),
        linear-gradient(-45deg, #2a2a2a 25%, transparent 25%);
    background-size: 100% 100%, 20px 20px, 20px 20px;
    background-position: 0 0, 0 0, 10px 10px;
    border-bottom: 2px solid #c0c0c0;
    padding: 12px 0;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    z-index: 1000 !important;
    position: relative !important;
}

.breadcrumb-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 15px;
}

.breadcrumb-trail {
    flex: 1;
}

.breadcrumb {
    margin: 0;
    padding: 0;
    background: none;
    font-size: 0.9rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #c0c0c0;
    margin: 0 8px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.breadcrumb-item a {
    color: #c0c0c0;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.breadcrumb-item a:hover {
    color: #00ffff;
    background: rgba(0, 255, 255, 0.1);
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.breadcrumb-item.active span {
    color: #ffffff;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.breadcrumb-item i {
    font-size: 0.85rem;
}

.breadcrumb-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.role-context {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
}

.role-label {
    color: #6c757d;
    font-weight: 500;
}

.role-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
    cursor: help;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.role-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.role-badge.role-user { background: #4a148c; }
.role-badge.role-staff { background: #1b5e20; }
.role-badge.role-judge { background: #e65100; }
.role-badge.role-coordinator { background: #880e4f; }
.role-badge.role-admin { background: #b71c1c; }

/* Racing Switch Button */
.racing-switch-btn {
    background: linear-gradient(145deg, #c0c0c0, #a0a0a0) !important;
    border: 2px solid #c0c0c0 !important;
    color: #1a1a1a !important;
    font-size: 0.8rem;
    padding: 4px 8px;
    text-shadow: none !important;
    font-weight: 600;
    transition: all 0.3s ease;
}

.racing-switch-btn:hover {
    background: linear-gradient(145deg, #00ffff, #00cccc) !important;
    border-color: #00ffff !important;
    color: #000 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 255, 255, 0.3);
}

/* Racing Dropdown Menu */
.racing-dropdown-menu {
    background:
        linear-gradient(rgba(30, 30, 30, 0.95), rgba(10, 10, 10, 0.98)),
        linear-gradient(45deg, #2a2a2a 25%, transparent 25%),
        linear-gradient(-45deg, #2a2a2a 25%, transparent 25%) !important;
    border: 2px solid #c0c0c0 !important;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5) !important;
    z-index: 1001 !important;
}

.racing-dropdown-header {
    color: #c0c0c0 !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

.racing-dropdown-item {
    color: #c0c0c0 !important;
    padding: 0.75rem 1rem !important;
    border-radius: 4px !important;
    transition: all 0.3s ease;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.racing-dropdown-item:hover {
    background: linear-gradient(145deg, rgba(0, 255, 255, 0.2), rgba(0, 255, 255, 0.1)) !important;
    color: #00ffff !important;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
    transform: translateX(5px);
}

.racing-divider {
    border-color: #c0c0c0 !important;
    opacity: 0.3 !important;
}

/* Mobile Compact Role Bar */
.mobile-role-bar {
    background:
        linear-gradient(rgba(30, 30, 30, 0.95), rgba(10, 10, 10, 0.98)),
        linear-gradient(45deg, #2a2a2a 25%, transparent 25%),
        linear-gradient(-45deg, #2a2a2a 25%, transparent 25%);
    background-size: 100% 100%, 20px 20px, 20px 20px;
    background-position: 0 0, 0 0, 10px 10px;
    border-bottom: 1px solid #c0c0c0;
    padding: 8px 0;
    margin-bottom: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.mobile-role-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.mobile-role-badge {
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    color: white;
    cursor: help;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.mobile-role-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.mobile-switch-btn {
    background: linear-gradient(145deg, #c0c0c0, #a0a0a0) !important;
    border: 1px solid #c0c0c0 !important;
    color: #1a1a1a !important;
    font-size: 0.7rem;
    padding: 2px 6px;
    text-shadow: none !important;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 32px;
    height: 28px;
}

.mobile-switch-btn:hover {
    background: linear-gradient(145deg, #00ffff, #00cccc) !important;
    border-color: #00ffff !important;
    color: #000 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 255, 255, 0.3);
}

/* Role color classes for switcher */
.text-purple { color: #4a148c !important; }
.text-pink { color: #880e4f !important; }
.text-orange { color: #e65100 !important; }
.text-green { color: #1b5e20 !important; }
.text-red { color: #b71c1c !important; }

/* Mobile responsive */
@media (max-width: 768px) {
    .breadcrumb-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .breadcrumb {
        font-size: 0.8rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        margin: 0 6px;
    }
    
    .breadcrumb-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .role-context {
        font-size: 0.8rem;
    }
    
    .role-badge {
        font-size: 0.7rem;
        padding: 3px 8px;
    }
}

/* Hide on very small screens */
@media (max-width: 480px) {
    .breadcrumb-navigation {
        display: none;
    }
}
</style>

<!-- Initialize Bootstrap Tooltips for Role Badges -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips for role badges
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            html: true,
            trigger: 'hover focus'
        });
    });
});
</script>

<?php
/**
 * Show Completion Controller
 * 
 * Handles the critical "Mark Show Complete" functionality
 * This triggers final scoring and winner determination
 */

class ShowCompletionController extends Controller {
    
    private $showModel;
    private $registrationModel;
    private $judgingModel;
    
    public function __construct() {
        $this->showModel = $this->model('ShowModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->judgingModel = $this->model('JudgingModel');
    }

    /**
     * Mark show as complete and trigger final scoring
     * This is a critical action that cannot be undone
     */
    public function markComplete() {
        // Ensure user is logged in and is a coordinator
        if (!isset($_SESSION['user_id'])) {
            redirect('auth/login');
            return;
        }

        // Get show ID from POST data
        if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['show_id'])) {
            $_SESSION['flash_message'] = 'Invalid request';
            $_SESSION['flash_type'] = 'error';
            redirect('coordinator/dashboard');
            return;
        }

        $showId = (int)$_POST['show_id'];
        
        // Verify user is coordinator of this show
        $show = $this->showModel->getShowById($showId);
        if (!$show || $show->coordinator_id != $_SESSION['user_id']) {
            $_SESSION['flash_message'] = 'You do not have permission to complete this show';
            $_SESSION['flash_type'] = 'error';
            redirect('coordinator/dashboard');
            return;
        }

        // Check if show is already completed
        if ($show->status === 'completed') {
            $_SESSION['flash_message'] = 'This show has already been completed';
            $_SESSION['flash_type'] = 'warning';
            redirect('coordinator/showDetails/' . $showId);
            return;
        }

        // Verify confirmation was provided
        if (!isset($_POST['confirm_completion']) || $_POST['confirm_completion'] !== 'yes') {
            $_SESSION['flash_message'] = 'You must confirm completion to proceed';
            $_SESSION['flash_type'] = 'error';
            redirect('coordinator/showDetails/' . $showId);
            return;
        }

        try {
            // Start transaction for atomic operation
            $this->showModel->beginTransaction();

            // 1. Mark show as completed
            $updateData = [
                'status' => 'completed',
                'completed_at' => date('Y-m-d H:i:s'),
                'completed_by' => $_SESSION['user_id']
            ];
            
            if (!$this->showModel->updateShow($showId, $updateData)) {
                throw new Exception('Failed to update show status');
            }

            // 2. Trigger final scoring calculation
            $this->calculateFinalResults($showId);

            // 3. Send winner notifications
            $this->sendWinnerNotifications($showId);

            // 4. Log the completion
            $this->logShowCompletion($showId);

            // Commit transaction
            $this->showModel->commit();

            $_SESSION['flash_message'] = 'Show has been marked as complete! Final results have been calculated and winners have been notified.';
            $_SESSION['flash_type'] = 'success';
            
            redirect('coordinator/showResults/' . $showId);

        } catch (Exception $e) {
            // Rollback transaction on error
            $this->showModel->rollback();
            
            error_log("Error completing show {$showId}: " . $e->getMessage());
            $_SESSION['flash_message'] = 'Error completing show: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'error';
            
            redirect('coordinator/showDetails/' . $showId);
        }
    }

    /**
     * Show completion confirmation page
     */
    public function confirmCompletion($showId) {
        // Ensure user is logged in and is a coordinator
        if (!isset($_SESSION['user_id'])) {
            redirect('auth/login');
            return;
        }

        $show = $this->showModel->getShowById($showId);
        if (!$show || $show->coordinator_id != $_SESSION['user_id']) {
            $_SESSION['flash_message'] = 'You do not have permission to access this show';
            $_SESSION['flash_type'] = 'error';
            redirect('coordinator/dashboard');
            return;
        }

        // Check if show is already completed
        if ($show->status === 'completed') {
            $_SESSION['flash_message'] = 'This show has already been completed';
            $_SESSION['flash_type'] = 'warning';
            redirect('coordinator/showDetails/' . $showId);
            return;
        }

        // Get judging progress statistics
        $judgingStats = $this->getJudgingProgress($showId);
        
        $data = [
            'title' => 'Confirm Show Completion',
            'show' => $show,
            'judging_stats' => $judgingStats
        ];

        $this->view('coordinator/confirm-completion', $data);
    }

    /**
     * Calculate final results for all categories
     */
    private function calculateFinalResults($showId) {
        // Get all categories for this show
        $categories = $this->showModel->getShowCategories($showId);
        
        foreach ($categories as $category) {
            // Get all vehicles in this category
            $vehicles = $this->registrationModel->getVehiclesByCategory($showId, $category->id);
            
            foreach ($vehicles as $vehicle) {
                // Calculate weighted average score for this vehicle
                $finalScore = $this->calculateVehicleFinalScore($showId, $vehicle->id, $category->id);
                
                // Update vehicle with final score
                $this->registrationModel->updateVehicleFinalScore($vehicle->id, $finalScore);
            }
            
            // Determine winners for this category
            $this->determineCategoryWinners($showId, $category->id);
        }
        
        // Calculate overall show winners (Best in Show, etc.)
        $this->determineOverallWinners($showId);
    }

    /**
     * Calculate final weighted score for a vehicle
     */
    private function calculateVehicleFinalScore($showId, $vehicleId, $categoryId) {
        // Get all judge scores for this vehicle
        $scores = $this->judgingModel->getVehicleScores($vehicleId);
        
        if (empty($scores)) {
            return 0;
        }

        // Get judging metrics and their weights
        $metrics = $this->showModel->getShowMetrics($showId);
        $totalWeightedScore = 0;
        $totalWeight = 0;

        foreach ($metrics as $metric) {
            $metricScores = [];
            
            // Collect all judge scores for this metric
            foreach ($scores as $score) {
                if ($score->metric_id == $metric->id) {
                    $metricScores[] = $score->score;
                }
            }
            
            if (!empty($metricScores)) {
                // Calculate average score for this metric
                $avgScore = array_sum($metricScores) / count($metricScores);
                
                // Apply metric weight
                $weightedScore = $avgScore * $metric->weight;
                $totalWeightedScore += $weightedScore;
                $totalWeight += $metric->weight;
            }
        }

        // Return final weighted average (0-100 scale)
        return $totalWeight > 0 ? ($totalWeightedScore / $totalWeight) : 0;
    }

    /**
     * Determine winners for a specific category
     */
    private function determineCategoryWinners($showId, $categoryId) {
        // Get all vehicles in category ordered by final score
        $vehicles = $this->registrationModel->getVehiclesByCategory($showId, $categoryId, 'final_score DESC');
        
        $position = 1;
        foreach ($vehicles as $vehicle) {
            if ($vehicle->final_score > 0) { // Only award positions to scored vehicles
                // Update vehicle with placement
                $this->registrationModel->updateVehiclePlacement($vehicle->id, $position);
                
                // Award trophy/ribbon based on position
                $award = $this->determineAward($position);
                if ($award) {
                    $this->registrationModel->updateVehicleAward($vehicle->id, $award);
                }
                
                $position++;
                
                // Stop after 3rd place unless show has more award levels
                if ($position > 3) break;
            }
        }
    }

    /**
     * Determine overall show winners (Best in Show, etc.)
     */
    private function determineOverallWinners($showId) {
        // Get highest scoring vehicle overall for Best in Show
        $bestInShow = $this->registrationModel->getHighestScoringVehicle($showId);
        
        if ($bestInShow) {
            $this->registrationModel->updateVehicleAward($bestInShow->id, 'Best in Show');
        }
        
        // Could add other overall awards here (People's Choice, etc.)
    }

    /**
     * Send notifications to winners
     */
    private function sendWinnerNotifications($showId) {
        // Get all winners (vehicles with awards)
        $winners = $this->registrationModel->getShowWinners($showId);
        
        foreach ($winners as $winner) {
            // Send email notification to vehicle owner
            $this->sendWinnerEmail($winner);
            
            // Could also send push notifications here
        }
    }

    /**
     * Send winner email notification
     */
    private function sendWinnerEmail($winner) {
        // Implementation would depend on your email system
        // This is a placeholder for the email functionality
        
        $subject = "Congratulations! You won at " . $winner->show_name;
        $message = "Your " . $winner->year . " " . $winner->make . " " . $winner->model . 
                  " won " . $winner->award . " in the " . $winner->category_name . " category!";
        
        // Send email using your existing email system
        // mail($winner->owner_email, $subject, $message);
    }

    /**
     * Log show completion for audit trail
     */
    private function logShowCompletion($showId) {
        $logData = [
            'show_id' => $showId,
            'action' => 'show_completed',
            'user_id' => $_SESSION['user_id'],
            'timestamp' => date('Y-m-d H:i:s'),
            'details' => 'Show marked as complete and final results calculated'
        ];
        
        // Log to audit table (if you have one)
        // $this->auditModel->logAction($logData);
    }

    /**
     * Determine award based on placement
     */
    private function determineAward($position) {
        switch ($position) {
            case 1:
                return '1st Place';
            case 2:
                return '2nd Place';
            case 3:
                return '3rd Place';
            default:
                return null;
        }
    }

    /**
     * Get judging progress statistics for completion confirmation
     */
    private function getJudgingProgress($showId) {
        $stats = [
            'total_vehicles' => 0,
            'judged_vehicles' => 0,
            'total_categories' => 0,
            'completed_categories' => 0,
            'completion_percentage' => 0
        ];

        // Get total vehicles registered
        $stats['total_vehicles'] = $this->registrationModel->getRegistrationCount($showId);
        
        // Get vehicles with at least one score
        $stats['judged_vehicles'] = $this->judgingModel->getJudgedVehicleCount($showId);
        
        // Get category statistics
        $categories = $this->showModel->getShowCategories($showId);
        $stats['total_categories'] = count($categories);
        
        foreach ($categories as $category) {
            $categoryVehicles = $this->registrationModel->getVehiclesByCategory($showId, $category->id);
            $judgedInCategory = 0;
            
            foreach ($categoryVehicles as $vehicle) {
                if ($this->judgingModel->hasVehicleBeenJudged($vehicle->id)) {
                    $judgedInCategory++;
                }
            }
            
            if ($judgedInCategory == count($categoryVehicles) && count($categoryVehicles) > 0) {
                $stats['completed_categories']++;
            }
        }
        
        // Calculate completion percentage
        if ($stats['total_vehicles'] > 0) {
            $stats['completion_percentage'] = round(($stats['judged_vehicles'] / $stats['total_vehicles']) * 100);
        }

        return $stats;
    }
}
?>
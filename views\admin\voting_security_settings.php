<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Fan Voting Security Settings</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/admin/dashboard">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/admin/settings_judging">Judging Settings</a></li>
                        <li class="breadcrumb-item active">Voting Security</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <?php flash('admin_message'); ?>

    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Global Voting Security Configuration
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post" action="<?php echo URLROOT; ?>/admin/votingSecuritySettings">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <!-- Geofencing Settings -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    Geofencing & Location Verification
                                </h5>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="geofence_enabled" name="geofence_enabled" 
                                           <?php echo ($data['settings']['geofence_enabled'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="geofence_enabled">
                                        <strong>Enable GPS Geofencing</strong>
                                        <small class="d-block text-muted">Require voters to be within specified radius of show location</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="geofence_radius_miles" class="form-label">Geofence Radius</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="geofence_radius_miles" name="geofence_radius_miles" 
                                           value="<?php echo $data['settings']['geofence_radius_miles'] ?? 0.5; ?>" 
                                           min="0.1" max="10" step="0.1" required>
                                    <span class="input-group-text">miles</span>
                                </div>
                                <small class="text-muted">How close voters must be to the show location (0.1 - 10 miles)</small>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="require_gps" name="require_gps" 
                                           <?php echo ($data['settings']['require_gps'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="require_gps">
                                        <strong>Require GPS Verification</strong>
                                        <small class="d-block text-muted">Mandate GPS location for all votes</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="allow_ip_fallback" name="allow_ip_fallback" 
                                           <?php echo ($data['settings']['allow_ip_fallback'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="allow_ip_fallback">
                                        <strong>Allow IP Geolocation Fallback</strong>
                                        <small class="d-block text-muted">Use IP-based location when GPS is unavailable</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Timing Controls -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-clock me-2"></i>
                                    Voting Time Controls
                                </h5>
                            </div>
                            <div class="col-md-6">
                                <label for="voting_start_offset_hours" class="form-label">Voting Start Offset</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="voting_start_offset_hours" name="voting_start_offset_hours" 
                                           value="<?php echo $data['settings']['voting_start_offset_hours'] ?? -2; ?>" 
                                           min="-24" max="0" required>
                                    <span class="input-group-text">hours before show</span>
                                </div>
                                <small class="text-muted">When voting opens relative to show start time</small>
                            </div>
                            <div class="col-md-6">
                                <label for="voting_end_offset_hours" class="form-label">Voting End Offset</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="voting_end_offset_hours" name="voting_end_offset_hours" 
                                           value="<?php echo $data['settings']['voting_end_offset_hours'] ?? -1; ?>" 
                                           min="-24" max="0" required>
                                    <span class="input-group-text">hours before show end</span>
                                </div>
                                <small class="text-muted">When voting closes relative to show end time</small>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="cooling_off_period_minutes" class="form-label">Cooling-off Period</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="cooling_off_period_minutes" name="cooling_off_period_minutes" 
                                           value="<?php echo $data['settings']['cooling_off_period_minutes'] ?? 5; ?>" 
                                           min="0" max="60" required>
                                    <span class="input-group-text">minutes</span>
                                </div>
                                <small class="text-muted">Required wait time between viewing vehicles and voting</small>
                            </div>
                        </div>

                        <!-- Rate Limiting -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    Rate Limiting & Velocity Checks
                                </h5>
                            </div>
                            <div class="col-md-4">
                                <label for="max_votes_per_ip" class="form-label">Max Votes per IP</label>
                                <input type="number" class="form-control" id="max_votes_per_ip" name="max_votes_per_ip" 
                                       value="<?php echo $data['settings']['max_votes_per_ip'] ?? 1; ?>" 
                                       min="1" max="10" required>
                                <small class="text-muted">Maximum votes allowed per IP address</small>
                            </div>
                            <div class="col-md-4">
                                <label for="max_votes_per_device" class="form-label">Max Votes per Device</label>
                                <input type="number" class="form-control" id="max_votes_per_device" name="max_votes_per_device" 
                                       value="<?php echo $data['settings']['max_votes_per_device'] ?? 1; ?>" 
                                       min="1" max="10" required>
                                <small class="text-muted">Maximum votes allowed per device fingerprint</small>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="velocity_check_enabled" name="velocity_check_enabled" 
                                           <?php echo ($data['settings']['velocity_check_enabled'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="velocity_check_enabled">
                                        <strong>Enable Velocity Checking</strong>
                                        <small class="d-block text-muted">Detect rapid-fire voting attempts</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="velocity_window_seconds" class="form-label">Velocity Window</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="velocity_window_seconds" name="velocity_window_seconds" 
                                           value="<?php echo $data['settings']['velocity_window_seconds'] ?? 30; ?>" 
                                           min="10" max="300" required>
                                    <span class="input-group-text">seconds</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="max_votes_in_window" class="form-label">Max Votes in Window</label>
                                <input type="number" class="form-control" id="max_votes_in_window" name="max_votes_in_window" 
                                       value="<?php echo $data['settings']['max_votes_in_window'] ?? 1; ?>" 
                                       min="1" max="5" required>
                            </div>
                        </div>

                        <!-- Fraud Detection -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-search me-2"></i>
                                    Fraud Detection & Risk Scoring
                                </h5>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="fraud_detection_enabled" name="fraud_detection_enabled" 
                                           <?php echo ($data['settings']['fraud_detection_enabled'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="fraud_detection_enabled">
                                        <strong>Enable Automated Fraud Detection</strong>
                                        <small class="d-block text-muted">Automatically analyze voting patterns for suspicious activity</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="auto_flag_threshold" class="form-label">Auto-Flag Threshold</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="auto_flag_threshold" name="auto_flag_threshold" 
                                           value="<?php echo $data['settings']['auto_flag_threshold'] ?? 70; ?>" 
                                           min="0" max="100" required>
                                    <span class="input-group-text">risk score</span>
                                </div>
                                <small class="text-muted">Risk score threshold for automatically flagging votes for review</small>
                            </div>
                            <div class="col-md-6">
                                <label for="auto_block_threshold" class="form-label">Auto-Block Threshold</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="auto_block_threshold" name="auto_block_threshold" 
                                           value="<?php echo $data['settings']['auto_block_threshold'] ?? 90; ?>" 
                                           min="0" max="100" required>
                                    <span class="input-group-text">risk score</span>
                                </div>
                                <small class="text-muted">Risk score threshold for automatically blocking votes</small>
                            </div>
                        </div>

                        <!-- Social Verification -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fab fa-facebook me-2"></i>
                                    Social Media Verification
                                </h5>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="social_verification_enabled" name="social_verification_enabled" 
                                           <?php echo ($data['settings']['social_verification_enabled'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="social_verification_enabled">
                                        <strong>Enable Facebook Profile Verification</strong>
                                        <small class="d-block text-muted">Verify Facebook profiles for authenticity</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="min_facebook_friends" class="form-label">Minimum Facebook Friends</label>
                                <input type="number" class="form-control" id="min_facebook_friends" name="min_facebook_friends" 
                                       value="<?php echo $data['settings']['min_facebook_friends'] ?? 10; ?>" 
                                       min="0" max="1000" required>
                                <small class="text-muted">Minimum number of friends required for Facebook verification</small>
                            </div>
                            <div class="col-md-6">
                                <label for="min_facebook_account_age_days" class="form-label">Minimum Account Age</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="min_facebook_account_age_days" name="min_facebook_account_age_days" 
                                           value="<?php echo $data['settings']['min_facebook_account_age_days'] ?? 30; ?>" 
                                           min="0" max="3650" required>
                                    <span class="input-group-text">days</span>
                                </div>
                                <small class="text-muted">Minimum age required for Facebook accounts</small>
                            </div>
                        </div>

                        <!-- Advanced Security -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-shield-virus me-2"></i>
                                    Advanced Security Features
                                </h5>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="honeypot_enabled" name="honeypot_enabled" 
                                           <?php echo ($data['settings']['honeypot_enabled'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="honeypot_enabled">
                                        <strong>Enable Honeypot Detection</strong>
                                        <small class="d-block text-muted">Detect automated bot voting</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="coordinate_clustering_detection" name="coordinate_clustering_detection" 
                                           <?php echo ($data['settings']['coordinate_clustering_detection'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="coordinate_clustering_detection">
                                        <strong>Coordinate Clustering Detection</strong>
                                        <small class="d-block text-muted">Detect coordinated voting patterns</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Data Retention -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-database me-2"></i>
                                    Data Retention & Privacy
                                </h5>
                            </div>
                            <div class="col-md-6">
                                <label for="data_retention_days" class="form-label">Data Retention Period</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="data_retention_days" name="data_retention_days" 
                                           value="<?php echo $data['settings']['data_retention_days'] ?? 30; ?>" 
                                           min="7" max="365" required>
                                    <span class="input-group-text">days</span>
                                </div>
                                <small class="text-muted">How long to retain detailed voting security data</small>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="<?php echo URLROOT; ?>/admin/settings_judging" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i> Back to Judging Settings
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i> Save Security Settings
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>

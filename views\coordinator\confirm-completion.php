<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="text-center mb-4">
                <div class="completion-icon bg-danger bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                    <i class="fas fa-flag-checkered fa-2x text-white"></i>
                </div>
                <h1 class="display-5 fw-bold text-danger">Mark Show as Complete</h1>
                <p class="lead text-muted">Finalize results and declare winners for <strong><?php echo htmlspecialchars($data['show']->name); ?></strong></p>
            </div>

            <!-- Critical Warning -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-danger border-danger shadow-sm">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-exclamation-triangle fa-2x text-danger me-3 mt-1"></i>
                            <div>
                                <h4 class="alert-heading text-danger mb-3">⚠️ CRITICAL ACTION - READ CAREFULLY</h4>
                                <p class="mb-3"><strong>Marking this show as complete will:</strong></p>
                                <ul class="mb-3">
                                    <li><strong>Calculate final scores</strong> from all judge submissions</li>
                                    <li><strong>Determine winners</strong> in each category automatically</li>
                                    <li><strong>Lock all results permanently</strong> - no changes possible</li>
                                    <li><strong>Send winner notifications</strong> to all participants</li>
                                    <li><strong>Publish results publicly</strong> on the website</li>
                                </ul>
                                <div class="bg-white p-3 rounded border border-danger">
                                    <p class="text-danger mb-0"><i class="fas fa-lock me-2"></i><strong>THIS ACTION CANNOT BE UNDONE!</strong> Once completed, you cannot modify scores, change winners, or reopen judging.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Judging Progress Check -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-clipboard-check me-2"></i>Pre-Completion Checklist
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="text-info mb-3">Judging Progress:</h5>
                                    <div class="progress-stats">
                                        <div class="stat-item d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                                            <div>
                                                <i class="fas fa-car text-primary me-2"></i>
                                                <strong>Vehicles Judged:</strong>
                                            </div>
                                            <div class="text-end">
                                                <span class="h5 mb-0 <?php echo $data['judging_stats']['completion_percentage'] >= 100 ? 'text-success' : 'text-warning'; ?>">
                                                    <?php echo $data['judging_stats']['judged_vehicles']; ?> / <?php echo $data['judging_stats']['total_vehicles']; ?>
                                                </span>
                                                <div class="small text-muted"><?php echo $data['judging_stats']['completion_percentage']; ?>% Complete</div>
                                            </div>
                                        </div>
                                        
                                        <div class="stat-item d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                                            <div>
                                                <i class="fas fa-list text-success me-2"></i>
                                                <strong>Categories Complete:</strong>
                                            </div>
                                            <div class="text-end">
                                                <span class="h5 mb-0 <?php echo $data['judging_stats']['completed_categories'] == $data['judging_stats']['total_categories'] ? 'text-success' : 'text-warning'; ?>">
                                                    <?php echo $data['judging_stats']['completed_categories']; ?> / <?php echo $data['judging_stats']['total_categories']; ?>
                                                </span>
                                                <div class="small text-muted">Categories</div>
                                            </div>
                                        </div>

                                        <!-- Progress Bar -->
                                        <div class="mb-3">
                                            <label class="form-label small text-muted">Overall Completion</label>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar <?php echo $data['judging_stats']['completion_percentage'] >= 100 ? 'bg-success' : 'bg-warning'; ?>" 
                                                     role="progressbar" 
                                                     style="width: <?php echo $data['judging_stats']['completion_percentage']; ?>%"
                                                     aria-valuenow="<?php echo $data['judging_stats']['completion_percentage']; ?>" 
                                                     aria-valuemin="0" 
                                                     aria-valuemax="100">
                                                    <?php echo $data['judging_stats']['completion_percentage']; ?>%
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h5 class="text-info mb-3">Before You Continue:</h5>
                                    <div class="checklist">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="check1" <?php echo $data['judging_stats']['completion_percentage'] >= 100 ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="check1">
                                                All vehicles have been judged
                                                <?php if ($data['judging_stats']['completion_percentage'] >= 100): ?>
                                                    <i class="fas fa-check-circle text-success ms-2"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-exclamation-circle text-warning ms-2"></i>
                                                <?php endif; ?>
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="check2">
                                            <label class="form-check-label" for="check2">
                                                All judges have submitted their scores
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="check3">
                                            <label class="form-check-label" for="check3">
                                                No disputed or missing scores
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="check4">
                                            <label class="form-check-label" for="check4">
                                                Awards ceremony is ready
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="check5">
                                            <label class="form-check-label" for="check5">
                                                I understand this action cannot be undone
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- What Happens Next -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-magic me-2"></i>What Happens When You Click "Complete Show"
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="completion-flow">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5 class="text-success mb-3">Automatic Processing:</h5>
                                        <div class="process-steps">
                                            <div class="process-step d-flex align-items-start mb-3">
                                                <div class="step-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 0.8rem;">1</div>
                                                <div>
                                                    <strong>Score Calculation</strong>
                                                    <p class="small text-muted mb-0">System calculates weighted averages for all vehicles using judge scores</p>
                                                </div>
                                            </div>
                                            
                                            <div class="process-step d-flex align-items-start mb-3">
                                                <div class="step-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 0.8rem;">2</div>
                                                <div>
                                                    <strong>Winner Determination</strong>
                                                    <p class="small text-muted mb-0">Ranks vehicles by final score and assigns 1st, 2nd, 3rd place in each category</p>
                                                </div>
                                            </div>
                                            
                                            <div class="process-step d-flex align-items-start mb-3">
                                                <div class="step-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 0.8rem;">3</div>
                                                <div>
                                                    <strong>Best in Show</strong>
                                                    <p class="small text-muted mb-0">Determines overall highest scoring vehicle across all categories</p>
                                                </div>
                                            </div>
                                            
                                            <div class="process-step d-flex align-items-start">
                                                <div class="step-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 0.8rem;">4</div>
                                                <div>
                                                    <strong>Results Published</strong>
                                                    <p class="small text-muted mb-0">Winners are announced publicly and results become visible to all</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h5 class="text-success mb-3">Notifications Sent:</h5>
                                        <div class="notification-list">
                                            <div class="notification-item d-flex align-items-center mb-2">
                                                <i class="fas fa-trophy text-warning me-2"></i>
                                                <span class="small">Winners receive congratulations emails</span>
                                            </div>
                                            <div class="notification-item d-flex align-items-center mb-2">
                                                <i class="fas fa-envelope text-info me-2"></i>
                                                <span class="small">All participants get results summary</span>
                                            </div>
                                            <div class="notification-item d-flex align-items-center mb-2">
                                                <i class="fas fa-bell text-primary me-2"></i>
                                                <span class="small">Push notifications to mobile users</span>
                                            </div>
                                            <div class="notification-item d-flex align-items-center mb-2">
                                                <i class="fas fa-share text-success me-2"></i>
                                                <span class="small">Social media sharing enabled</span>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-4 p-3 bg-light rounded">
                                            <h6 class="text-success mb-2">After Completion:</h6>
                                            <ul class="small mb-0">
                                                <li>Results are permanently locked</li>
                                                <li>Show status changes to "Completed"</li>
                                                <li>Participants can view detailed scores</li>
                                                <li>You can download final reports</li>
                                                <li>Show appears in completed shows list</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Completion Form -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-hand-paper me-2"></i>Final Confirmation Required
                            </h4>
                        </div>
                        <div class="card-body">
                            <form action="<?php echo BASE_URL; ?>/showCompletion/markComplete" method="POST" id="completionForm">
                                <input type="hidden" name="show_id" value="<?php echo $data['show']->id; ?>">
                                
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="confirmation-section">
                                            <h5 class="text-danger mb-3">Type "COMPLETE" to confirm:</h5>
                                            <div class="mb-3">
                                                <input type="text" class="form-control form-control-lg" id="confirmText" placeholder="Type COMPLETE here" required>
                                                <div class="form-text">This ensures you understand the permanent nature of this action</div>
                                            </div>
                                            
                                            <div class="form-check mb-4">
                                                <input class="form-check-input" type="checkbox" id="finalConfirm" name="confirm_completion" value="yes" required>
                                                <label class="form-check-label fw-bold" for="finalConfirm">
                                                    I understand that marking this show as complete will trigger final scoring, determine winners, and lock all results permanently. This action cannot be undone.
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="action-summary bg-light p-3 rounded">
                                            <h6 class="text-danger mb-3">Show Summary:</h6>
                                            <ul class="list-unstyled small">
                                                <li><strong>Show:</strong> <?php echo htmlspecialchars($data['show']->name); ?></li>
                                                <li><strong>Date:</strong> <?php echo date('M j, Y', strtotime($data['show']->start_date)); ?></li>
                                                <li><strong>Vehicles:</strong> <?php echo $data['judging_stats']['total_vehicles']; ?></li>
                                                <li><strong>Categories:</strong> <?php echo $data['judging_stats']['total_categories']; ?></li>
                                                <li><strong>Judged:</strong> <?php echo $data['judging_stats']['completion_percentage']; ?>%</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-center mt-4">
                                    <a href="<?php echo BASE_URL; ?>/coordinator/showDetails/<?php echo $data['show']->id; ?>" class="btn btn-secondary btn-lg me-3">
                                        <i class="fas fa-arrow-left me-2"></i>Cancel - Go Back
                                    </a>
                                    <button type="submit" class="btn btn-danger btn-lg" id="completeBtn" disabled>
                                        <i class="fas fa-flag-checkered me-2"></i>Mark Show Complete
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmText = document.getElementById('confirmText');
    const finalConfirm = document.getElementById('finalConfirm');
    const completeBtn = document.getElementById('completeBtn');
    const form = document.getElementById('completionForm');
    
    function checkFormValidity() {
        const textValid = confirmText.value.toUpperCase() === 'COMPLETE';
        const checkboxValid = finalConfirm.checked;
        
        completeBtn.disabled = !(textValid && checkboxValid);
        
        if (textValid) {
            confirmText.classList.remove('is-invalid');
            confirmText.classList.add('is-valid');
        } else {
            confirmText.classList.remove('is-valid');
            if (confirmText.value.length > 0) {
                confirmText.classList.add('is-invalid');
            }
        }
    }
    
    confirmText.addEventListener('input', checkFormValidity);
    finalConfirm.addEventListener('change', checkFormValidity);
    
    form.addEventListener('submit', function(e) {
        if (!confirm('Are you absolutely sure you want to complete this show? This action cannot be undone and will immediately calculate final results and notify winners.')) {
            e.preventDefault();
            return false;
        }
        
        // Disable button to prevent double submission
        completeBtn.disabled = true;
        completeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    });
});
</script>

<style>
.completion-icon {
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.process-step .step-icon {
    font-size: 0.8rem;
    font-weight: bold;
}

.notification-item {
    padding: 0.25rem 0;
}

.confirmation-section input[type="text"].is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.17z'/%3e%3c/svg%3e");
}

.confirmation-section input[type="text"].is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}

@media (max-width: 768px) {
    .process-steps, .notification-list {
        margin-bottom: 2rem;
    }
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
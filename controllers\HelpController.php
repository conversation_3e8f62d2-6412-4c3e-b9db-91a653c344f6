<?php
/**
 * Help Controller
 * Handles all help-related pages and documentation
 */

class HelpController extends Controller {

    private $userModel;
    private $auth;

    public function __construct() {
        $this->userModel = $this->model('UserModel');
        $this->auth = new Auth();
    }
    
    /**
     * Help center index page
     */
    public function index() {
        $data = [
            'title' => 'Help Center'
        ];
        
        $this->view('help/index', $data);
    }
    
    /**
     * Getting Started Guide
     */
    public function getting_started() {
        $data = [
            'title' => 'Getting Started Guide'
        ];
        
        $this->view('help/getting-started', $data);
    }
    
    /**
     * Fan Voting Guide
     */
    public function fan_voting() {
        $data = [
            'title' => 'Fan Voting Guide - How to Vote at Car Shows'
        ];
        
        $this->view('help/fan-voting', $data);
    }
    
    /**
     * Voting Security Help Page
     */
    public function voting_security() {
        $data = [
            'title' => 'Voting Security Help - Appeal Blocked Votes'
        ];
        
        $this->view('help/voting_security', $data);
    }
    
    /**
     * Show Management Guide
     */
    public function show_management() {
        $data = [
            'title' => 'Show Management Guide'
        ];
        
        $this->view('help/show-management', $data);
    }
    
    /**
     * Registration Guide
     */
    public function registration() {
        $data = [
            'title' => 'Vehicle Registration Guide'
        ];
        
        $this->view('help/registration', $data);
    }
    
    /**
     * Judging Guide
     */
    public function judging() {
        $data = [
            'title' => 'Judging Guide'
        ];
        
        $this->view('help/judging', $data);
    }
    
    /**
     * PWA Installation Guide
     */
    public function pwa_installation() {
        $data = [
            'title' => 'PWA Installation Guide'
        ];
        
        $this->view('help/pwa-installation', $data);
    }
    
    /**
     * Contact Support
     */
    public function contact() {
        $data = [
            'title' => 'Contact Support'
        ];
        
        $this->view('help/contact', $data);
    }
    
    /**
     * FAQ Page
     */
    public function faq() {
        $data = [
            'title' => 'Frequently Asked Questions'
        ];
        
        $this->view('help/faq', $data);
    }
}

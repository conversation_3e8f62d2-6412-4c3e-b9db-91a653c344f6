-- Fan Voting Security Enhancement - Fixed Version
-- This script enhances the fan voting system with comprehensive security measures
-- Fixed to match your existing database structure

-- First, let's enhance the existing fan_votes table
ALTER TABLE fan_votes 
ADD COLUMN IF NOT EXISTS latitude DECIMAL(10, 8) NULL COMMENT 'Voter GPS latitude',
ADD COLUMN IF NOT EXISTS longitude DECIMAL(11, 8) NULL COMMENT 'Voter GPS longitude',
ADD COLUMN IF NOT EXISTS location_accuracy FLOAT NULL COMMENT 'GPS accuracy in meters',
ADD COLUMN IF NOT EXISTS device_fingerprint VARCHAR(255) NULL COMMENT 'Browser/device fingerprint hash',
ADD COLUMN IF NOT EXISTS user_agent TEXT NULL COMMENT 'Browser user agent string',
ADD COLUMN IF NOT EXISTS screen_resolution VARCHAR(20) NULL COMMENT 'Screen resolution (e.g., 1920x1080)',
ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) NULL COMMENT 'User timezone',
ADD COLUMN IF NOT EXISTS language VARCHAR(10) NULL COMMENT 'Browser language',
ADD COLUMN IF NOT EXISTS referrer TEXT NULL COMMENT 'Page referrer URL',
ADD COLUMN IF NOT EXISTS session_id VARCHAR(255) NULL COMMENT 'Session identifier',
ADD COLUMN IF NOT EXISTS vote_method ENUM('ip', 'facebook', 'manual_staff', 'manual_coordinator', 'manual_admin') DEFAULT 'ip' COMMENT 'How the vote was submitted',
ADD COLUMN IF NOT EXISTS manual_voter_name VARCHAR(255) NULL COMMENT 'Name for manually entered votes',
ADD COLUMN IF NOT EXISTS manual_voter_contact VARCHAR(255) NULL COMMENT 'Phone/email for manually entered votes',
ADD COLUMN IF NOT EXISTS manual_entered_by INT(10) UNSIGNED NULL COMMENT 'User ID who manually entered the vote',
ADD COLUMN IF NOT EXISTS risk_score INT DEFAULT 0 COMMENT 'Fraud risk score (0-100)',
ADD COLUMN IF NOT EXISTS security_flags JSON NULL COMMENT 'Security flags and warnings',
ADD COLUMN IF NOT EXISTS is_flagged BOOLEAN DEFAULT FALSE COMMENT 'Vote flagged for review',
ADD COLUMN IF NOT EXISTS is_approved BOOLEAN DEFAULT TRUE COMMENT 'Vote approved by admin/coordinator',
ADD COLUMN IF NOT EXISTS reviewed_by INT(10) UNSIGNED NULL COMMENT 'User ID who reviewed the vote',
ADD COLUMN IF NOT EXISTS reviewed_at DATETIME NULL COMMENT 'When vote was reviewed',
ADD COLUMN IF NOT EXISTS review_notes TEXT NULL COMMENT 'Admin notes about the vote',
ADD COLUMN IF NOT EXISTS distance_from_show DECIMAL(8, 3) NULL COMMENT 'Distance from show location in miles',
ADD COLUMN IF NOT EXISTS ip_country VARCHAR(2) NULL COMMENT 'Country code from IP geolocation',
ADD COLUMN IF NOT EXISTS ip_region VARCHAR(100) NULL COMMENT 'Region from IP geolocation',
ADD COLUMN IF NOT EXISTS ip_city VARCHAR(100) NULL COMMENT 'City from IP geolocation',
ADD COLUMN IF NOT EXISTS is_vpn BOOLEAN DEFAULT FALSE COMMENT 'Detected VPN/proxy usage',
ADD COLUMN IF NOT EXISTS is_mobile BOOLEAN DEFAULT FALSE COMMENT 'Vote submitted from mobile device',
ADD COLUMN IF NOT EXISTS vote_duration_seconds INT NULL COMMENT 'Time spent on voting page before submitting',
ADD COLUMN IF NOT EXISTS updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp';

-- Add indexes for performance
ALTER TABLE fan_votes 
ADD INDEX IF NOT EXISTS idx_fan_votes_location (latitude, longitude),
ADD INDEX IF NOT EXISTS idx_fan_votes_device (device_fingerprint),
ADD INDEX IF NOT EXISTS idx_fan_votes_flagged (is_flagged),
ADD INDEX IF NOT EXISTS idx_fan_votes_approved (is_approved),
ADD INDEX IF NOT EXISTS idx_fan_votes_risk (risk_score),
ADD INDEX IF NOT EXISTS idx_fan_votes_method (vote_method),
ADD INDEX IF NOT EXISTS idx_fan_votes_manual_by (manual_entered_by),
ADD INDEX IF NOT EXISTS idx_fan_votes_reviewed (reviewed_by),
ADD INDEX IF NOT EXISTS idx_fan_votes_distance (distance_from_show);

-- Create voting security settings table
CREATE TABLE IF NOT EXISTS voting_security_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    show_id INT(10) UNSIGNED NOT NULL,
    geofence_enabled BOOLEAN DEFAULT TRUE COMMENT 'Enable GPS geofencing',
    geofence_radius_miles DECIMAL(4, 2) DEFAULT 0.5 COMMENT 'Geofence radius in miles',
    require_gps BOOLEAN DEFAULT TRUE COMMENT 'Require GPS verification',
    allow_ip_fallback BOOLEAN DEFAULT TRUE COMMENT 'Allow IP geolocation as fallback',
    voting_start_offset_hours INT DEFAULT -2 COMMENT 'Hours before show start to allow voting',
    voting_end_offset_hours INT DEFAULT -1 COMMENT 'Hours before show end to stop voting',
    max_votes_per_ip INT DEFAULT 1 COMMENT 'Maximum votes per IP address',
    max_votes_per_device INT DEFAULT 1 COMMENT 'Maximum votes per device fingerprint',
    velocity_check_enabled BOOLEAN DEFAULT TRUE COMMENT 'Enable rapid voting detection',
    velocity_window_seconds INT DEFAULT 30 COMMENT 'Time window for velocity checking',
    max_votes_in_window INT DEFAULT 1 COMMENT 'Max votes allowed in velocity window',
    cooling_off_period_minutes INT DEFAULT 5 COMMENT 'Required wait time between page view and vote',
    fraud_detection_enabled BOOLEAN DEFAULT TRUE COMMENT 'Enable automated fraud detection',
    auto_flag_threshold INT DEFAULT 70 COMMENT 'Risk score threshold for auto-flagging',
    auto_block_threshold INT DEFAULT 90 COMMENT 'Risk score threshold for auto-blocking',
    social_verification_enabled BOOLEAN DEFAULT TRUE COMMENT 'Enable Facebook profile verification',
    min_facebook_friends INT DEFAULT 10 COMMENT 'Minimum Facebook friends for verification',
    min_facebook_account_age_days INT DEFAULT 30 COMMENT 'Minimum Facebook account age',
    honeypot_enabled BOOLEAN DEFAULT TRUE COMMENT 'Enable honeypot detection',
    coordinate_clustering_detection BOOLEAN DEFAULT TRUE COMMENT 'Detect coordinated voting patterns',
    data_retention_days INT DEFAULT 30 COMMENT 'Days to retain detailed voting data',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_show_settings (show_id),
    INDEX idx_security_show (show_id)
) COMMENT 'Security settings per show';

-- Create voting sessions table for tracking user behavior
CREATE TABLE IF NOT EXISTS voting_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    show_id INT(10) UNSIGNED NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    device_fingerprint VARCHAR(255) NULL,
    user_agent TEXT NULL,
    first_page_view DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    pages_viewed INT DEFAULT 1,
    vehicles_viewed JSON NULL COMMENT 'Array of vehicle IDs viewed',
    time_on_site_seconds INT DEFAULT 0,
    vote_submitted BOOLEAN DEFAULT FALSE,
    vote_id INT NULL COMMENT 'Reference to fan_votes.id if vote was submitted',
    suspicious_behavior JSON NULL COMMENT 'Flags for suspicious behavior',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_id (session_id),
    INDEX idx_show_session (show_id, session_id),
    INDEX idx_ip_session (ip_address),
    INDEX idx_device_session (device_fingerprint),
    INDEX idx_vote_submitted (vote_submitted)
) COMMENT 'Track voting sessions for behavior analysis';

-- Create fraud detection patterns table
CREATE TABLE IF NOT EXISTS fraud_detection_patterns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    show_id INT(10) UNSIGNED NOT NULL,
    pattern_type ENUM('velocity', 'clustering', 'device_sharing', 'suspicious_timing', 'honeypot', 'social_verification') NOT NULL,
    pattern_data JSON NOT NULL COMMENT 'Details about the detected pattern',
    affected_votes JSON NOT NULL COMMENT 'Array of vote IDs involved in pattern',
    risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    auto_flagged BOOLEAN DEFAULT FALSE COMMENT 'Automatically flagged by system',
    reviewed BOOLEAN DEFAULT FALSE COMMENT 'Reviewed by admin/coordinator',
    reviewed_by INT(10) UNSIGNED NULL,
    reviewed_at DATETIME NULL,
    action_taken ENUM('none', 'flagged', 'blocked', 'approved') DEFAULT 'none',
    notes TEXT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_pattern_show (show_id),
    INDEX idx_pattern_type (pattern_type),
    INDEX idx_pattern_risk (risk_level),
    INDEX idx_pattern_reviewed (reviewed)
) COMMENT 'Track detected fraud patterns';

-- Create voting alerts table
CREATE TABLE IF NOT EXISTS voting_alerts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    show_id INT(10) UNSIGNED NOT NULL,
    alert_type ENUM('fraud_detected', 'high_risk_vote', 'pattern_detected', 'manual_review_needed', 'system_error') NOT NULL,
    severity ENUM('info', 'warning', 'error', 'critical') DEFAULT 'warning',
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    alert_data JSON NULL COMMENT 'Additional alert data',
    sent_to_admin BOOLEAN DEFAULT FALSE,
    sent_to_coordinator BOOLEAN DEFAULT FALSE,
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by INT(10) UNSIGNED NULL,
    acknowledged_at DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_alert_show (show_id),
    INDEX idx_alert_type (alert_type),
    INDEX idx_alert_severity (severity),
    INDEX idx_alert_acknowledged (acknowledged)
) COMMENT 'System alerts for voting security';

-- Create honeypot votes table (fake voting options to catch bots)
CREATE TABLE IF NOT EXISTS honeypot_votes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    show_id INT(10) UNSIGNED NOT NULL,
    honeypot_identifier VARCHAR(255) NOT NULL COMMENT 'Hidden identifier for honeypot',
    ip_address VARCHAR(45) NOT NULL,
    device_fingerprint VARCHAR(255) NULL,
    user_agent TEXT NULL,
    session_id VARCHAR(255) NULL,
    detected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_honeypot_show (show_id),
    INDEX idx_honeypot_ip (ip_address),
    INDEX idx_honeypot_device (device_fingerprint)
) COMMENT 'Track honeypot interactions to detect bots';

-- Create vote appeals table
CREATE TABLE IF NOT EXISTS vote_appeals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    show_id INT(10) UNSIGNED NOT NULL,
    registration_id INT(10) UNSIGNED NOT NULL,
    voter_name VARCHAR(255) NOT NULL,
    voter_email VARCHAR(255) NOT NULL,
    voter_phone VARCHAR(20) NULL,
    appeal_reason ENUM('was_at_show', 'gps_error', 'network_issue', 'shared_device', 'technical_difficulty', 'other') NOT NULL,
    other_reason TEXT NULL,
    appeal_explanation TEXT NOT NULL,
    witness_name VARCHAR(255) NULL,
    witness_contact VARCHAR(255) NULL,
    social_media_proof TEXT NULL,
    receipt_or_ticket VARCHAR(255) NULL,
    status ENUM('pending', 'approved', 'denied', 'manual_entry') DEFAULT 'pending',
    reviewed_by INT(10) UNSIGNED NULL,
    reviewed_at DATETIME NULL,
    review_notes TEXT NULL,
    admin_response TEXT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_appeal_show (show_id),
    INDEX idx_appeal_status (status),
    INDEX idx_appeal_voter (voter_email),
    INDEX idx_appeal_reviewed (reviewed_by)
) COMMENT 'Vote appeals for blocked votes';

-- Create security reports table for exports
CREATE TABLE IF NOT EXISTS security_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    show_id INT(10) UNSIGNED NOT NULL,
    report_type ENUM('voting_summary', 'fraud_analysis', 'security_audit', 'appeal_summary') NOT NULL,
    report_data JSON NOT NULL,
    generated_by INT(10) UNSIGNED NOT NULL,
    generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    file_path VARCHAR(500) NULL,
    INDEX idx_report_show (show_id),
    INDEX idx_report_type (report_type),
    INDEX idx_report_generated (generated_by)
) COMMENT 'Generated security reports and exports';

-- Insert default security settings for existing shows (without foreign key constraints for now)
INSERT IGNORE INTO voting_security_settings (show_id)
SELECT id FROM shows 
WHERE id NOT IN (SELECT show_id FROM voting_security_settings WHERE show_id IS NOT NULL);

-- Create view for vote analysis
CREATE OR REPLACE VIEW vote_security_analysis AS
SELECT 
    fv.id,
    fv.show_id,
    fv.registration_id,
    fv.voter_ip,
    fv.vote_method,
    fv.risk_score,
    fv.is_flagged,
    fv.is_approved,
    fv.distance_from_show,
    fv.created_at,
    s.name as show_name,
    CASE
        WHEN fv.manual_entered_by IS NOT NULL THEN CONCAT('Manual by ', COALESCE(u.name, 'Unknown'))
        WHEN fv.fb_user_id IS NOT NULL THEN 'Facebook'
        ELSE 'IP-based'
    END as vote_source,
    CASE 
        WHEN fv.risk_score >= 90 THEN 'Critical'
        WHEN fv.risk_score >= 70 THEN 'High'
        WHEN fv.risk_score >= 40 THEN 'Medium'
        ELSE 'Low'
    END as risk_level
FROM fan_votes fv
JOIN shows s ON fv.show_id = s.id
LEFT JOIN users u ON fv.manual_entered_by = u.id;

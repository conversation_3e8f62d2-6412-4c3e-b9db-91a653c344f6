<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Home Page Hero Settings</h1>
            <p class="text-muted">Customize the hero section appearance on your home page</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Settings
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Hero Settings Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-image me-2"></i>Hero Section Configuration</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo BASE_URL; ?>/admin/settings_hero" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">

                        <!-- Background Image Settings -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3"><i class="fas fa-image me-2"></i>Background Image</h6>
                                
                                <!-- Image Source Selection -->
                                <div class="mb-3">
                                    <label class="form-label">Background Image Source</label>
                                    <div class="btn-group w-100" role="group" aria-label="Image source selection">
                                        <input type="radio" class="btn-check" name="image_source" id="source_url" value="url" checked>
                                        <label class="btn btn-outline-primary" for="source_url">
                                            <i class="fas fa-link me-2"></i>Use URL
                                        </label>

                                        <input type="radio" class="btn-check" name="image_source" id="source_upload" value="upload">
                                        <label class="btn btn-outline-primary" for="source_upload">
                                            <i class="fas fa-upload me-2"></i>Upload File
                                        </label>
                                    </div>
                                </div>

                                <!-- URL Input -->
                                <div class="mb-3" id="url_input_section">
                                    <label for="hero_bg_image" class="form-label">Background Image URL</label>
                                    <input type="url" class="form-control" id="hero_bg_image" name="hero_bg_image"
                                           value="<?php echo htmlspecialchars($data['settings']['hero_bg_image']); ?>"
                                           placeholder="https://example.com/image.jpg">
                                    <div class="form-text">Enter the full URL to your background image</div>
                                </div>

                                <!-- File Upload -->
                                <div class="mb-3 d-none" id="upload_input_section">
                                    <label for="hero_bg_file" class="form-label">Upload Background Image</label>
                                    <input type="file" class="form-control" id="hero_bg_file" name="hero_bg_file"
                                           accept="image/jpeg,image/jpg,image/png,image/gif,image/webp">
                                    <div class="form-text">
                                        Supported formats: JPG, PNG, GIF, WebP<br>
                                        Maximum file size: 10MB
                                    </div>
                                    <div id="file_preview" class="mt-2 d-none">
                                        <img id="preview_image" src="" alt="Preview" class="img-thumbnail" style="max-height: 150px;">
                                        <div class="mt-1">
                                            <small class="text-muted" id="file_info"></small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="hero_bg_size" class="form-label">Background Size</label>
                                        <select class="form-select" id="hero_bg_size" name="hero_bg_size">
                                            <option value="cover" <?php echo $data['settings']['hero_bg_size'] == 'cover' ? 'selected' : ''; ?>>Cover</option>
                                            <option value="contain" <?php echo $data['settings']['hero_bg_size'] == 'contain' ? 'selected' : ''; ?>>Contain</option>
                                            <option value="auto" <?php echo $data['settings']['hero_bg_size'] == 'auto' ? 'selected' : ''; ?>>Auto</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="hero_bg_position" class="form-label">Background Position</label>
                                        <select class="form-select" id="hero_bg_position" name="hero_bg_position">
                                            <option value="center" <?php echo $data['settings']['hero_bg_position'] == 'center' ? 'selected' : ''; ?>>Center</option>
                                            <option value="top" <?php echo $data['settings']['hero_bg_position'] == 'top' ? 'selected' : ''; ?>>Top</option>
                                            <option value="bottom" <?php echo $data['settings']['hero_bg_position'] == 'bottom' ? 'selected' : ''; ?>>Bottom</option>
                                            <option value="left" <?php echo $data['settings']['hero_bg_position'] == 'left' ? 'selected' : ''; ?>>Left</option>
                                            <option value="right" <?php echo $data['settings']['hero_bg_position'] == 'right' ? 'selected' : ''; ?>>Right</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="hero_bg_brightness" class="form-label">
                                            Background Brightness: <span id="bg_brightness_value"><?php echo $data['settings']['hero_bg_brightness']; ?>%</span>
                                        </label>
                                        <input type="range" class="form-range" id="hero_bg_brightness" name="hero_bg_brightness" 
                                               min="0" max="150" value="<?php echo $data['settings']['hero_bg_brightness']; ?>"
                                               oninput="document.getElementById('bg_brightness_value').textContent = this.value + '%'">
                                        <div class="form-text">Adjust image brightness (50% = normal)</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="hero_bg_transparency" class="form-label">
                                        Background Transparency: <span id="bg_transparency_value"><?php echo $data['settings']['hero_bg_transparency']; ?>%</span>
                                    </label>
                                    <input type="range" class="form-range" id="hero_bg_transparency" name="hero_bg_transparency" 
                                           min="0" max="100" value="<?php echo $data['settings']['hero_bg_transparency']; ?>"
                                           oninput="document.getElementById('bg_transparency_value').textContent = this.value + '%'">
                                    <div class="form-text">0% = fully opaque, 100% = fully transparent</div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Overlay Settings -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3"><i class="fas fa-layer-group me-2"></i>Overlay Settings</h6>
                                
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="hero_overlay_color" class="form-label">Overlay Color</label>
                                        <input type="color" class="form-control form-control-color" id="hero_overlay_color" name="hero_overlay_color" 
                                               value="<?php echo $data['settings']['hero_overlay_color']; ?>">
                                        <div class="form-text">Color for the overlay layer</div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="hero_overlay_brightness" class="form-label">
                                            Overlay Brightness: <span id="overlay_brightness_value"><?php echo $data['settings']['hero_overlay_brightness']; ?>%</span>
                                        </label>
                                        <input type="range" class="form-range" id="hero_overlay_brightness" name="hero_overlay_brightness" 
                                               min="0" max="150" value="<?php echo $data['settings']['hero_overlay_brightness']; ?>"
                                               oninput="document.getElementById('overlay_brightness_value').textContent = this.value + '%'">
                                        <div class="form-text">Adjust overlay brightness</div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="hero_overlay_transparency" class="form-label">
                                            Overlay Transparency: <span id="overlay_transparency_value"><?php echo $data['settings']['hero_overlay_transparency']; ?>%</span>
                                        </label>
                                        <input type="range" class="form-range" id="hero_overlay_transparency" name="hero_overlay_transparency" 
                                               min="0" max="100" value="<?php echo $data['settings']['hero_overlay_transparency']; ?>"
                                               oninput="document.getElementById('overlay_transparency_value').textContent = this.value + '%'">
                                        <div class="form-text">0% = fully opaque, 100% = fully transparent</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-primary" onclick="previewChanges()">
                                <i class="fas fa-eye me-2"></i>Preview Changes
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Panel -->
        <div class="col-lg-4">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-eye me-2"></i>Live Preview</h6>
                </div>
                <div class="card-body p-0">
                    <div id="hero-preview" class="position-relative" style="height: 300px; overflow: hidden;">
                        <!-- Background -->
                        <div id="preview-bg" class="position-absolute w-100 h-100" style="
                            background-image: url('<?php echo htmlspecialchars($data['settings']['hero_bg_image']); ?>');
                            background-size: <?php echo htmlspecialchars($data['settings']['hero_bg_size']); ?>;
                            background-position: <?php echo htmlspecialchars($data['settings']['hero_bg_position']); ?>;
                            background-repeat: no-repeat;
                            filter: brightness(<?php echo $data['settings']['hero_bg_brightness']; ?>%);
                            opacity: <?php echo (100 - intval($data['settings']['hero_bg_transparency'])) / 100; ?>;
                        "></div>
                        
                        <!-- Overlay -->
                        <div id="preview-overlay" class="position-absolute w-100 h-100" style="
                            background-color: <?php echo $data['settings']['hero_overlay_color']; ?>;
                            filter: brightness(<?php echo $data['settings']['hero_overlay_brightness']; ?>%);
                            opacity: <?php echo (100 - intval($data['settings']['hero_overlay_transparency'])) / 100; ?>;
                        "></div>
                        
                        <!-- Sample Content -->
                        <div class="position-absolute w-100 h-100 d-flex align-items-center justify-content-center text-white text-center">
                            <div>
                                <h4 class="fw-bold mb-2">Car Show Management</h4>
                                <p class="mb-0">Sample hero content</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tips -->
            <div class="card shadow-sm border-0 mt-3">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0 small">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Use high-resolution images (1920x1080 or larger)</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Darker overlays improve text readability</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Test on mobile devices for best results</li>
                        <li><i class="fas fa-check text-success me-2"></i>Use "Cover" size for full-screen backgrounds</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Handle image source selection
function toggleImageSource() {
    const urlRadio = document.getElementById('source_url');
    const uploadRadio = document.getElementById('source_upload');
    const urlSection = document.getElementById('url_input_section');
    const uploadSection = document.getElementById('upload_input_section');

    if (urlRadio.checked) {
        urlSection.classList.remove('d-none');
        uploadSection.classList.add('d-none');
        // Clear file input when switching to URL
        document.getElementById('hero_bg_file').value = '';
        document.getElementById('file_preview').classList.add('d-none');
    } else if (uploadRadio.checked) {
        urlSection.classList.add('d-none');
        uploadSection.classList.remove('d-none');
        // Clear URL input when switching to upload
        document.getElementById('hero_bg_image').value = '';
        previewChanges(); // Update preview to remove URL image
    }
}

// Handle file upload preview
function handleFileUpload(event) {
    const file = event.target.files[0];
    const preview = document.getElementById('file_preview');
    const previewImage = document.getElementById('preview_image');
    const fileInfo = document.getElementById('file_info');

    if (file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('Invalid file type. Please upload JPG, PNG, GIF, or WebP images only.');
            event.target.value = '';
            preview.classList.add('d-none');
            return;
        }

        // Validate file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('File size exceeds 10MB limit.');
            event.target.value = '';
            preview.classList.add('d-none');
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImage.src = e.target.result;
            preview.classList.remove('d-none');

            // Update live preview
            const previewBg = document.getElementById('preview-bg');
            previewBg.style.backgroundImage = `url('${e.target.result}')`;
            previewChanges(); // Apply other settings
        };
        reader.readAsDataURL(file);

        // Show file info
        fileInfo.textContent = `${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
    } else {
        preview.classList.add('d-none');
        previewChanges(); // Reset preview
    }
}

function previewChanges() {
    const urlRadio = document.getElementById('source_url');
    const bgImage = document.getElementById('hero_bg_image').value;
    const bgSize = document.getElementById('hero_bg_size').value;
    const bgPosition = document.getElementById('hero_bg_position').value;
    const bgBrightness = document.getElementById('hero_bg_brightness').value;
    const bgTransparency = document.getElementById('hero_bg_transparency').value;
    const overlayColor = document.getElementById('hero_overlay_color').value;
    const overlayBrightness = document.getElementById('hero_overlay_brightness').value;
    const overlayTransparency = document.getElementById('hero_overlay_transparency').value;

    // Update background (only if using URL source and no file is being previewed)
    const previewBg = document.getElementById('preview-bg');
    if (urlRadio.checked && bgImage && !document.getElementById('hero_bg_file').files[0]) {
        previewBg.style.backgroundImage = `url('${bgImage}')`;
    } else if (urlRadio.checked && !bgImage) {
        previewBg.style.backgroundImage = 'none';
    }
    // Note: File upload preview is handled in handleFileUpload function

    previewBg.style.backgroundSize = bgSize;
    previewBg.style.backgroundPosition = bgPosition;
    previewBg.style.filter = `brightness(${bgBrightness}%)`;
    previewBg.style.opacity = (100 - parseInt(bgTransparency)) / 100;

    // Update overlay
    const previewOverlay = document.getElementById('preview-overlay');
    previewOverlay.style.backgroundColor = overlayColor;
    previewOverlay.style.filter = `brightness(${overlayBrightness}%)`;
    previewOverlay.style.opacity = (100 - parseInt(overlayTransparency)) / 100;
}

// Auto-update preview when inputs change
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['hero_bg_image', 'hero_bg_size', 'hero_bg_position', 'hero_bg_brightness', 'hero_bg_transparency',
                   'hero_overlay_color', 'hero_overlay_brightness', 'hero_overlay_transparency'];

    inputs.forEach(function(inputId) {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', previewChanges);
            input.addEventListener('change', previewChanges);
        }
    });

    // Add event listeners for image source selection
    document.getElementById('source_url').addEventListener('change', toggleImageSource);
    document.getElementById('source_upload').addEventListener('change', toggleImageSource);

    // Add event listener for file upload
    document.getElementById('hero_bg_file').addEventListener('change', handleFileUpload);

    // Initialize the correct section based on current state
    toggleImageSource();
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
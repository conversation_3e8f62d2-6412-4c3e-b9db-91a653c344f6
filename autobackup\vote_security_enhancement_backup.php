<?php 
// Ensure APPROOT is defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(dirname(dirname(__FILE__))));
}

// Make APPROOT available globally
if (!isset($GLOBALS['APPROOT'])) {
    $GLOBALS['APPROOT'] = APPROOT;
}

require APPROOT . '/views/includes/header.php'; 
?>

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-12 col-xl-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h2 class="h4 mb-0">Fan Favorite Voting</h2>
                </div>
                <div class="card-body">
                    <?php flash('vote_message'); ?>
                    
                    <div class="text-center mb-4">
                        <h3><?php echo $data['show']->name; ?></h3>
                        <p class="lead">Vote for your favorite vehicle!</p>
                    </div>
                    
                    <?php if(isset($data['registration'])): ?>
                        <!-- Direct voting for a specific vehicle -->
                        <div class="row">
                            <div class="col-md-6 mx-auto">
                                <div class="card mb-4">
                                    <div class="card-body text-center">
                                        <?php if(isset($data['vehicle_image']) && !empty($data['vehicle_image'])): ?>
                                            <img src="<?php echo URLROOT . '/' . $data['vehicle_image']; ?>" alt="Vehicle Image" class="img-fluid mb-3" style="max-height: 200px;">
                                        <?php else: ?>
                                            <div class="bg-light p-3 mb-3 text-center">
                                                <i class="fas fa-car fa-3x text-muted"></i>
                                                <p class="mt-2 text-muted">No image available</p>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <h4><?php echo $data['vehicle']->year . ' ' . $data['vehicle']->make . ' ' . $data['vehicle']->model; ?></h4>
                                        <p class="text-muted">Registration #: <?php echo $data['registration']->registration_number; ?></p>
                                        <p>Category: <?php echo $data['category']->name; ?></p>
                                        
                                        <!-- Voting Form -->
                                        <form action="<?php echo URLROOT; ?>/show/vote/<?php echo $data['show']->id; ?>/<?php echo $data['registration']->id; ?>" method="post" id="voteForm">
                                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                            <input type="hidden" name="registration_id" value="<?php echo $data['registration']->id; ?>">
                                            
                                            <div class="d-grid gap-2">
                                                <!-- IP-based voting -->
                                                <button type="submit" name="vote_type" value="ip" class="btn btn-primary btn-lg mb-2">
                                                    <i class="fas fa-thumbs-up me-2"></i> Vote Now
                                                </button>
                                                
                                                <!-- Facebook login voting -->
                                                <div class="text-center my-3">
                                                    <p class="text-muted">- OR -</p>
                                                </div>
                                                
                                                <button type="button" id="fbLoginBtn" class="btn btn-facebook btn-lg">
                                                    <i class="fab fa-facebook-f me-2"></i> Vote with Facebook
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Show all vehicles for voting -->
                        <div class="row">
                            <?php foreach($data['vehicles'] as $vehicle): ?>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-4">
                                    <div class="card h-100" style="border: none; border-radius: 12px; box-shadow: 0 6px 15px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.3s ease;">
                                        <?php 
                                        $vehicleImage = '';
                                        foreach($vehicle->images as $image) {
                                            if($image->is_primary) {
                                                // Check if thumbnail_path exists and is not empty
                                                if(isset($image->thumbnail_path) && !empty($image->thumbnail_path)) {
                                                    $vehicleImage = $image->thumbnail_path;
                                                } 
                                                // Otherwise use file_path
                                                else if(isset($image->file_path) && !empty($image->file_path)) {
                                                    $vehicleImage = $image->file_path;
                                                }
                                                break;
                                            }
                                        }
                                        
                                        // If no primary image was found, use the first image
                                        if(empty($vehicleImage) && !empty($vehicle->images)) {
                                            $firstImage = $vehicle->images[0];
                                            if(isset($firstImage->thumbnail_path) && !empty($firstImage->thumbnail_path)) {
                                                $vehicleImage = $firstImage->thumbnail_path;
                                            } else if(isset($firstImage->file_path) && !empty($firstImage->file_path)) {
                                                $vehicleImage = $firstImage->file_path;
                                            }
                                        }
                                        ?>
                                        
                                        <div class="vehicle-image-container" style="aspect-ratio: 4/3; overflow: hidden;">
                                            <?php if(!empty($vehicleImage)): ?>
                                                <?php
                                                // Prefer file_path (full-size image) over thumbnail
                                                $imagePath = '';
                                                if(isset($image->file_path) && !empty($image->file_path)) {
                                                    $imagePath = $image->file_path;
                                                } else {
                                                    $imagePath = $vehicleImage;
                                                }
                                                ?>
                                                <img src="<?php echo URLROOT . '/' . $imagePath; ?>" class="card-img-top" alt="Vehicle Image" style="width: 100%; height: 100%; object-fit: contain; transition: transform 0.3s ease;">
                                            <?php else: ?>
                                                <div class="bg-light d-flex flex-column align-items-center justify-content-center" style="height: 100%;">
                                                    <i class="fas fa-car fa-4x text-muted mt-4"></i>
                                                    <p class="mt-2 text-muted">No image available</p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="card-body">
                                            <div class="text-center mb-3">
                                                <span class="badge bg-primary" style="font-size: 1.1rem; padding: 6px 12px;">
                                                    Display #: <?php echo isset($vehicle->display_number) && !empty($vehicle->display_number) ? $vehicle->display_number : $vehicle->registration_number; ?>
                                                </span>
                                            </div>
                                            
                                            <h5 class="card-title text-center" style="font-weight: 600; color: #343a40;">
                                                <?php echo $vehicle->year . ' ' . $vehicle->make . ' ' . $vehicle->model; ?>
                                            </h5>
                                            
                                            <p class="card-text text-center">Category: <?php echo $vehicle->category_name; ?></p>
                                            
                                            <div class="d-grid mt-3">
                                                <a href="<?php echo URLROOT; ?>/show/vote/<?php echo $data['show']->id; ?>/<?php echo $vehicle->registration_id; ?>" 
                                                   class="btn btn-primary" 
                                                   style="padding: 10px; font-weight: 500; border-radius: 30px; box-shadow: 0 4px 8px rgba(0,123,255,0.3);">
                                                    <i class="fas fa-thumbs-up me-2"></i> Vote
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Facebook SDK -->
<div id="fb-root"></div>
<script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Facebook SDK
    window.fbAsyncInit = function() {
        FB.init({
            appId: '<?php echo isset($data['fb_app_id']) ? $data['fb_app_id'] : '123456789012345'; ?>',
            cookie: true,
            xfbml: true,
            version: 'v18.0'
        });
    };
    
    // Facebook Login Handler
    document.getElementById('fbLoginBtn')?.addEventListener('click', function() {
        FB.login(function(response) {
            if (response.authResponse) {
                // Get user info
                FB.api('/me', {fields: 'id,name,email'}, function(userInfo) {
                    // Add FB user info to form
                    const form = document.getElementById('voteForm');
                    
                    // Create hidden fields for FB data
                    const fbIdInput = document.createElement('input');
                    fbIdInput.type = 'hidden';
                    fbIdInput.name = 'fb_user_id';
                    fbIdInput.value = userInfo.id;
                    form.appendChild(fbIdInput);
                    
                    const fbNameInput = document.createElement('input');
                    fbNameInput.type = 'hidden';
                    fbNameInput.name = 'fb_user_name';
                    fbNameInput.value = userInfo.name;
                    form.appendChild(fbNameInput);
                    
                    const fbEmailInput = document.createElement('input');
                    fbEmailInput.type = 'hidden';
                    fbEmailInput.name = 'fb_user_email';
                    fbEmailInput.value = userInfo.email || '';
                    form.appendChild(fbEmailInput);
                    
                    const voteTypeInput = document.createElement('input');
                    voteTypeInput.type = 'hidden';
                    voteTypeInput.name = 'vote_type';
                    voteTypeInput.value = 'facebook';
                    form.appendChild(voteTypeInput);
                    
                    // Submit the form
                    form.submit();
                });
            } else {
                console.log('Facebook login cancelled or failed');
            }
        }, {scope: 'public_profile,email'});
    });
});
</script>

<style>
.btn-facebook {
    background-color: #3b5998;
    color: white;
}
.btn-facebook:hover {
    background-color: #2d4373;
    color: white;
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
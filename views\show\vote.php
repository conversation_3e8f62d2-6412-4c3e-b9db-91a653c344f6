<?php 
// Ensure APPROOT is defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(dirname(dirname(__FILE__))));
}

// Make APPROOT available globally
if (!isset($GLOBALS['APPROOT'])) {
    $GLOBALS['APPROOT'] = APPROOT;
}

require APPROOT . '/views/includes/header.php'; 
?>

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-12 col-xl-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h2 class="h4 mb-0">Fan Favorite Voting</h2>
                    <a href="<?php echo BASE_URL; ?>/help/voting_security" class="btn btn-light btn-sm" target="_blank">
                        <i class="fas fa-question-circle me-1"></i>Need Help?
                    </a>
                </div>
                <div class="card-body">
                    <?php flash('vote_message'); ?>
                    
                    <div class="text-center mb-4">
                        <h3><?php echo $data['show']->name; ?></h3>
                        <p class="lead">Vote for your favorite vehicle!</p>
                    </div>
                    
                    <?php if(isset($data['registration'])): ?>
                        <!-- Direct voting for a specific vehicle -->
                        <div class="row">
                            <div class="col-md-6 mx-auto">
                                <div class="card mb-4">
                                    <div class="card-body text-center">
                                        <?php if(isset($data['vehicle_image']) && !empty($data['vehicle_image'])): ?>
                                            <img src="<?php echo URLROOT . '/' . $data['vehicle_image']; ?>" alt="Vehicle Image" class="img-fluid mb-3" style="max-height: 200px;">
                                        <?php else: ?>
                                            <div class="bg-light p-3 mb-3 text-center">
                                                <i class="fas fa-car fa-3x text-muted"></i>
                                                <p class="mt-2 text-muted">No image available</p>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <h4><?php echo $data['vehicle']->year . ' ' . $data['vehicle']->make . ' ' . $data['vehicle']->model; ?></h4>
                                        <p class="text-muted">Registration #: <?php echo $data['registration']->registration_number; ?></p>
                                        <p>Category: <?php echo $data['category']->name; ?></p>
                                        
                                        <!-- Voting Form -->
                                        <form action="<?php echo URLROOT; ?>/show/vote/<?php echo $data['show']->id; ?>/<?php echo $data['registration']->id; ?>" method="post" id="voteForm">
                                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                            <input type="hidden" name="registration_id" value="<?php echo $data['registration']->id; ?>">

                                            <!-- Security fields -->
                                            <input type="hidden" name="latitude" id="latitude">
                                            <input type="hidden" name="longitude" id="longitude">
                                            <input type="hidden" name="location_accuracy" id="location_accuracy">
                                            <input type="hidden" name="device_fingerprint" id="device_fingerprint">
                                            <input type="hidden" name="screen_resolution" id="screen_resolution">
                                            <input type="hidden" name="timezone" id="timezone">
                                            <input type="hidden" name="language" id="language">
                                            <input type="hidden" name="platform" id="platform">
                                            <input type="hidden" name="page_load_time" id="page_load_time">

                                            <!-- Honeypot field (hidden from users, bots might fill it) -->
                                            <input type="text" name="honeypot_field" id="honeypot_field" style="position: absolute; left: -9999px; opacity: 0;" tabindex="-1" autocomplete="off">

                                            <!-- Location verification status -->
                                            <div id="locationStatus" class="alert alert-info" style="display: none;">
                                                <i class="fas fa-map-marker-alt me-2"></i>
                                                <span id="locationMessage">Verifying your location...</span>
                                            </div>

                                            <div class="d-grid gap-2">
                                                <!-- IP-based voting -->
                                                <button type="submit" name="vote_type" value="ip" class="btn btn-primary btn-lg mb-2" id="voteBtn">
                                                    <i class="fas fa-thumbs-up me-2"></i> Vote Now
                                                </button>

                                                <!-- Facebook login voting -->
                                                <div class="text-center my-3">
                                                    <p class="text-muted">- OR -</p>
                                                </div>

                                                <button type="button" id="fbLoginBtn" class="btn btn-facebook btn-lg">
                                                    <i class="fab fa-facebook-f me-2"></i> Vote with Facebook
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Vehicle Search and Filters -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <!-- Search -->
                                            <div class="col-md-6">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" id="vehicleSearch" placeholder="Search by make, model, year, or registration..." value="<?php echo htmlspecialchars($data['search']); ?>">
                                                    <button class="btn btn-outline-secondary" type="button" onclick="searchVehicles()">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Category Filter -->
                                            <div class="col-md-3">
                                                <select class="form-select" id="categoryFilter" onchange="filterByCategory()">
                                                    <option value="">All Categories</option>
                                                    <?php foreach($data['categories'] as $category): ?>
                                                        <option value="<?php echo $category->id; ?>" <?php echo $data['selected_category'] == $category->id ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($category->name); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>

                                            <!-- Results Per Page -->
                                            <div class="col-md-3">
                                                <select class="form-select" id="limitSelect" onchange="changeLimit()">
                                                    <option value="6" <?php echo $data['limit'] == 6 ? 'selected' : ''; ?>>6 per page</option>
                                                    <option value="12" <?php echo $data['limit'] == 12 ? 'selected' : ''; ?>>12 per page</option>
                                                    <option value="24" <?php echo $data['limit'] == 24 ? 'selected' : ''; ?>>24 per page</option>
                                                    <option value="48" <?php echo $data['limit'] == 48 ? 'selected' : ''; ?>>48 per page</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Results Info -->
                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        Showing <?php echo count($data['vehicles']); ?> of <?php echo number_format($data['total_vehicles']); ?> vehicles
                                                        <?php if ($data['current_page'] > 1): ?>
                                                            (Page <?php echo $data['current_page']; ?> of <?php echo $data['total_pages']; ?>)
                                                        <?php endif; ?>
                                                    </small>

                                                    <?php if ($data['total_pages'] > 1): ?>
                                                    <nav aria-label="Vehicle pagination">
                                                        <ul class="pagination pagination-sm mb-0">
                                                            <?php if ($data['current_page'] > 1): ?>
                                                                <li class="page-item">
                                                                    <a class="page-link" href="?page=<?php echo $data['current_page'] - 1; ?>&search=<?php echo urlencode($data['search']); ?>&category=<?php echo $data['selected_category']; ?>&limit=<?php echo $data['limit']; ?>">Previous</a>
                                                                </li>
                                                            <?php endif; ?>

                                                            <?php
                                                            $start = max(1, $data['current_page'] - 2);
                                                            $end = min($data['total_pages'], $data['current_page'] + 2);
                                                            ?>

                                                            <?php for ($i = $start; $i <= $end; $i++): ?>
                                                                <li class="page-item <?php echo $i == $data['current_page'] ? 'active' : ''; ?>">
                                                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($data['search']); ?>&category=<?php echo $data['selected_category']; ?>&limit=<?php echo $data['limit']; ?>"><?php echo $i; ?></a>
                                                                </li>
                                                            <?php endfor; ?>

                                                            <?php if ($data['current_page'] < $data['total_pages']): ?>
                                                                <li class="page-item">
                                                                    <a class="page-link" href="?page=<?php echo $data['current_page'] + 1; ?>&search=<?php echo urlencode($data['search']); ?>&category=<?php echo $data['selected_category']; ?>&limit=<?php echo $data['limit']; ?>">Next</a>
                                                                </li>
                                                            <?php endif; ?>
                                                        </ul>
                                                    </nav>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Vehicle Grid -->
                        <?php if (!empty($data['vehicles'])): ?>
                        <div class="row" id="vehicleGrid">
                            <?php foreach($data['vehicles'] as $vehicle): ?>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-4">
                                    <div class="card h-100" style="border: none; border-radius: 12px; box-shadow: 0 6px 15px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.3s ease;">
                                        <?php 
                                        $vehicleImage = '';
                                        foreach($vehicle->images as $image) {
                                            if($image->is_primary) {
                                                // Check if thumbnail_path exists and is not empty
                                                if(isset($image->thumbnail_path) && !empty($image->thumbnail_path)) {
                                                    $vehicleImage = $image->thumbnail_path;
                                                } 
                                                // Otherwise use file_path
                                                else if(isset($image->file_path) && !empty($image->file_path)) {
                                                    $vehicleImage = $image->file_path;
                                                }
                                                break;
                                            }
                                        }
                                        
                                        // If no primary image was found, use the first image
                                        if(empty($vehicleImage) && !empty($vehicle->images)) {
                                            $firstImage = $vehicle->images[0];
                                            if(isset($firstImage->thumbnail_path) && !empty($firstImage->thumbnail_path)) {
                                                $vehicleImage = $firstImage->thumbnail_path;
                                            } else if(isset($firstImage->file_path) && !empty($firstImage->file_path)) {
                                                $vehicleImage = $firstImage->file_path;
                                            }
                                        }
                                        ?>
                                        
                                        <div class="vehicle-image-container" style="aspect-ratio: 4/3; overflow: hidden;">
                                            <?php if(!empty($vehicleImage)): ?>
                                                <?php
                                                // Prefer file_path (full-size image) over thumbnail
                                                $imagePath = '';
                                                if(isset($image->file_path) && !empty($image->file_path)) {
                                                    $imagePath = $image->file_path;
                                                } else {
                                                    $imagePath = $vehicleImage;
                                                }
                                                ?>
                                                <img src="<?php echo URLROOT . '/' . $imagePath; ?>" class="card-img-top" alt="Vehicle Image" style="width: 100%; height: 100%; object-fit: contain; transition: transform 0.3s ease;">
                                            <?php else: ?>
                                                <div class="bg-light d-flex flex-column align-items-center justify-content-center" style="height: 100%;">
                                                    <i class="fas fa-car fa-4x text-muted mt-4"></i>
                                                    <p class="mt-2 text-muted">No image available</p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="card-body">
                                            <div class="text-center mb-3">
                                                <span class="badge bg-primary" style="font-size: 1.1rem; padding: 6px 12px;">
                                                    Display #: <?php echo isset($vehicle->display_number) && !empty($vehicle->display_number) ? $vehicle->display_number : $vehicle->registration_number; ?>
                                                </span>
                                            </div>
                                            
                                            <h5 class="card-title text-center" style="font-weight: 600; color: #343a40;">
                                                <?php echo $vehicle->year . ' ' . $vehicle->make . ' ' . $vehicle->model; ?>
                                            </h5>
                                            
                                            <p class="card-text text-center">Category: <?php echo $vehicle->category_name; ?></p>
                                            
                                            <div class="d-grid mt-3">
                                                <a href="<?php echo URLROOT; ?>/show/vote/<?php echo $data['show']->id; ?>/<?php echo $vehicle->registration_id; ?>" 
                                                   class="btn btn-primary" 
                                                   style="padding: 10px; font-weight: 500; border-radius: 30px; box-shadow: 0 4px 8px rgba(0,123,255,0.3);">
                                                    <i class="fas fa-thumbs-up me-2"></i> Vote
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Bottom Pagination -->
                        <?php if ($data['total_pages'] > 1): ?>
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="Vehicle pagination">
                                <ul class="pagination">
                                    <?php if ($data['current_page'] > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $data['current_page'] - 1; ?>&search=<?php echo urlencode($data['search']); ?>&category=<?php echo $data['selected_category']; ?>&limit=<?php echo $data['limit']; ?>">Previous</a>
                                        </li>
                                    <?php endif; ?>

                                    <?php
                                    $start = max(1, $data['current_page'] - 5);
                                    $end = min($data['total_pages'], $data['current_page'] + 5);
                                    ?>

                                    <?php if ($start > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1&search=<?php echo urlencode($data['search']); ?>&category=<?php echo $data['selected_category']; ?>&limit=<?php echo $data['limit']; ?>">1</a>
                                        </li>
                                        <?php if ($start > 2): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php for ($i = $start; $i <= $end; $i++): ?>
                                        <li class="page-item <?php echo $i == $data['current_page'] ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($data['search']); ?>&category=<?php echo $data['selected_category']; ?>&limit=<?php echo $data['limit']; ?>"><?php echo $i; ?></a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($end < $data['total_pages']): ?>
                                        <?php if ($end < $data['total_pages'] - 1): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif; ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $data['total_pages']; ?>&search=<?php echo urlencode($data['search']); ?>&category=<?php echo $data['selected_category']; ?>&limit=<?php echo $data['limit']; ?>"><?php echo $data['total_pages']; ?></a>
                                        </li>
                                    <?php endif; ?>

                                    <?php if ($data['current_page'] < $data['total_pages']): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $data['current_page'] + 1; ?>&search=<?php echo urlencode($data['search']); ?>&category=<?php echo $data['selected_category']; ?>&limit=<?php echo $data['limit']; ?>">Next</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                        <?php endif; ?>

                        <?php else: ?>
                        <!-- No vehicles found -->
                        <div class="text-center py-5">
                            <i class="fas fa-car fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No vehicles found</h5>
                            <p class="text-muted">
                                <?php if (!empty($data['search']) || !empty($data['selected_category'])): ?>
                                    No vehicles match your search criteria. Try adjusting your filters.
                                <?php else: ?>
                                    No vehicles are currently registered for voting in this show.
                                <?php endif; ?>
                            </p>
                            <?php if (!empty($data['search']) || !empty($data['selected_category'])): ?>
                            <a href="<?php echo BASE_URL; ?>/show/vote/<?php echo $data['show']->id; ?>" class="btn btn-outline-primary">
                                <i class="fas fa-times me-2"></i>Clear Filters
                            </a>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Facebook SDK -->
<div id="fb-root"></div>
<script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js"></script>

<script>
// Search and filter functions
function searchVehicles() {
    const search = document.getElementById('vehicleSearch').value;
    const category = document.getElementById('categoryFilter').value;
    const limit = document.getElementById('limitSelect').value;

    const params = new URLSearchParams();
    if (search) params.set('search', search);
    if (category) params.set('category', category);
    if (limit) params.set('limit', limit);
    params.set('page', '1'); // Reset to first page

    window.location.href = '?' + params.toString();
}

function filterByCategory() {
    searchVehicles();
}

function changeLimit() {
    searchVehicles();
}

// Enter key search
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('vehicleSearch')?.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchVehicles();
        }
    });
});

document.addEventListener('DOMContentLoaded', function() {
    // Record page load time for security analysis
    document.getElementById('page_load_time').value = Date.now();

    // Collect device fingerprinting data
    collectDeviceFingerprint();

    // Get location for geofencing
    getLocationForVoting();

    // Initialize Facebook SDK
    window.fbAsyncInit = function() {
        FB.init({
            appId: '<?php echo isset($data['fb_app_id']) ? $data['fb_app_id'] : '123456789012345'; ?>',
            cookie: true,
            xfbml: true,
            version: 'v18.0'
        });
    };
    
    // Facebook Login Handler
    document.getElementById('fbLoginBtn')?.addEventListener('click', function() {
        FB.login(function(response) {
            if (response.authResponse) {
                // Get user info
                FB.api('/me', {fields: 'id,name,email'}, function(userInfo) {
                    // Add FB user info to form
                    const form = document.getElementById('voteForm');
                    
                    // Create hidden fields for FB data
                    const fbIdInput = document.createElement('input');
                    fbIdInput.type = 'hidden';
                    fbIdInput.name = 'fb_user_id';
                    fbIdInput.value = userInfo.id;
                    form.appendChild(fbIdInput);
                    
                    const fbNameInput = document.createElement('input');
                    fbNameInput.type = 'hidden';
                    fbNameInput.name = 'fb_user_name';
                    fbNameInput.value = userInfo.name;
                    form.appendChild(fbNameInput);
                    
                    const fbEmailInput = document.createElement('input');
                    fbEmailInput.type = 'hidden';
                    fbEmailInput.name = 'fb_user_email';
                    fbEmailInput.value = userInfo.email || '';
                    form.appendChild(fbEmailInput);
                    
                    const voteTypeInput = document.createElement('input');
                    voteTypeInput.type = 'hidden';
                    voteTypeInput.name = 'vote_type';
                    voteTypeInput.value = 'facebook';
                    form.appendChild(voteTypeInput);
                    
                    // Submit the form
                    form.submit();
                });
            } else {
                console.log('Facebook login cancelled or failed');
            }
        }, {scope: 'public_profile,email'});
    });

    // Prevent double-clicking on vote buttons
    document.getElementById('voteBtn')?.addEventListener('click', function() {
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Submitting...';
        setTimeout(() => {
            document.getElementById('voteForm').submit();
        }, 100);
    });

    document.getElementById('fbLoginBtn')?.addEventListener('click', function() {
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Connecting...';
    });
});

// Collect device fingerprinting data
function collectDeviceFingerprint() {
    // Screen resolution
    document.getElementById('screen_resolution').value = screen.width + 'x' + screen.height;

    // Timezone
    document.getElementById('timezone').value = Intl.DateTimeFormat().resolvedOptions().timeZone;

    // Language
    document.getElementById('language').value = navigator.language || navigator.userLanguage;

    // Platform
    document.getElementById('platform').value = navigator.platform;

    // Generate device fingerprint hash
    const fingerprint = [
        navigator.userAgent,
        screen.width + 'x' + screen.height,
        Intl.DateTimeFormat().resolvedOptions().timeZone,
        navigator.language,
        navigator.platform,
        navigator.hardwareConcurrency || 'unknown',
        navigator.deviceMemory || 'unknown'
    ].join('|');

    // Simple hash function
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
        const char = fingerprint.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
    }

    document.getElementById('device_fingerprint').value = Math.abs(hash).toString(16);
}

// Get location for geofencing verification
function getLocationForVoting() {
    const locationStatus = document.getElementById('locationStatus');
    const locationMessage = document.getElementById('locationMessage');

    if (!navigator.geolocation) {
        console.log('Geolocation not supported');
        return;
    }

    locationStatus.style.display = 'block';
    locationMessage.textContent = 'Verifying your location for security...';

    navigator.geolocation.getCurrentPosition(
        function(position) {
            // Success - got location
            document.getElementById('latitude').value = position.coords.latitude;
            document.getElementById('longitude').value = position.coords.longitude;
            document.getElementById('location_accuracy').value = position.coords.accuracy;

            locationStatus.className = 'alert alert-success';
            locationMessage.innerHTML = '<i class="fas fa-check-circle me-2"></i>Location verified successfully';

            // Hide after 3 seconds
            setTimeout(() => {
                locationStatus.style.display = 'none';
            }, 3000);
        },
        function(error) {
            // Error or denied
            console.log('Geolocation error:', error);
            locationStatus.className = 'alert alert-warning';

            switch(error.code) {
                case error.PERMISSION_DENIED:
                    locationMessage.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Location access denied. Voting will use IP-based verification.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    locationMessage.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Location unavailable. Voting will use IP-based verification.';
                    break;
                case error.TIMEOUT:
                    locationMessage.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Location timeout. Voting will use IP-based verification.';
                    break;
                default:
                    locationMessage.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Location error. Voting will use IP-based verification.';
                    break;
            }

            // Hide after 5 seconds
            setTimeout(() => {
                locationStatus.style.display = 'none';
            }, 5000);
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000 // 5 minutes
        }
    );
}
</script>

<style>
.btn-facebook {
    background-color: #3b5998;
    color: white;
}
.btn-facebook:hover {
    background-color: #2d4373;
    color: white;
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
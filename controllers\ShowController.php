<?php
/**
 * Show Controller
 * 
 * This controller handles public show-related functionality.
 */
class ShowController extends Controller {
    private $showModel;
    private $vehicleModel;
    private $judgingModel;
    private $registrationModel;
    private $imageModel;
    private $vehicleScoringModel;
    private $settingsModel;
    private $userModel;
    private $formDesignerModel;
    private $entityTemplateManager;
    private $paymentModel;
    private $emailService;
    private $calendarModel;
    private $showCategoryModel;
    private $auth;
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Ensure APPROOT is defined and available globally
        if (!defined('APPROOT')) {
            define('APPROOT', dirname(dirname(__FILE__)));
        }

        // Load Facebook crawler helper early
        require_once APPROOT . '/helpers/facebook_crawler_helper.php';

        // Make APPROOT available globally
        if (!isset($GLOBALS['APPROOT'])) {
            $GLOBALS['APPROOT'] = APPROOT;
        }

        // Initialize database property
        $this->db = new Database();
        
        $this->showModel = $this->model('ShowModel');
        $this->vehicleModel = $this->model('VehicleModel');
        $this->judgingModel = $this->model('JudgingModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->imageModel = $this->model('ImageEditorModel');
        $this->vehicleScoringModel = $this->model('VehicleScoringModel');
        $this->settingsModel = $this->model('SettingsModel');
        $this->userModel = $this->model('UserModel');
        $this->formDesignerModel = $this->model('FormDesignerModel');
        $this->entityTemplateManager = $this->model('EntityTemplateManager');
        $this->paymentModel = $this->model('PaymentModel');
        $this->emailService = $this->model('EmailService');
        $this->calendarModel = $this->model('CalendarModel');
        $this->showCategoryModel = $this->model('ShowCategoryModel');
        $this->auth = new Auth();
    }
    
    /**
     * Show listing with filtering and pagination
     */
    public function index() {
        // Get filter parameters from GET request
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        $state = isset($_GET['state']) ? trim($_GET['state']) : '';
        $city = isset($_GET['city']) ? trim($_GET['city']) : '';
        $showDate = isset($_GET['show_date']) ? trim($_GET['show_date']) : '';
        $fanVoting = isset($_GET['fan_voting']) ? (int)$_GET['fan_voting'] : -1; // -1 means no filter
        
        // Add default filter to show only upcoming shows (from current date forward)
        // This can be overridden by providing a specific show_date parameter
        $showFromDate = isset($_GET['show_from_date']) ? trim($_GET['show_from_date']) : date('Y-m-d');
        
        // Get pagination parameters
        $perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 20;
        // Validate per_page to only allow 20, 50, or 100
        if (!in_array($perPage, [20, 50, 100])) {
            $perPage = 20; // Default to 20 if invalid
        }
        
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        if ($page < 1) $page = 1;
        
        $offset = ($page - 1) * $perPage;
        
        // Get filtered shows with pagination
        $shows = $this->showModel->getFilteredShows(
            'published', 
            $search, 
            $state, 
            $city, 
            $showDate, 
            $fanVoting, 
            $perPage, 
            $offset,
            $showFromDate  // Add the new parameter for filtering from current date forward
        );
        
        // Get total count for pagination
        $totalShows = $this->showModel->countFilteredShows(
            'published', 
            $search, 
            $state, 
            $city, 
            $showDate, 
            $fanVoting,
            $showFromDate  // Add the new parameter for counting from current date forward
        );
        
        // Calculate total pages
        $totalPages = ceil($totalShows / $perPage);
        
        // Get unique states for filters
        $states = $this->showModel->getUniqueStatesFromShows();
        
        // Get cities filtered by selected state if applicable
        $cities = $this->showModel->getUniqueCitiesFromShows($state);
        
        // Debug mode check
        $debug = defined('DEBUG_MODE') && DEBUG_MODE;
        if ($debug) {
            error_log("ShowController::index - Found " . count($states) . " unique states");
            error_log("ShowController::index - Found " . count($cities) . " unique cities" . 
                      ($state ? " for state: $state" : ""));
        }
        
        $data = [
            'title' => 'Car Shows',
            'shows' => $shows,
            'search' => $search,
            'state' => $state,
            'city' => $city,
            'show_date' => $showDate,
            'show_from_date' => $showFromDate,
            'fan_voting' => $fanVoting,
            'per_page' => $perPage,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_shows' => $totalShows,
            'states' => $states,
            'cities' => $cities
        ];
        
        $this->view('show/index', $data);
    }
    
    /**
     * Show details
     * 
     * @param int|string $id Show ID or view name
     * @param array $data Optional data array
     * @return void
     */
    public function view($id, $data = []) {
        // Ensure APPROOT is defined and available globally
        if (!defined('APPROOT')) {
            define('APPROOT', dirname(dirname(__FILE__)));
        }
        
        // Make APPROOT available globally
        if (!isset($GLOBALS['APPROOT'])) {
            $GLOBALS['APPROOT'] = APPROOT;
        }
        
        // If this is a call to the parent view method (with a view name), pass it through
        if (is_string($id) && strpos($id, '/') !== false) {
            parent::view($id, $data);
            return;
        }
        
        // Otherwise, treat it as a show ID
        // Get show with error handling
        try {
            // Ensure ID is a valid integer
            $id = intval($id);
            if ($id <= 0) {
                // Log the invalid ID
                error_log("[ShowController::showDetails] Parsed ID: " . $id);
                error_log("[ShowController::showDetails] Redirecting: Invalid ID <= 0. ID: " . $id);
                $this->redirect('home/not_found');
                return;
            }
            
            // Handle Facebook crawler requests early
            if (isFacebookCrawler()) {
                // Get show details for Facebook crawler
                $show = $this->showModel->getShowById($id);

                if (!$show || $show->status != 'published') {
                    // Return basic page for non-existent or unpublished shows
                    outputFacebookCrawlerHTML('show', []);
                    exit;
                }

                // Get show images for Facebook crawler
                $showImages = $this->imageModel->getShowImages($id);
                $primaryShowImage = null;
                if (!empty($showImages)) {
                    // Look for primary image first
                    foreach ($showImages as $image) {
                        if (isset($image->is_primary) && $image->is_primary) {
                            $primaryShowImage = $image;
                            break;
                        }
                    }
                    // If no primary image, use the first one
                    if (!$primaryShowImage) {
                        $primaryShowImage = $showImages[0];
                    }
                }

                // Prepare show data for Facebook crawler
                $showData = [
                    'show' => $show,
                    'title' => $show->name,
                    'description' => $show->description,
                    'start_date' => $show->start_date,
                    'location' => $show->location,
                    'primary_image' => $primaryShowImage
                ];

                outputFacebookCrawlerHTML('show', $showData);
                exit;
            }

            // Add detailed logging for debugging
            error_log("[ShowController::showDetails] Parsed ID: " . $id);
            error_log("[ShowController::showDetails] Attempting to fetch show with ID: " . $id);

            // Get show
            $show = $this->showModel->getShowById($id);
            
            // Debug output for troubleshooting
            if (!$show) {
                error_log("[ShowController::showDetails] Show not found with ID: " . $id);
                $this->redirect('home/not_found');
                return;
            }
            
            error_log("[ShowController::showDetails] Show found. Name: " . $show->name . ". Original Status: '" . $show->status . "'. Trimmed Status: '" . trim($show->status) . "'. ID: " . $id);
            
            // Check if show is published
            if (trim($show->status) != 'published') {
                error_log("Show with ID: " . $id . " is not published. Status: " . $show->status);
                
                // Allow admins and coordinators to view unpublished shows
                $auth = new Auth();
                if (!$auth->isLoggedIn() || 
                    (!$auth->hasRole('admin') && 
                     (!$auth->hasRole('coordinator') || $show->coordinator_id != $auth->getCurrentUserId()))) {
                    $this->redirect('home/not_found');
                    return;
                }
            }
            
            // Get categories
            $categories = $this->showModel->getShowCategories($id);
            error_log("ShowController::showDetails - Found " . count($categories) . " categories for show ID: " . $id);
            
            // Get registration counts
            $registrationCounts = $this->registrationModel->countRegistrationsByCategory($id);
            
            // Check if registration is open
            $registrationOpen = $this->showModel->isRegistrationOpen($id);
            error_log("ShowController::showDetails - Registration open: " . ($registrationOpen ? 'Yes' : 'No'));
            
            // Get show images
            $showImages = $this->imageModel->getShowImages($id);
            error_log("ShowController::showDetails - Found " . count($showImages) . " images for show ID: " . $id);

            // Get primary/first image for Facebook sharing
            $primaryShowImage = null;
            if (!empty($showImages)) {
                // Look for primary image first
                foreach ($showImages as $image) {
                    if (isset($image->is_primary) && $image->is_primary) {
                        $primaryShowImage = $image;
                        break;
                    }
                }
                // If no primary image, use the first one
                if (!$primaryShowImage) {
                    $primaryShowImage = $showImages[0];
                }
            }
            
            // Get user's registered vehicles for this show if user is logged in
            $userRegistrations = [];
            $userVehicles = [];
            $userAddress = null;
            if (isset($_SESSION['user_id'])) {
                $userRegistrations = $this->registrationModel->getUserRegistrationsForShow($id, $_SESSION['user_id']);
                error_log("ShowController::showDetails - Found " . count($userRegistrations) . " registrations for user ID: " . $_SESSION['user_id']);

                // Get all user vehicles
                $userVehicles = $this->vehicleModel->getUserVehicles($_SESSION['user_id']);
                error_log("ShowController::showDetails - Found " . count($userVehicles) . " vehicles for user ID: " . $_SESSION['user_id']);

                // Get user address for directions
                $user = $this->userModel->getUserById($_SESSION['user_id']);
                if ($user && !empty($user->address)) {
                    $userAddress = [
                        'address' => $user->address,
                        'city' => $user->city,
                        'state' => $user->state,
                        'zip' => $user->zip
                    ];
                }
            }

            // Get map provider settings
            $mapSettings = $this->calendarModel->getMapProviderSettings();

            // Get venue information if available (check for calendar events linked to this show)
            $venueData = null;
            $events = $this->calendarModel->getEventsByShowId($id);
            if (!empty($events)) {
                $event = $events[0]; // Use the first event
                if (!empty($event->venue_name)) {
                    $venueData = [
                        'name' => $event->venue_name,
                        'address' => $event->venue_address,
                        'city' => $event->venue_city,
                        'state' => $event->venue_state,
                        'zip' => $event->venue_zip,
                        'country' => $event->venue_country
                    ];
                }
            }

            // Determine location for map (venue first, then show location)
            $mapLocation = null;
            if ($venueData) {
                $mapLocation = [
                    'name' => $venueData['name'],
                    'address' => trim(implode(', ', array_filter([
                        $venueData['address'],
                        $venueData['city'],
                        $venueData['state'],
                        $venueData['zip']
                    ]))),
                    'lat' => $show->lat ?? null,
                    'lng' => $show->lng ?? null
                ];
            } else {
                $mapLocation = [
                    'name' => $show->name,
                    'address' => $show->location,
                    'lat' => $show->lat ?? null,
                    'lng' => $show->lng ?? null
                ];
            }
            
            // Prepare Open Graph data for Facebook sharing
            $showTitle = $show->name;
            $showDescription = !empty($show->description) ?
                substr(strip_tags($show->description), 0, 300) :
                'Join us for this exciting car show!';
            $showUrl = BASE_URL . '/show/view/' . $show->id;
            $showDate = date('F j, Y', strtotime($show->start_date));
            $showLocation = !empty($show->location) ? $show->location : '';

            // Create enhanced description with show details
            $enhancedDescription = $showDescription;
            if ($showDate) {
                $enhancedDescription .= "\n\n📅 " . $showDate;
            }
            if ($showLocation) {
                $enhancedDescription .= "\n📍 " . $showLocation;
            }
            if ($registrationOpen) {
                $enhancedDescription .= "\n\n🚗 Registration is open!";
            }
            $enhancedDescription .= "\n\n🔗 View show details and register your vehicle";

            // Set page image - use primary show image if available, otherwise default
            $pageImage = BASE_URL . '/public/images/logo.png'; // Default
            if ($primaryShowImage && !empty($primaryShowImage->file_path)) {
                $pageImage = BASE_URL . '/' . $primaryShowImage->file_path;
            }

            // Create data array for the view
            $viewData = [
                'title' => $show->name,
                'pageTitle' => $showTitle,
                'pageDescription' => $enhancedDescription,
                'pageUrl' => $showUrl,
                'pageType' => 'photo',
                'pageImage' => $pageImage,
                'show' => $show,
                'categories' => $categories,
                'registration_counts' => $registrationCounts,
                'registration_open' => $registrationOpen,
                'show_images' => $showImages,
                'user_registrations' => $userRegistrations,
                'vehicles' => $userVehicles,
                'mapSettings' => $mapSettings,
                'mapLocation' => $mapLocation,
                'userAddress' => $userAddress,
                'venueData' => $venueData
            ];
            
            // Merge with any existing data
            if (!empty($data)) {
                $viewData = array_merge($data, $viewData);
            }
            
            error_log("ShowController::showDetails - Rendering view for show ID: " . $id);
            $this->view('show/view', $viewData);
            
        } catch (Exception $e) {
            // Log the error
            error_log("Error in ShowController::view: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->redirect('home/error/An%20error%20occurred%20while%20loading%20the%20show');
            return;
        }
    }
    
    /**
     * Show vehicles
     * 
     * @param int $id Show ID
     */
    public function vehicles($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show || $show->status != 'published') {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get vehicles
        $vehicles = $this->vehicleModel->getShowVehicles($id);
        
        // Get categories
        $categories = $this->showModel->getShowCategories($id);
        
        $data = [
            'title' => $show->name . ' - Vehicles',
            'show' => $show,
            'vehicles' => $vehicles,
            'categories' => $categories
        ];
        
        $this->view('show/vehicles', $data);
    }
    
    /**
     * Category vehicles
     * 
     * @param int $id Category ID
     */
    public function category($id) {
        // Get category
        $category = $this->showModel->getCategoryById($id);
        
        if (!$category) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($category->show_id);
        
        if (!$show || $show->status != 'published') {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get vehicles
        $vehicles = $this->vehicleModel->getCategoryVehicles($id);
        
        $data = [
            'title' => $show->name . ' - ' . $category->name,
            'show' => $show,
            'category' => $category,
            'vehicles' => $vehicles
        ];
        
        $this->view('show/category', $data);
    }
    
    /**
     * Vehicle details
     * 
     * @param int $id Vehicle ID
     */
    public function vehicle($id) {
        // Get vehicle
        $vehicle = $this->vehicleModel->getVehicleById($id);
        
        if (!$vehicle) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get vehicle images from old system (vehicle_images table)
        $oldImages = $this->vehicleModel->getVehicleImages($id);
        
        // Get images from new system (images table)
        $imageEditorModel = $this->model('ImageEditorModel');
        $newImages = $imageEditorModel->getImagesByEntity('vehicle', $id);
        
        // Combine images from both systems
        $allImages = [];
        
        // Process old images
        foreach ($oldImages as $image) {
            // Add a source identifier
            $image->source = 'old';
            $allImages[] = $image;
        }
        
        // Process new images
        foreach ($newImages as $image) {
            // Add a source identifier
            $image->source = 'new';
            $allImages[] = $image;
        }
        
        // Find primary image
        $primaryImage = null;
        foreach ($allImages as $image) {
            if (isset($image->is_primary) && $image->is_primary) {
                $primaryImage = $image;
                break;
            }
        }
        
        // If no primary image is set, use the first image
        if (!$primaryImage && count($allImages) > 0) {
            $primaryImage = $allImages[0];
            $primaryImage->is_primary = 1;
        }
        
        // Get registrations for this vehicle
        $this->db->query('SELECT r.*, r.created_at as registration_date, s.name as show_name, s.status as show_status, 
                          s.start_date as show_start_date, s.end_date as show_end_date, 
                          sc.name as category_name 
                          FROM registrations r 
                          JOIN shows s ON r.show_id = s.id 
                          JOIN show_categories sc ON r.category_id = sc.id 
                          WHERE r.vehicle_id = :vehicle_id AND s.status = :status AND r.status = :reg_status');
        $this->db->bind(':vehicle_id', $id);
        $this->db->bind(':status', 'published');
        $this->db->bind(':reg_status', 'approved');
        $registrations = $this->db->resultSet();
        
        $data = [
            'title' => $vehicle->year . ' ' . $vehicle->make . ' ' . $vehicle->model,
            'vehicle' => $vehicle,
            'images' => $allImages,
            'primary_image' => $primaryImage,
            'registrations' => $registrations
        ];
        
        $this->view('show/vehicle', $data);
    }
    
    /**
     * Create or add a show (shared between admin and coordinator)
     * 
     * @return void
     */
    public function create() {
        // Determine if user is admin or coordinator
        $isAdmin = $this->auth->hasRole('admin');
        $userId = $this->auth->getCurrentUserId();
        
        // Check if user is logged in and has appropriate role
        if (!$this->auth->isLoggedIn() || (!$isAdmin && !$this->auth->hasRole('coordinator'))) {
            $this->redirect('users/login');
            return;
        }
        
        // Get coordinators (for admin only)
        $coordinators = $isAdmin ? $this->userModel->getUsers('coordinator') : [];
        
        // Get listing fee from settings
        $listingFee = $this->settingsModel->getSetting('default_listing_fee', 0);
        $listingFeeType = $this->settingsModel->getSetting('listing_fee_type', 'per_show');
        
        // First check if there's a default template set in DefaultTemplateManager
        $templateId = $this->entityTemplateManager->getBestTemplateId('show', 0);
        
        // If a template was found, use it
        if ($templateId) {
            $template = $this->formDesignerModel->getFormTemplateById($templateId);
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ShowController::create - Using template ID {$templateId} from DefaultTemplateManager");
            }
        } else {
            // Fall back to the default admin form template
            $template = $this->formDesignerModel->getFormTemplateByTypeAndEntity('admin_show', 0);
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ShowController::create - Using default admin_show template");
            }
        }
        
        // If no template exists, create a basic one
        if (!$template) {
            // Log the issue
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Default show form template not found. Creating a basic template.');
            }
            
            // Create a basic template with essential fields
            $basicFields = [
                (object)[
                    'id' => 'name',
                    'type' => 'text',
                    'label' => 'Show Name',
                    'required' => true,
                    'row' => 0,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'location',
                    'type' => 'text',
                    'label' => 'Location',
                    'required' => true,
                    'row' => 0,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'description',
                    'type' => 'textarea',
                    'label' => 'Description',
                    'required' => false,
                    'row' => 1,
                    'width' => 'col-md-12'
                ],
                (object)[
                    'id' => 'start_date',
                    'type' => 'date',
                    'label' => 'Start Date',
                    'required' => true,
                    'row' => 2,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'end_date',
                    'type' => 'date',
                    'label' => 'End Date',
                    'required' => true,
                    'row' => 2,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'registration_start',
                    'type' => 'date',
                    'label' => 'Registration Start',
                    'required' => true,
                    'row' => 3,
                    'width' => 'col-md-6'
                ],
                (object)[
                    'id' => 'registration_end',
                    'type' => 'date',
                    'label' => 'Registration End',
                    'required' => true,
                    'row' => 3,
                    'width' => 'col-md-6'
                ]
            ];
            
            // Add coordinator field for admin only
            if ($isAdmin) {
                $basicFields[] = (object)[
                    'id' => 'coordinator_id',
                    'type' => 'select',
                    'label' => 'Coordinator',
                    'required' => true,
                    'row' => 4,
                    'width' => 'col-md-6'
                ];
            }
            
            // Add status field
            $basicFields[] = (object)[
                'id' => 'status',
                'type' => 'select',
                'label' => 'Status',
                'required' => true,
                'row' => 4,
                'width' => 'col-md-6',
                'options' => [
                    (object)['value' => 'draft', 'label' => 'Draft'],
                    (object)['value' => 'published', 'label' => 'Published'],
                    (object)['value' => 'cancelled', 'label' => 'Cancelled'],
                    (object)['value' => 'completed', 'label' => 'Completed']
                ]
            ];
            
            // Add listing paid field for admin only
            if ($isAdmin) {
                $basicFields[] = (object)[
                    'id' => 'listing_paid',
                    'type' => 'checkbox',
                    'label' => 'Listing Fee Paid',
                    'required' => false,
                    'row' => 5,
                    'width' => 'col-md-6'
                ];
            }
            
            // Add featured image field
            $basicFields[] = (object)[
                'id' => 'featured_image',
                'type' => 'image',
                'label' => 'Featured Image',
                'required' => false,
                'row' => 6,
                'width' => 'col-md-6',
                'placeholder' => 'Select a featured image for this show'
            ];
            
            // Create a temporary template object
            $template = (object)[
                'id' => 0,
                'name' => 'Default Show Form (Temporary)',
                'type' => 'admin_show',
                'entity_id' => 0,
                'fields' => json_encode($basicFields)
            ];
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data - start with standard fields
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'location' => trim($_POST['location']),
                'start_date' => convertUserDateTimeToUTC(trim($_POST['start_date']), $this->auth->getCurrentUserId()),
                'end_date' => convertUserDateTimeToUTC(trim($_POST['end_date']), $this->auth->getCurrentUserId()),
                'registration_start' => convertUserDateTimeToUTC(trim($_POST['registration_start']), $this->auth->getCurrentUserId()),
                'registration_end' => convertUserDateTimeToUTC(trim($_POST['registration_end']), $this->auth->getCurrentUserId()),
                'status' => trim($_POST['status']),
                'fan_voting_enabled' => isset($_POST['fan_voting_enabled']) ? (bool)$_POST['fan_voting_enabled'] : true,
                'is_free' => isset($_POST['is_free']) ? (bool)$_POST['is_free'] : false,
                // If is_free is checked, always set registration_fee to 0
                'registration_fee' => (isset($_POST['is_free']) && $_POST['is_free']) ? 0.00 : (isset($_POST['registration_fee']) ? $_POST['registration_fee'] : 0.00),
                'listing_fee' => $listingFee,
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_start_err' => '',
                'registration_end_err' => '',
                'title' => $isAdmin ? 'Add Show' : 'Create Show',
                'coordinators' => $coordinators,
                'template' => $template,
                'listing_fee' => $listingFee,
                'listing_fee_type' => $listingFeeType
            ];
            
            // Set coordinator_id based on role
            if ($isAdmin) {
                $data['coordinator_id'] = trim($_POST['coordinator_id']);
                $data['coordinator_id_err'] = '';
                $data['listing_paid'] = isset($_POST['listing_paid']) ? (bool)$_POST['listing_paid'] : false;
                $data['featured_image_id'] = isset($_POST['featured_image_id']) ? $_POST['featured_image_id'] : null;
                $data['featured_image'] = isset($_POST['featured_image_text']) ? $_POST['featured_image_text'] : '';
            } else {
                $data['coordinator_id'] = $userId; // Always assign to current coordinator
                $data['listing_paid'] = false; // Not paid yet for coordinators
            }
            
            // Get custom fields from the template
            $templateFields = json_decode($template->fields, true);
            if (is_array($templateFields)) {
                foreach ($templateFields as $field) {
                    // Skip if field doesn't have an ID or is a system field
                    if (!isset($field['id']) || in_array($field['id'], [
                        'id', 'created_at', 'updated_at', 'name', 'description', 'location', 
                        'start_date', 'end_date', 'registration_start', 'registration_end', 
                        'coordinator_id', 'status', 'fan_voting_enabled', 'registration_fee', 
                        'is_free', 'listing_fee', 'listing_paid', 'featured_image_id'
                    ])) {
                        continue;
                    }
                    
                    // Skip section dividers and HTML fields (they don't need database columns)
                    if (isset($field['type']) && in_array($field['type'], ['section', 'html'])) {
                        continue;
                    }
                    
                    // Add the field to the data array
                    $fieldId = $field['id'];
                    if (isset($_POST[$fieldId])) {
                        // Handle different field types
                        switch ($field['type']) {
                            case 'checkbox':
                                $data[$fieldId] = isset($_POST[$fieldId]) ? true : false;
                                break;
                            case 'number':
                                $data[$fieldId] = is_numeric($_POST[$fieldId]) ? $_POST[$fieldId] : 0;
                                break;
                            default:
                                $data[$fieldId] = trim($_POST[$fieldId]);
                        }
                    } else {
                        // Set default value if field is not in POST data
                        $data[$fieldId] = isset($field['default']) ? $field['default'] : null;
                    }
                }
            }
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate location
            if (empty($data['location'])) {
                $data['location_err'] = 'Please enter a location';
            }
            
            // Validate dates
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif ($data['end_date'] < $data['start_date']) {
                $data['end_date_err'] = 'End date must be after start date';
            }
            
            if (empty($data['registration_start'])) {
                $data['registration_start_err'] = 'Please enter a registration start date';
            }
            
            if (empty($data['registration_end'])) {
                $data['registration_end_err'] = 'Please enter a registration end date';
            } elseif ($data['registration_end'] < $data['registration_start']) {
                $data['registration_end_err'] = 'Registration end date must be after registration start date';
            } elseif ($data['registration_end'] > $data['start_date']) {
                $data['registration_end_err'] = 'Registration must end before the show starts';
            }
            
            // Validate coordinator if admin
            if ($isAdmin && empty($data['coordinator_id'])) {
                $data['coordinator_id_err'] = 'Please select a coordinator';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select a status';
            }
            
            // Check for errors
            $hasErrors = !empty($data['name_err']) || !empty($data['location_err']) || 
                        !empty($data['start_date_err']) || !empty($data['end_date_err']) || 
                        !empty($data['registration_start_err']) || !empty($data['registration_end_err']) || 
                        ($isAdmin && !empty($data['coordinator_id_err'])) || !empty($data['status_err']);
            
            if (!$hasErrors) {
                // Check if coordinator is pre-approved or exempt from listing fees
                $isPreApproved = false;
                $isExempt = false;
                
                if (!$isAdmin) {
                    // Check if coordinator is exempt from listing fees
                    $isExempt = $this->paymentModel->isUserExemptFromListingFees($userId);
                    
                    // Check if coordinator is pre-approved
                    $this->db->query('SELECT * FROM pre_approved_cords 
                                     WHERE coordinator_id = :coordinator_id 
                                     AND begin_date <= CURDATE() 
                                     AND end_date >= CURDATE()');
                    $this->db->bind(':coordinator_id', $userId);
                    $preApproved = $this->db->single();
                    $isPreApproved = !empty($preApproved);
                    
                    // If coordinator is exempt or pre-approved, mark listing as paid
                    if ($isExempt || $isPreApproved) {
                        $data['listing_paid'] = true;
                    } else {
                        // For coordinators who need to pay, set status to payment_pending
                        // unless admin has explicitly set a different status
                        $data['status'] = 'payment_pending';
                    }
                }
                
                // Create show
                $showId = $this->showModel->createShow($data);
                
                if ($showId) {
                    // If admin created the show
                    if ($isAdmin) {
                        $this->setFlashMessage('admin_message', 'Show created successfully', 'success');
                        $this->redirect('admin/shows');
                    } 
                    // If coordinator created the show
                    else {
                        // Get coordinator info for email notification
                        $coordinator = $this->userModel->getUserById($userId);
                        $show = $this->showModel->getShowById($showId);
                        
                        // Send email notification to admins
                        $this->emailService->sendNewShowNotification($show, $coordinator);
                        
                        // Set success message
                        $this->setFlashMessage('coordinator_message', 'Show created successfully', 'success');
                        
                        // If exempt or pre-approved, redirect to show page
                        if ($isExempt || $isPreApproved) {
                            if ($isExempt) {
                                $this->setFlashMessage('coordinator_message', 'Show created successfully. Listing fee waived based on your account privileges.', 'success');
                            } else {
                                $this->setFlashMessage('coordinator_message', 'Show created successfully. Listing fee waived based on your pre-approved status.', 'success');
                            }
                            $this->redirect('coordinator/show/' . $showId);
                        } 
                        // Otherwise, redirect to payment page
                        else if ($listingFee > 0) {
                            $this->redirect('payment/showListing/' . $showId);
                        } else {
                            // If no listing fee, redirect to show page
                            $this->redirect('coordinator/show/' . $showId);
                        }
                    }
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                if ($isAdmin) {
                    $this->view('admin/shows/add_with_template', $data);
                } else {
                    $this->view('coordinator/shows/create_with_template', $data);
                }
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'description' => '',
                'location' => '',
                'start_date' => '',
                'end_date' => '',
                'registration_start' => '',
                'registration_end' => '',
                'status' => 'draft',
                'fan_voting_enabled' => true,
                'registration_fee' => 0.00,
                'is_free' => false,
                'listing_fee' => $listingFee,
                'listing_fee_type' => $listingFeeType,
                'featured_image_id' => '',
                'featured_image' => '',
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_start_err' => '',
                'registration_end_err' => '',
                'title' => $isAdmin ? 'Add Show' : 'Create Show',
                'template' => $template,
                'data' => [] // This will hold all field values for the template
            ];
            
            // Add coordinator-specific fields
            if ($isAdmin) {
                $data['coordinator_id'] = '';
                $data['coordinator_id_err'] = '';
                $data['coordinators'] = $coordinators;
                $data['listing_paid'] = false;
            } else {
                $data['coordinator_id'] = $userId;
            }
            
            // Extract template fields to pre-populate with default values
            if ($template && isset($template->fields)) {
                $fields = json_decode($template->fields);
                if ($fields) {
                    foreach ($fields as $field) {
                        if (isset($field->id) && isset($field->default)) {
                            $data['data'][$field->id] = $field->default;
                        } else if (isset($field->id)) {
                            $data['data'][$field->id] = '';
                        }
                    }
                }
            }
            
            // Load view with template
            if ($isAdmin) {
                $this->view('admin/shows/add_with_template', $data);
            } else {
                $this->view('coordinator/shows/create_with_template', $data);
            }
        }
    }

    /**
     * AJAX endpoint for searching vehicles in voting interface - optimized for large datasets
     */
    public function searchVotingVehicles($showId) {
        // Verify show exists and voting is enabled
        $show = $this->showModel->getShowById($showId);
        if (!$show || $show->status != 'published' || !$show->fan_voting_enabled) {
            http_response_code(403);
            echo json_encode(['error' => 'Voting not available']);
            return;
        }

        $query = isset($_GET['q']) ? trim($_GET['q']) : '';
        $categoryId = isset($_GET['category']) ? intval($_GET['category']) : null;
        $limit = 20; // Limit results for performance

        try {
            // Get vehicles with search and category filters
            $vehiclesData = $this->vehicleModel->getPaginatedShowVehiclesWithImages($showId, 1, $limit, $query, $categoryId);

            // Format response for frontend
            $vehicles = [];
            foreach ($vehiclesData['vehicles'] as $vehicle) {
                $primaryImage = null;
                if (!empty($vehicle->images)) {
                    foreach ($vehicle->images as $image) {
                        if ($image->is_primary) {
                            $primaryImage = $image;
                            break;
                        }
                    }
                    if (!$primaryImage) {
                        $primaryImage = $vehicle->images[0];
                    }
                }

                $vehicles[] = [
                    'registration_id' => $vehicle->registration_id,
                    'vehicle_info' => $vehicle->year . ' ' . $vehicle->make . ' ' . $vehicle->model,
                    'registration_number' => $vehicle->registration_number,
                    'category_name' => $vehicle->category_name,
                    'vote_count' => $vehicle->vote_count ?? 0,
                    'image' => $primaryImage ? [
                        'filename' => $primaryImage->filename,
                        'alt_text' => $primaryImage->alt_text
                    ] : null
                ];
            }

            header('Content-Type: application/json');
            echo json_encode([
                'vehicles' => $vehicles,
                'total' => $vehiclesData['total']
            ]);

        } catch (Exception $e) {
            error_log('Error searching voting vehicles: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Search failed']);
        }
    }

    /**
     * Thank you page after voting
     * 
     * @param int $id Show ID
     * @return void
     */
    public function vote_thanks($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get Facebook App ID from settings
        $fbAppId = $this->model('SettingsModel')->getSetting('facebook_app_id', '');
        
        // Init data
        $data = [
            'title' => 'Thank You for Voting - ' . $show->name,
            'show' => $show,
            'share_enabled' => true,
            'fb_app_id' => $fbAppId
        ];
        
        // Load view
        $this->view('show/vote_thanks', $data);
    }
    
    /**
     * Print judge QR codes for a show
     * 
     * @param int $id Show ID
     * @return void
     */
    public function print_judge_qr($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if QR codes are enabled
        $settingsModel = $this->model('SettingsModel');
        $qrEnabled = $settingsModel->getSetting('qr_code_enabled', '0');
        
        if ($qrEnabled != '1') {
            $this->redirect('home/error/QR%20codes%20are%20not%20enabled');
            return;
        }
        
        // Get categories for this show
        $categories = $this->showModel->getShowCategories($id);
        
        // Init data
        $data = [
            'title' => 'Judge QR Codes - ' . $show->name,
            'show' => $show,
            'categories' => $categories
        ];
        
        // Load view
        $this->view('shared/print_judge_qr_codes', $data);
    }
    
    /**
     * Fan voting
     * 
     * @param int $id Show ID
     * @param int $registrationId Optional registration ID for direct voting
     */
    public function vote($id, $registrationId = null) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show || $show->status != 'published') {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if fan voting is enabled for this show
        if (!$show->fan_voting_enabled) {
            $this->redirect('home/error/Fan%20voting%20is%20not%20enabled%20for%20this%20show');
            return;
        }
        
        // Check if voting is allowed (all comparisons in UTC)
        $now = new DateTime('now', new DateTimeZone('UTC'));
        $startDate = new DateTime($show->start_date, new DateTimeZone('UTC'));
        $endDate = new DateTime($show->end_date, new DateTimeZone('UTC'));
        $endDate->setTime(23, 59, 59); // End of the day
        
        if ($now < $startDate || $now > $endDate) {
            $this->redirect('home/error/Voting%20is%20not%20currently%20open%20for%20this%20show');
            return;
        }
        
        // Get Facebook App ID from settings
        $fbAppId = $this->model('SettingsModel')->getSetting('facebook_app_id', '');
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $registrationId = intval($_POST['registration_id']);
            $voteType = isset($_POST['vote_type']) ? $_POST['vote_type'] : 'ip';
            
            // Validate registration
            if (empty($registrationId)) {
                $this->redirect('home/error/Please%20select%20a%20vehicle');
                return;
            }
            
            // Get voter IP
            $voterIp = $_SERVER['REMOTE_ADDR'];

            // Collect security data
            $securityData = $this->collectSecurityData($id);

            // Check for honeypot interaction
            if (isset($_POST['honeypot_field']) && !empty($_POST['honeypot_field'])) {
                // Bot detected - record honeypot interaction
                require_once APPROOT . '/models/VotingSecurityModel.php';
                $securityModel = new VotingSecurityModel();
                $securityModel->recordHoneypotInteraction($id, 'vote_form', $voterIp,
                    $securityData['device_fingerprint'] ?? null,
                    $securityData['user_agent'] ?? null,
                    session_id());

                // Redirect to error page
                $this->redirect('home/error/Invalid%20submission%20detected');
                return;
            }

            // Handle different vote types
            if ($voteType === 'facebook') {
                // Facebook login voting
                $fbUserId = isset($_POST['fb_user_id']) ? $_POST['fb_user_id'] : '';
                $fbUserName = isset($_POST['fb_user_name']) ? $_POST['fb_user_name'] : '';
                $fbUserEmail = isset($_POST['fb_user_email']) ? $_POST['fb_user_email'] : '';
                
                if (empty($fbUserId)) {
                    $this->redirect('home/error/Facebook%20authentication%20failed');
                    return;
                }
                
                // Record vote with Facebook info and security data
                $voteResult = $this->judgingModel->recordFanVoteFacebook($id, $registrationId, $voterIp, $fbUserId, $fbUserName, $fbUserEmail, $securityData);

                if ($voteResult) {
                    // Check if vote was blocked due to high risk
                    $lastVoteId = $this->db->lastInsertId();
                    if ($lastVoteId) {
                        $this->db->query('SELECT is_approved, is_flagged, risk_score FROM fan_votes WHERE id = :vote_id');
                        $this->db->bind(':vote_id', $lastVoteId);
                        $voteStatus = $this->db->single();

                        if ($voteStatus && !$voteStatus->is_approved && $voteStatus->risk_score >= 90) {
                            // Vote was blocked - redirect to appeal form
                            flash('vote_message', 'Your Facebook vote was blocked by our security system. You can appeal this decision below.', 'alert alert-warning');
                            $this->redirect('show/voteAppeal/' . $id . '/' . $registrationId);
                            return;
                        }
                    }

                    // Redirect to thank you page
                    $this->redirect('show/vote_thanks/' . $id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // IP-based voting
                // Check if this IP has already voted
                if ($this->judgingModel->hasIpVoted($id, $voterIp)) {
                    $this->redirect('home/error/You%20have%20already%20voted%20for%20this%20show');
                    return;
                }
                
                // Record vote with security data
                $voteResult = $this->judgingModel->recordFanVote($id, $registrationId, $voterIp, $securityData);

                if ($voteResult) {
                    // Check if vote was blocked due to high risk
                    $lastVoteId = $this->db->lastInsertId();
                    if ($lastVoteId) {
                        $this->db->query('SELECT is_approved, is_flagged, risk_score FROM fan_votes WHERE id = :vote_id');
                        $this->db->bind(':vote_id', $lastVoteId);
                        $voteStatus = $this->db->single();

                        if ($voteStatus && !$voteStatus->is_approved && $voteStatus->risk_score >= 90) {
                            // Vote was blocked - redirect to appeal form
                            flash('vote_message', 'Your vote was blocked by our security system. You can appeal this decision below.', 'alert alert-warning');
                            $this->redirect('show/voteAppeal/' . $id . '/' . $registrationId);
                            return;
                        }
                    }

                    // Redirect to thank you page
                    $this->redirect('show/vote_thanks/' . $id);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            }
        } else {
            // Direct voting for a specific vehicle
            if ($registrationId) {
                // Get registration details
                $registration = $this->registrationModel->getRegistrationById($registrationId);
                
                if (!$registration || $registration->show_id != $id || $registration->status != 'approved') {
                    $this->redirect('home/not_found');
                    return;
                }
                
                // Get vehicle details
                $vehicle = $this->vehicleModel->getVehicleById($registration->vehicle_id);
                
                if (!$vehicle) {
                    $this->redirect('home/not_found');
                    return;
                }
                
                // Get category
                $category = $this->showModel->getCategoryById($registration->category_id);
                
                // Get primary vehicle image
                $vehicleImage = null;
                $images = $this->imageModel->getImagesByEntity('vehicle', $vehicle->id);
                foreach ($images as $image) {
                    if ($image->is_primary) {
                        $vehicleImage = $image;
                        break;
                    }
                }
                
                // If no primary image was found, use the first image
                if(!$vehicleImage && !empty($images)) {
                    $vehicleImage = $images[0];
                }
                
                // Init data for direct voting
                $data = [
                    'title' => $show->name . ' - Fan Favorite Voting',
                    'show' => $show,
                    'registration' => $registration,
                    'vehicle' => $vehicle,
                    'category' => $category,
                    'vehicle_image' => $vehicleImage,
                    'fb_app_id' => $fbAppId
                ];
                
                // Load view for direct vehicle voting
                $this->view('show/vote_vehicle', $data);
            } else {
                // Get pagination parameters
                $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
                $limit = isset($_GET['limit']) ? min(50, max(6, intval($_GET['limit']))) : 12;
                $search = isset($_GET['search']) ? trim($_GET['search']) : '';
                $categoryId = isset($_GET['category']) ? intval($_GET['category']) : null;

                // Get paginated vehicles for voting
                $vehiclesData = $this->vehicleModel->getPaginatedShowVehiclesWithImages($id, $page, $limit, $search, $categoryId);
                $categories = $this->showModel->getShowCategories($id);

                // Get vehicle count for display
                $totalVehicles = $this->vehicleModel->getShowVehicleCount($id);

                // Init data for vehicle selection
                $data = [
                    'title' => $show->name . ' - Fan Favorite Voting',
                    'show' => $show,
                    'vehicles' => $vehiclesData['vehicles'],
                    'total_vehicles' => $vehiclesData['total'],
                    'current_page' => $page,
                    'total_pages' => ceil($vehiclesData['total'] / $limit),
                    'limit' => $limit,
                    'categories' => $categories,
                    'search' => $search,
                    'selected_category' => $categoryId,
                    'fb_app_id' => $fbAppId
                ];

                // Load view for vehicle selection
                $this->view('show/vote', $data);
            }
        }
    }
    
    /**
     * Vote thanks page
     * 
     * @param int $id Show ID
     */
    public function voteThanks($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show || $show->status != 'published') {
            $this->redirect('home/not_found');
            return;
        }
        
        $data = [
            'title' => 'Thank You for Voting',
            'show' => $show
        ];
        
        $this->view('show/vote_thanks', $data);
    }
    
    /**
     * Ensure the category_winners table exists
     * This method creates the table if it doesn't exist
     */
    private function ensureCategoryWinnersTableExists() {
        try {
            // Check if category_winners table exists
            $this->db->query("SHOW TABLES LIKE 'category_winners'");
            $categoryWinnersExists = $this->db->rowCount() > 0;
            
            if (!$categoryWinnersExists) {
                // Create the category_winners table
                $sql = "CREATE TABLE IF NOT EXISTS `category_winners` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `show_id` int(11) NOT NULL,
                    `category_id` int(11) NOT NULL,
                    `registration_id` int(11) NOT NULL,
                    `vehicle_id` int(11) NOT NULL,
                    `user_id` int(11) NOT NULL,
                    `place` int(11) NOT NULL,
                    `score` decimal(10,2) NOT NULL,
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `unique_winner` (`show_id`,`category_id`,`place`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
                
                $this->db->query($sql);
                $this->db->execute();
                
                // Add indexes to improve performance
                $indexQueries = [
                    "ALTER TABLE category_winners ADD INDEX idx_show_id (show_id)",
                    "ALTER TABLE category_winners ADD INDEX idx_category_id (category_id)",
                    "ALTER TABLE category_winners ADD INDEX idx_vehicle_id (vehicle_id)",
                    "ALTER TABLE category_winners ADD INDEX idx_registration_id (registration_id)"
                ];
                
                foreach ($indexQueries as $indexQuery) {
                    try {
                        $this->db->query($indexQuery);
                        $this->db->execute();
                    } catch (Exception $e) {
                        // Index might already exist, which is fine
                        error_log('Note: ' . $e->getMessage());
                    }
                }
            }
        } catch (Exception $e) {
            error_log('Error ensuring category_winners table exists: ' . $e->getMessage());
        }
    }
    
    /**
     * Show results
     * 
     * @param int $id Show ID
     */
    public function results($id) {
        try {
            // Get show
            $show = $this->showModel->getShowById($id);
            
            if (!$show) {
                $this->redirect('home/not_found');
                return;
            }
            
            // Only allow viewing results for completed shows
            if ($show->status != 'completed') {
                $this->redirect('home/error/Results%20are%20not%20available%20yet');
                return;
            }
            
            // Ensure the category_winners table exists
            $this->ensureCategoryWinnersTableExists();
            
            // Get results
            $results = $this->judgingModel->getShowResults($id);
            
            // Get fan vote rankings
            $fanVotes = $this->judgingModel->getFanVoteRankings($id, 10);
        } catch (Exception $e) {
            error_log('Error in results method: ' . $e->getMessage());
            $this->redirect('home/error/An%20error%20occurred%20while%20loading%20results');
            return;
        }
        
        // Get categories for this show
        $categories = $this->showModel->getShowCategories($id);
        
        // Get winners for each category
        $categoryWinners = [];
        
        foreach ($categories as $category) {
            // Check if we already have winners stored in the database
            $this->db->query("SELECT * FROM category_winners 
                             WHERE show_id = :show_id AND category_id = :category_id 
                             ORDER BY place ASC");
            $this->db->bind(':show_id', $id);
            $this->db->bind(':category_id', $category->id);
            $winners = $this->db->resultSet();
            
            // If we don't have winners stored, calculate them
            if (empty($winners)) {
                // Get all registrations for this category
                $this->db->query("SELECT r.*, v.year, v.make, v.model, v.owner_id, u.name as owner_name 
                                 FROM registrations r 
                                 JOIN vehicles v ON r.vehicle_id = v.id 
                                 JOIN users u ON v.owner_id = u.id 
                                 WHERE r.show_id = :show_id AND r.category_id = :category_id 
                                 AND r.status = 'approved'");
                $this->db->bind(':show_id', $id);
                $this->db->bind(':category_id', $category->id);
                $registrations = $this->db->resultSet();
                
                // Calculate scores for each registration
                $scoredRegistrations = [];
                foreach ($registrations as $registration) {
                    // Get the total score
                    $score = $this->vehicleScoringModel->getVehicleTotalScore($id, $registration->vehicle_id);
                    
                    if ($score !== false) {
                        $registration->total_score = $score;
                        $scoredRegistrations[] = $registration;
                    }
                }
                
                // Sort by score (highest first)
                usort($scoredRegistrations, function($a, $b) {
                    return $b->total_score <=> $a->total_score;
                });
                
                // Take top 3 (or fewer if not enough)
                $topThree = array_slice($scoredRegistrations, 0, 3);
                
                // Store winners in the database for future reference
                foreach ($topThree as $index => $winner) {
                    $place = $index + 1; // 1st, 2nd, 3rd
                    
                    // Check if we already have a record for this place
                    $this->db->query("SELECT id FROM category_winners 
                                     WHERE show_id = :show_id AND category_id = :category_id 
                                     AND place = :place");
                    $this->db->bind(':show_id', $id);
                    $this->db->bind(':category_id', $category->id);
                    $this->db->bind(':place', $place);
                    $existingWinner = $this->db->single();
                    
                    if (!$existingWinner) {
                        // Insert new winner
                        $this->db->query("INSERT INTO category_winners 
                                         (show_id, category_id, registration_id, vehicle_id, user_id, place, score) 
                                         VALUES (:show_id, :category_id, :registration_id, :vehicle_id, :user_id, :place, :score)");
                        $this->db->bind(':show_id', $id);
                        $this->db->bind(':category_id', $category->id);
                        $this->db->bind(':registration_id', $winner->id);
                        $this->db->bind(':vehicle_id', $winner->vehicle_id);
                        $this->db->bind(':user_id', $winner->owner_id);
                        $this->db->bind(':place', $place);
                        $this->db->bind(':score', $winner->total_score);
                        $this->db->execute();
                    }
                }
                
                // Get the winners we just stored
                $this->db->query("SELECT cw.*, v.year, v.make, v.model, r.display_number, u.name as owner_name 
                                 FROM category_winners cw 
                                 JOIN vehicles v ON cw.vehicle_id = v.id 
                                 JOIN users u ON cw.user_id = u.id 
                                 JOIN registrations r ON cw.registration_id = r.id 
                                 WHERE cw.show_id = :show_id AND cw.category_id = :category_id 
                                 ORDER BY cw.place ASC");
                $this->db->bind(':show_id', $id);
                $this->db->bind(':category_id', $category->id);
                $winners = $this->db->resultSet();
            } else {
                // Enhance the winners with additional information
                foreach ($winners as &$winner) {
                    // Get vehicle details
                    $this->db->query("SELECT v.year, v.make, v.model, r.display_number, u.name as owner_name 
                                     FROM vehicles v 
                                     JOIN users u ON v.owner_id = u.id 
                                     JOIN registrations r ON r.vehicle_id = v.id AND r.id = :registration_id");
                    $this->db->bind(':registration_id', $winner->registration_id);
                    $vehicleInfo = $this->db->single();
                    
                    if ($vehicleInfo) {
                        $winner->year = $vehicleInfo->year;
                        $winner->make = $vehicleInfo->make;
                        $winner->model = $vehicleInfo->model;
                        $winner->display_number = $vehicleInfo->display_number;
                        $winner->owner_name = $vehicleInfo->owner_name;
                    }
                }
            }
            
            $categoryWinners[$category->id] = [
                'category' => $category,
                'winners' => $winners
            ];
        }
        
        $data = [
            'title' => $show->name . ' - Results',
            'show' => $show,
            'results' => $results,
            'fan_votes' => $fanVotes,
            'categories' => $categories,
            'category_winners' => $categoryWinners
        ];
        
        $this->view('show/results', $data);
    }

    /**
     * Debug method to see what meta tags are being generated for shows
     *
     * @param int $id Show ID
     */
    public function debug($id) {
        if (!$id) {
            $this->redirect('home');
            return;
        }

        // Get show
        $show = $this->showModel->getShowById($id);

        if (!$show) {
            echo "Show not found with ID: " . $id;
            return;
        }

        // Prepare Open Graph data for Facebook sharing (same logic as view method)
        $showTitle = $show->name;
        $showDescription = !empty($show->description) ?
            substr(strip_tags($show->description), 0, 300) :
            'Join us for this exciting car show!';
        $showUrl = BASE_URL . '/show/view/' . $show->id;
        $showDate = date('F j, Y', strtotime($show->start_date));
        $showLocation = !empty($show->location) ? $show->location : '';

        // Create enhanced description with show details
        $enhancedDescription = $showDescription;
        if ($showDate) {
            $enhancedDescription .= "\n\n📅 " . $showDate;
        }
        if ($showLocation) {
            $enhancedDescription .= "\n📍 " . $showLocation;
        }
        $enhancedDescription .= "\n\n🔗 View show details and register your vehicle";

        $ogData = [
            'title' => $showTitle,
            'description' => $enhancedDescription,
            'image' => BASE_URL . '/public/images/logo.png',
            'url' => $showUrl,
            'type' => 'photo'
        ];

        $data = [
            'show' => $show,
            'og_data' => $ogData
        ];

        $this->view('show/debug', $data);
    }
    
    /**
     * Get and display a vehicle's score
     * 
     * @param int $showId Show ID
     * @param int $vehicleId Vehicle ID
     * @return void
     */
    public function vehicleScore($showId, $vehicleId) {
        // Check if show exists
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if show is completed
        if ($show->status !== 'completed') {
            $this->setFlashMessage('error', 'Vehicle scores are only available for completed shows.', 'alert alert-danger');
            $this->redirect('user/registrations');
            return;
        }
        
        // Check if vehicle exists
        $vehicle = $this->vehicleModel->getVehicleById($vehicleId);
        
        if (!$vehicle) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get registration
        $registration = $this->registrationModel->getRegistrationByVehicleAndShow($vehicleId, $showId);
        
        if (!$registration) {
            $this->setFlashMessage('error', 'This vehicle is not registered for this show.', 'alert alert-danger');
            $this->redirect('show/view/' . $showId);
            return;
        }
        
        // Get the vehicle's total score using the class property
        $totalScore = $this->vehicleScoringModel->getVehicleTotalScore($showId, $vehicleId);
        
        // Get vehicle images using the class property
        $images = $this->imageModel->getImagesByEntity('vehicle', $vehicleId);
        
        // Find primary image
        $primaryImage = null;
        foreach ($images as $image) {
            if (isset($image->is_primary) && $image->is_primary) {
                $primaryImage = $image;
                break;
            }
        }
        
        // If no primary image is set, use the first image
        if (!$primaryImage && count($images) > 0) {
            $primaryImage = $images[0];
        }
        
        // Get judging metrics for this show
        $this->db->query("SELECT id, name, max_score, weight FROM judging_metrics 
                         WHERE show_id = :show_id ORDER BY display_order");
        $this->db->bind(':show_id', $showId);
        $metrics = $this->db->resultSet();
        
        // Get scores for this registration with judge information
        $this->db->query("SELECT s.*, m.name as metric_name, m.description as metric_description, 
                         m.max_score, m.weight, j.name as judge_name, j.id as judge_id
                         FROM scores s
                         JOIN judging_metrics m ON s.metric_id = m.id
                         JOIN users j ON s.judge_id = j.id
                         WHERE s.registration_id = :registration_id
                         AND s.is_draft = 0
                         ORDER BY j.name, m.display_order");
        $this->db->bind(':registration_id', $registration->id);
        $scores = $this->db->resultSet();
        
        // Get age weight for this vehicle
        $this->db->query("SELECT multiplier FROM age_weights 
                         WHERE show_id = :show_id 
                         AND :vehicle_year BETWEEN min_age AND max_age");
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':vehicle_year', $registration->year);
        $ageWeightResult = $this->db->single();
        
        $ageWeight = $ageWeightResult ? $ageWeightResult->multiplier : 1.0;
        
        // Get scoring settings and formula
        $this->db->query("SELECT ss.*, sf.name as formula_name, sf.formula, sf.id as formula_id
                         FROM show_scoring_settings ss
                         LEFT JOIN scoring_formulas sf ON ss.formula_id = sf.id
                         WHERE ss.show_id = :show_id");
        $this->db->bind(':show_id', $showId);
        $scoringSettings = $this->db->single();
        
        if (!$scoringSettings) {
            // Create default settings object if none exists
            $scoringSettings = (object)[
                'use_age_weight' => true,
                'age_weight_multiplier' => 1.0,
                'weight_multiplier' => 100,
                'normalize_scores' => false
            ];
        }
        
        // Get the active formula
        $activeFormula = isset($scoringSettings->formula) ? $scoringSettings->formula : 'rawScore * metricWeight * ageWeight';
        
        // Get formula name
        $formulaName = isset($scoringSettings->formula_name) ? $scoringSettings->formula_name : 'Standard';
        
        // Get metric scores from the database if available
        $this->db->query("SELECT * FROM vehicle_metric_scores 
                         WHERE show_id = :show_id AND vehicle_id = :vehicle_id
                         ORDER BY metric_name");
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':vehicle_id', $vehicleId);
        $metricScores = $this->db->resultSet();
        
        // Calculate weighted total score
        $weightedTotalScore = $totalScore;
        
        $data = [
            'title' => 'Vehicle Score',
            'show' => $show,
            'vehicle' => $vehicle,
            'registration' => $registration,
            'totalScore' => $totalScore,
            'primaryImage' => $primaryImage,
            'metrics' => $metrics,
            'scores' => $scores,
            'age_weight' => $ageWeight,
            'scoring_settings' => $scoringSettings,
            'active_formula' => $activeFormula,
            'formula_name' => $formulaName,
            'weighted_total_score' => $weightedTotalScore,
            'metric_scores' => $metricScores
        ];
        
        $this->view('show/vehicle_score', $data);
    }

    /**
     * Collect security data for voting
     */
    private function collectSecurityData($showId) {
        $securityData = [];

        // Get GPS location if provided
        if (isset($_POST['latitude']) && isset($_POST['longitude'])) {
            $securityData['latitude'] = floatval($_POST['latitude']);
            $securityData['longitude'] = floatval($_POST['longitude']);
            $securityData['location_accuracy'] = isset($_POST['location_accuracy']) ? floatval($_POST['location_accuracy']) : null;
        }

        // Device fingerprinting data
        if (isset($_POST['device_fingerprint'])) {
            $securityData['device_fingerprint'] = $_POST['device_fingerprint'];
        } else {
            // Generate basic fingerprint from available data
            require_once APPROOT . '/models/VotingSecurityModel.php';
            $securityModel = new VotingSecurityModel();
            $browserData = [
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'screen_resolution' => $_POST['screen_resolution'] ?? '',
                'timezone' => $_POST['timezone'] ?? '',
                'language' => $_POST['language'] ?? $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
                'platform' => $_POST['platform'] ?? '',
                'plugins' => $_POST['plugins'] ?? '',
                'canvas_fingerprint' => $_POST['canvas_fingerprint'] ?? ''
            ];
            $securityData['device_fingerprint'] = $securityModel->generateDeviceFingerprint($browserData);
        }

        // Browser and system information
        $securityData['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $securityData['screen_resolution'] = $_POST['screen_resolution'] ?? '';
        $securityData['timezone'] = $_POST['timezone'] ?? '';
        $securityData['language'] = $_POST['language'] ?? $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
        $securityData['referrer'] = $_SERVER['HTTP_REFERER'] ?? '';
        $securityData['session_id'] = session_id();

        // Mobile detection
        $securityData['is_mobile'] = $this->isMobileDevice();

        // Vote timing
        if (isset($_POST['page_load_time'])) {
            $pageLoadTime = intval($_POST['page_load_time']);
            $currentTime = time() * 1000; // Convert to milliseconds
            $securityData['vote_duration_seconds'] = ($currentTime - $pageLoadTime) / 1000;
        }

        // IP geolocation (if not GPS available)
        if (!isset($securityData['latitude'])) {
            require_once APPROOT . '/models/VotingSecurityModel.php';
            $securityModel = new VotingSecurityModel();
            $ipGeo = $securityModel->getIpGeolocation($_SERVER['REMOTE_ADDR']);
            if ($ipGeo) {
                $securityData['ip_country'] = $ipGeo['country'] ?? '';
                $securityData['ip_region'] = $ipGeo['region'] ?? '';
                $securityData['ip_city'] = $ipGeo['city'] ?? '';
                $securityData['is_vpn'] = $ipGeo['is_vpn'] ?? false;

                // Use IP geolocation as fallback for distance calculation
                if (isset($ipGeo['latitude']) && isset($ipGeo['longitude'])) {
                    $securityData['latitude'] = $ipGeo['latitude'];
                    $securityData['longitude'] = $ipGeo['longitude'];
                    $securityData['location_accuracy'] = 50000; // IP geolocation is less accurate
                }
            }
        }

        return $securityData;
    }

    /**
     * Detect if request is from mobile device
     */
    private function isMobileDevice() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $mobileKeywords = ['Mobile', 'Android', 'iPhone', 'iPad', 'Windows Phone', 'BlackBerry'];

        foreach ($mobileKeywords as $keyword) {
            if (stripos($userAgent, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Display vote results for a vehicle (post-show)
     */
    public function voteResults($registrationId) {
        // Get registration details
        $registration = $this->registrationModel->getRegistrationById($registrationId);

        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }

        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);

        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }

        // Check if user owns this vehicle or is admin/coordinator
        $canView = false;
        if (isLoggedIn()) {
            $canView = ($registration->user_id == $_SESSION['user_id']) ||
                      $this->auth->hasRole('admin') ||
                      ($this->auth->hasRole('coordinator') && $show->coordinator_id == $_SESSION['user_id']);
        }

        if (!$canView) {
            $this->redirect('home/access_denied');
            return;
        }

        // Get vehicle details
        $vehicle = $this->vehicleModel->getVehicleById($registration->vehicle_id);
        $category = $this->showCategoryModel->getCategoryById($registration->category_id);

        // Get vehicle image
        $vehicleImage = '';
        $images = $this->vehicleModel->getVehicleImages($registration->vehicle_id);
        foreach ($images as $image) {
            if ($image->is_primary) {
                $vehicleImage = $image->file_path;
                break;
            }
        }

        // Get votes for this vehicle with security analysis
        $this->db->query('SELECT * FROM fan_votes
                         WHERE registration_id = :registration_id
                         ORDER BY created_at DESC');
        $this->db->bind(':registration_id', $registrationId);
        $votes = $this->db->resultSet();

        // Calculate vote statistics
        $voteStats = [
            'total_votes' => count($votes),
            'verified_votes' => count(array_filter($votes, function($v) { return $v->is_approved && !$v->is_flagged; }))
        ];

        // Calculate security statistics
        $securityStats = [
            'gps_verified' => count(array_filter($votes, function($v) { return $v->latitude && $v->longitude; })),
            'facebook_votes' => count(array_filter($votes, function($v) { return $v->vote_method === 'facebook'; })),
            'staff_votes' => count(array_filter($votes, function($v) { return strpos($v->vote_method, 'manual_') === 0; })),
            'flagged_votes' => count(array_filter($votes, function($v) { return $v->is_flagged; }))
        ];

        // Get fraud patterns for this vehicle
        $this->db->query('SELECT * FROM fraud_detection_patterns
                         WHERE show_id = :show_id AND pattern_data LIKE :registration_search
                         AND reviewed = TRUE
                         ORDER BY created_at DESC');
        $this->db->bind(':show_id', $registration->show_id);
        $this->db->bind(':registration_search', '%"registration_id":' . $registrationId . '%');
        $fraudPatterns = $this->db->resultSet();

        $data = [
            'title' => 'Vote Results - ' . $vehicle->year . ' ' . $vehicle->make . ' ' . $vehicle->model,
            'show' => $show,
            'registration' => $registration,
            'vehicle' => $vehicle,
            'category' => $category,
            'vehicle_image' => $vehicleImage,
            'votes' => $votes,
            'vote_stats' => $voteStats,
            'security_stats' => $securityStats,
            'fraud_patterns' => $fraudPatterns
        ];

        $this->view('show/vote_results', $data);
    }

    /**
     * Show vote appeal form
     */
    public function voteAppeal($showId, $registrationId) {
        // Get show and registration details
        $show = $this->showModel->getShowById($showId);
        $registration = $this->registrationModel->getRegistrationById($registrationId);

        if (!$show || !$registration) {
            $this->redirect('home/not_found');
            return;
        }

        // Get vehicle details
        $vehicle = $this->vehicleModel->getVehicleById($registration->vehicle_id);

        $data = [
            'title' => 'Appeal Blocked Vote - ' . $show->name,
            'show' => $show,
            'show_id' => $showId,
            'registration' => $registration,
            'registration_id' => $registrationId,
            'vehicle' => $vehicle
        ];

        $this->view('show/vote_appeal', $data);
    }

    /**
     * Submit vote appeal
     */
    public function submitVoteAppeal() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('home/error/Invalid%20request');
            return;
        }

        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            flash('appeal_message', 'Invalid request. Please try again.', 'alert alert-danger');
            $this->redirect('home/error/Invalid%20request');
            return;
        }

        // Sanitize input
        $_POST = $this->sanitizeInput($_POST);

        $showId = intval($_POST['show_id']);
        $registrationId = intval($_POST['registration_id']);
        $voterName = trim($_POST['voter_name']);
        $voterEmail = trim($_POST['voter_email']);
        $voterPhone = trim($_POST['voter_phone']);
        $appealReason = $_POST['appeal_reason'];
        $otherReason = trim($_POST['other_reason']);
        $appealExplanation = trim($_POST['appeal_explanation']);
        $witnessName = trim($_POST['witness_name']);
        $witnessContact = trim($_POST['witness_contact']);
        $socialMediaProof = trim($_POST['social_media_proof']);
        $receiptOrTicket = trim($_POST['receipt_or_ticket']);

        // Validate required fields
        if (empty($voterName) || empty($voterEmail) || empty($appealReason) || empty($appealExplanation)) {
            flash('appeal_message', 'Please fill in all required fields.', 'alert alert-danger');
            $this->redirect('show/voteAppeal/' . $showId . '/' . $registrationId);
            return;
        }

        // Validate email
        if (!filter_var($voterEmail, FILTER_VALIDATE_EMAIL)) {
            flash('appeal_message', 'Please enter a valid email address.', 'alert alert-danger');
            $this->redirect('show/voteAppeal/' . $showId . '/' . $registrationId);
            return;
        }

        // Check if appeal already exists
        $this->db->query('SELECT id FROM vote_appeals
                         WHERE show_id = :show_id AND registration_id = :registration_id AND voter_email = :email');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':registration_id', $registrationId);
        $this->db->bind(':email', $voterEmail);
        $existingAppeal = $this->db->single();

        if ($existingAppeal) {
            flash('appeal_message', 'An appeal has already been submitted for this vote.', 'alert alert-warning');
            $this->redirect('show/details/' . $showId);
            return;
        }

        // Insert appeal
        $this->db->query('INSERT INTO vote_appeals
                         (show_id, registration_id, voter_name, voter_email, voter_phone, appeal_reason,
                          other_reason, appeal_explanation, witness_name, witness_contact,
                          social_media_proof, receipt_or_ticket)
                         VALUES
                         (:show_id, :registration_id, :voter_name, :voter_email, :voter_phone, :appeal_reason,
                          :other_reason, :appeal_explanation, :witness_name, :witness_contact,
                          :social_media_proof, :receipt_or_ticket)');

        $this->db->bind(':show_id', $showId);
        $this->db->bind(':registration_id', $registrationId);
        $this->db->bind(':voter_name', $voterName);
        $this->db->bind(':voter_email', $voterEmail);
        $this->db->bind(':voter_phone', $voterPhone);
        $this->db->bind(':appeal_reason', $appealReason);
        $this->db->bind(':other_reason', $otherReason);
        $this->db->bind(':appeal_explanation', $appealExplanation);
        $this->db->bind(':witness_name', $witnessName);
        $this->db->bind(':witness_contact', $witnessContact);
        $this->db->bind(':social_media_proof', $socialMediaProof);
        $this->db->bind(':receipt_or_ticket', $receiptOrTicket);

        if ($this->db->execute()) {
            // Send notification to coordinators/admins
            $this->notifyAppealSubmitted($showId, $voterName, $voterEmail);

            flash('appeal_message', 'Your appeal has been submitted successfully. You will receive an email notification when it has been reviewed.', 'alert alert-success');
            $this->redirect('show/details/' . $showId);
        } else {
            flash('appeal_message', 'Error submitting appeal. Please try again.', 'alert alert-danger');
            $this->redirect('show/voteAppeal/' . $showId . '/' . $registrationId);
        }
    }

    /**
     * Notify coordinators/admins of new appeal
     */
    private function notifyAppealSubmitted($showId, $voterName, $voterEmail) {
        try {
            // Get show details
            $show = $this->showModel->getShowById($showId);
            if (!$show) return;

            // Initialize security model for notifications
            require_once APPROOT . '/models/VotingSecurityModel.php';
            $securityModel = new VotingSecurityModel();

            $title = "New Vote Appeal Submitted";
            $message = "A new vote appeal has been submitted for {$show->name}.\n\nVoter: {$voterName}\nEmail: {$voterEmail}\n\nPlease review the appeal in the coordinator dashboard.";

            $securityModel->createAlert(
                $showId,
                'manual_review_needed',
                'warning',
                $title,
                $message,
                [
                    'voter_name' => $voterName,
                    'voter_email' => $voterEmail,
                    'appeal_type' => 'vote_appeal'
                ]
            );

        } catch (Exception $e) {
            error_log('Error notifying appeal submission: ' . $e->getMessage());
        }
    }


}
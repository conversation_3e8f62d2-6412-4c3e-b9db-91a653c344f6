<?php
/**
 * Judging Model
 * 
 * This model handles all database operations related to judging and scoring.
 */
class JudgingModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get scores for a registration
     * 
     * @param int $registrationId Registration ID
     * @return array
     */
    public function getScores($registrationId) {
        $this->db->query('SELECT s.*, jm.name as metric_name, jm.description as metric_description, jm.max_score, jm.weight, 
                          u.name as judge_name 
                          FROM scores s 
                          JOIN judging_metrics jm ON s.metric_id = jm.id 
                          JOIN users u ON s.judge_id = u.id 
                          WHERE s.registration_id = :registration_id 
                          ORDER BY u.name, jm.display_order');
        $this->db->bind(':registration_id', $registrationId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get scores by a specific judge for a registration
     * 
     * @param int $registrationId Registration ID
     * @param int $judgeId Judge ID
     * @return array
     */
    public function getJudgeScores($registrationId, $judgeId) {
        $this->db->query('SELECT s.*, jm.name as metric_name, jm.description, jm.max_score, jm.weight 
                          FROM scores s 
                          JOIN judging_metrics jm ON s.metric_id = jm.id 
                          WHERE s.registration_id = :registration_id AND s.judge_id = :judge_id 
                          ORDER BY jm.display_order');
        $this->db->bind(':registration_id', $registrationId);
        $this->db->bind(':judge_id', $judgeId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get auto-saved scores by a specific judge for a registration
     * 
     * @param int $registrationId Registration ID
     * @param int $judgeId Judge ID
     * @return array
     */
    public function getAutoSavedScores($registrationId, $judgeId) {
        // Ensure the auto_saved column exists
        $this->ensureColumnExists('scores', 'auto_saved', 'TINYINT(1) NOT NULL DEFAULT 0');
        
        $this->db->query('SELECT s.*, jm.name as metric_name, jm.description, jm.max_score, jm.weight 
                          FROM scores s 
                          JOIN judging_metrics jm ON s.metric_id = jm.id 
                          WHERE s.registration_id = :registration_id 
                          AND s.judge_id = :judge_id 
                          AND s.auto_saved = 1
                          ORDER BY jm.display_order');
        $this->db->bind(':registration_id', $registrationId);
        $this->db->bind(':judge_id', $judgeId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Create scores table if it doesn't exist
     * 
     * @return bool True if successful, false otherwise
     */
    private function createScoresTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS scores (
                id INT AUTO_INCREMENT PRIMARY KEY,
                registration_id INT NOT NULL,
                judge_id INT NOT NULL,
                metric_id INT NOT NULL,
                score DECIMAL(5,2) NOT NULL,
                comments TEXT,
                is_draft TINYINT(1) NOT NULL DEFAULT 0,
                auto_saved TINYINT(1) NOT NULL DEFAULT 0,
                last_auto_save DATETIME NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_score (registration_id, judge_id, metric_id)
            )";
            
            $this->db->query($sql);
            $result = $this->db->execute();
            
            if ($result) {
                error_log("Created scores table successfully");
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error creating scores table: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Save a score
     * 
     * @param array $data Score data
     * @return bool|int False on failure, score ID on success
     */
    public function saveScore($data) {
        try {
            // Check if scores table exists
            if (!$this->tableExists('scores')) {
                // Create the table if it doesn't exist
                if (!$this->createScoresTable()) {
                    error_log("Failed to create scores table");
                    return false;
                }
            } else {
                // Ensure required columns exist
                $this->ensureColumnExists('scores', 'is_draft', 'TINYINT(1) NOT NULL DEFAULT 0');
                $this->ensureColumnExists('scores', 'auto_saved', 'TINYINT(1) NOT NULL DEFAULT 0');
                $this->ensureColumnExists('scores', 'last_auto_save', 'DATETIME NULL');
            }
            
            // Set default values for optional fields
            $data['comments'] = isset($data['comments']) ? $data['comments'] : '';
            $data['is_draft'] = isset($data['is_draft']) ? $data['is_draft'] : 0;
            $data['auto_saved'] = isset($data['auto_saved']) ? $data['auto_saved'] : 0;
            
            // Ensure the judges table exists and has the is_active column
            if (!$this->tableExists('judges')) {
                $this->createJudgesTable();
            } else {
                $this->ensureColumnExists('judges', 'is_active', 'TINYINT(1) NOT NULL DEFAULT 1');
            }
            
            // Check if the judge exists in the judges table
            $this->db->query('SELECT id FROM judges WHERE id = :judge_id');
            $this->db->bind(':judge_id', $data['judge_id']);
            $judgeExists = $this->db->single();
            
            if (!$judgeExists) {
                // Check if the judge exists in the users table with judge role
                $this->db->query('SELECT id FROM users WHERE id = :user_id AND (role = "judge" OR role = "admin")');
                $this->db->bind(':user_id', $data['judge_id']);
                $userExists = $this->db->single();
                
                if ($userExists) {
                    try {
                        // Get the show_id from the registration
                        $this->db->query('SELECT show_id FROM registrations WHERE id = :registration_id');
                        $this->db->bind(':registration_id', $data['registration_id']);
                        $registration = $this->db->single();
                        
                        if (!$registration) {
                            error_log("Registration ID " . $data['registration_id'] . " not found");
                            return false;
                        }
                        
                        // First check if a judge entry already exists with this user_id and show_id
                        $this->db->query('SELECT id FROM judges WHERE user_id = :user_id AND show_id = :show_id');
                        $this->db->bind(':user_id', $data['judge_id']);
                        $this->db->bind(':show_id', $registration->show_id);
                        $existingJudge = $this->db->single();
                        
                        if ($existingJudge) {
                            // Judge entry exists but with a different id, update the id
                            $this->db->query('UPDATE judges SET id = :new_id WHERE id = :old_id');
                            $this->db->bind(':new_id', $data['judge_id']);
                            $this->db->bind(':old_id', $existingJudge->id);
                            
                            if (!$this->db->execute()) {
                                error_log("Failed to update judge ID for user ID " . $data['judge_id']);
                                // Continue anyway, we'll use the existing judge entry
                            }
                        } else {
                            // No existing entry, create a new one
                            // Check if the judges table has the is_active column
                            $hasIsActiveColumn = $this->columnExists('judges', 'is_active');
                            
                            try {
                                if ($hasIsActiveColumn) {
                                    // Create a judge entry for this user with is_active
                                    $this->db->query('INSERT INTO judges (id, user_id, show_id, is_active) 
                                                   VALUES (:id, :user_id, :show_id, 1)');
                                } else {
                                    // Create a judge entry without is_active
                                    $this->db->query('INSERT INTO judges (id, user_id, show_id) 
                                                   VALUES (:id, :user_id, :show_id)');
                                }
                                
                                $this->db->bind(':id', $data['judge_id']);
                                $this->db->bind(':user_id', $data['judge_id']);
                                $this->db->bind(':show_id', $registration->show_id);
                                
                                if (!$this->db->execute()) {
                                    error_log("Failed to create judge entry for user ID " . $data['judge_id']);
                                    return false;
                                }
                            } catch (Exception $e) {
                                error_log("Error creating judge entry: " . $e->getMessage());
                                // Try an alternative approach - insert without specifying the id
                                try {
                                    if ($hasIsActiveColumn) {
                                        $this->db->query('INSERT INTO judges (user_id, show_id, is_active) 
                                                       VALUES (:user_id, :show_id, 1)');
                                    } else {
                                        $this->db->query('INSERT INTO judges (user_id, show_id) 
                                                       VALUES (:user_id, :show_id)');
                                    }
                                    
                                    $this->db->bind(':user_id', $data['judge_id']);
                                    $this->db->bind(':show_id', $registration->show_id);
                                    
                                    if (!$this->db->execute()) {
                                        error_log("Failed to create judge entry (alternative method) for user ID " . $data['judge_id']);
                                        return false;
                                    }
                                } catch (Exception $e2) {
                                    error_log("Error creating judge entry (alternative method): " . $e2->getMessage());
                                    return false;
                                }
                            }
                        }
                        
                        // Now ensure the is_active column exists
                        $this->ensureColumnExists('judges', 'is_active', 'TINYINT(1) NOT NULL DEFAULT 1');
                    } catch (Exception $e) {
                        error_log("Error creating judge entry: " . $e->getMessage());
                        return false;
                    }
                } else {
                    error_log("Judge ID " . $data['judge_id'] . " does not exist in users table with judge role");
                    return false;
                }
            }
            
            // Check if score already exists
            $this->db->query('SELECT id FROM scores 
                            WHERE registration_id = :registration_id AND judge_id = :judge_id AND metric_id = :metric_id');
            $this->db->bind(':registration_id', $data['registration_id']);
            $this->db->bind(':judge_id', $data['judge_id']);
            $this->db->bind(':metric_id', $data['metric_id']);
            $existingScore = $this->db->single();
            
            if ($existingScore) {
                // Update existing score
                $updateQuery = 'UPDATE scores SET score = :score, comments = :comments, 
                              is_draft = :is_draft, updated_at = NOW()';
                
                // Add auto-save fields if this is an auto-save operation
                if (isset($data['auto_saved']) && $data['auto_saved']) {
                    $updateQuery .= ', auto_saved = 1, last_auto_save = NOW()';
                }
                
                $updateQuery .= ' WHERE id = :id';
                
                $this->db->query($updateQuery);
                $this->db->bind(':score', $data['score']);
                $this->db->bind(':comments', $data['comments']);
                $this->db->bind(':is_draft', $data['is_draft']);
                $this->db->bind(':id', $existingScore->id);
                
                return $this->db->execute() ? $existingScore->id : false;
            } else {
                // Create new score
                $insertQuery = 'INSERT INTO scores (registration_id, judge_id, metric_id, score, 
                              comments, is_draft';
                
                $valuesQuery = ' VALUES (:registration_id, :judge_id, :metric_id, :score, 
                              :comments, :is_draft';
                
                // Add auto-save fields if this is an auto-save operation
                if (isset($data['auto_saved']) && $data['auto_saved']) {
                    $insertQuery .= ', auto_saved, last_auto_save';
                    $valuesQuery .= ', 1, NOW()';
                }
                
                $insertQuery .= ', created_at)';
                $valuesQuery .= ', NOW())';
                
                $this->db->query($insertQuery . $valuesQuery);
                $this->db->bind(':registration_id', $data['registration_id']);
                $this->db->bind(':judge_id', $data['judge_id']);
                $this->db->bind(':metric_id', $data['metric_id']);
                $this->db->bind(':score', $data['score']);
                $this->db->bind(':comments', $data['comments']);
                $this->db->bind(':is_draft', $data['is_draft']);
                
                return $this->db->execute() ? $this->db->lastInsertId() : false;
            }
        } catch (Exception $e) {
            error_log("Error saving score: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Save multiple scores at once
     * 
     * @param array $scores Array of score data
     * @return bool
     */
    public function saveScores($scores) {
        // Check if we have any scores to save
        if (empty($scores)) {
            error_log('No scores provided to saveScores method');
            return false;
        }
        
        error_log('Saving ' . count($scores) . ' scores');
        
        // Make sure scores table exists
        if (!$this->tableExists('scores')) {
            if (!$this->createScoresTable()) {
                error_log("Failed to create scores table in saveScores method");
                return false;
            }
        }
        
        $this->db->beginTransaction();
        
        try {
            $successCount = 0;
            
            foreach ($scores as $index => $score) {
                // Add debugging info
                error_log("Processing score {$index}: registration_id={$score['registration_id']}, judge_id={$score['judge_id']}, metric_id={$score['metric_id']}, score={$score['score']}");
                
                // Make sure all required fields are present
                if (!isset($score['registration_id']) || !isset($score['judge_id']) || 
                    !isset($score['metric_id']) || !isset($score['score'])) {
                    error_log("Missing required fields for score {$index}");
                    continue;
                }
                
                // Save the score
                $result = $this->saveScore($score);
                
                if ($result) {
                    $successCount++;
                    error_log("Successfully saved score {$index} with ID {$result}");
                } else {
                    error_log("Failed to save score {$index}");
                }
            }
            
            // If we saved at least one score, consider it a success
            if ($successCount > 0) {
                $this->db->commit();
                error_log("Successfully saved {$successCount} out of " . count($scores) . " scores");
                return true;
            } else {
                $this->db->rollBack();
                error_log("Failed to save any scores");
                return false;
            }
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('Error saving scores: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Finalize scores (mark as not draft)
     * 
     * @param int $registrationId Registration ID
     * @param int $judgeId Judge ID
     * @return bool
     */
    public function finalizeScores($registrationId, $judgeId) {
        $result = false;
        
        try {
            $this->db->query('UPDATE scores SET is_draft = 0, auto_saved = 0, updated_at = NOW() 
                              WHERE registration_id = :registration_id AND judge_id = :judge_id');
            $this->db->bind(':registration_id', $registrationId);
            $this->db->bind(':judge_id', $judgeId);
            
            $result = $this->db->execute();
            
            if ($result) {
                // Trigger automatic conflict detection for this registration
                $this->triggerConflictDetection($registrationId);
            }
            
        } catch (Exception $e) {
            error_log("JudgingModel::finalizeScores - Error: " . $e->getMessage());
        }
        
        return $result;
    }
    
    /**
     * Trigger automatic conflict detection for a registration
     * 
     * @param int $registrationId Registration ID
     * @return void
     */
    private function triggerConflictDetection($registrationId) {
        try {
            // Get show ID for this registration
            $this->db->query('SELECT show_id FROM registrations WHERE id = :registration_id');
            $this->db->bind(':registration_id', $registrationId);
            $registration = $this->db->single();
            
            if (!$registration) {
                return;
            }
            
            // Check if conflict detection is enabled
            $this->db->query('SELECT value FROM settings WHERE name = :name');
            $this->db->bind(':name', 'conflict_auto_detection_enabled');
            $setting = $this->db->single();
            
            if (!$setting || $setting->value != '1') {
                return;
            }
            
            // Load conflict model and run detection
            require_once APPROOT . '/models/JudgingConflictModel.php';
            $conflictModel = new JudgingConflictModel();
            $conflictModel->detectScoreDiscrepancies($registration->show_id);
            
        } catch (Exception $e) {
            error_log("JudgingModel::triggerConflictDetection - Error: " . $e->getMessage());
        }
    }
    
    /**
     * Auto-save scores
     * 
     * @param array $scores Array of score data
     * @return bool
     */
    public function autoSaveScores($scores) {
        // Check if we have any scores to save
        if (empty($scores)) {
            error_log('No scores provided to autoSaveScores method');
            return false;
        }
        
        error_log('Auto-saving ' . count($scores) . ' scores');
        
        // Make sure scores table exists
        if (!$this->tableExists('scores')) {
            if (!$this->createScoresTable()) {
                error_log("Failed to create scores table in autoSaveScores method");
                return false;
            }
        } else {
            // Ensure auto_saved column exists
            $this->ensureColumnExists('scores', 'auto_saved', 'TINYINT(1) NOT NULL DEFAULT 0');
            $this->ensureColumnExists('scores', 'last_auto_save', 'DATETIME NULL');
        }
        
        // Make sure judges table exists
        if (!$this->tableExists('judges')) {
            if (!$this->createJudgesTable()) {
                error_log("Failed to create judges table in autoSaveScores method");
                return false;
            }
        } else {
            // Ensure the is_active column exists
            $this->ensureColumnExists('judges', 'is_active', 'TINYINT(1) NOT NULL DEFAULT 1');
        }
        
        // Get the first score to extract judge_id and registration_id
        $firstScore = reset($scores);
        $judgeId = $firstScore['judge_id'];
        $registrationId = $firstScore['registration_id'];
        
        // Check if the judge exists in the judges table - without using is_active
        $this->db->query('SELECT id FROM judges WHERE id = :judge_id');
        $this->db->bind(':judge_id', $judgeId);
        $judgeExists = $this->db->single();
        
        // If judge doesn't exist, try to create an entry
        if (!$judgeExists) {
            // Check if the user exists with judge role
            $this->db->query('SELECT id FROM users WHERE id = :user_id AND (role = "judge" OR role = "admin")');
            $this->db->bind(':user_id', $judgeId);
            $userExists = $this->db->single();
            
            if ($userExists) {
                // Get the show_id from the registration
                $this->db->query('SELECT show_id FROM registrations WHERE id = :registration_id');
                $this->db->bind(':registration_id', $registrationId);
                $registration = $this->db->single();
                
                if ($registration) {
                    try {
                        // First check if a judge entry already exists with this user_id and show_id
                        $this->db->query('SELECT id FROM judges WHERE user_id = :user_id AND show_id = :show_id');
                        $this->db->bind(':user_id', $judgeId);
                        $this->db->bind(':show_id', $registration->show_id);
                        $existingJudge = $this->db->single();
                        
                        if ($existingJudge) {
                            // Judge entry exists but with a different id, update the id
                            $this->db->query('UPDATE judges SET id = :new_id WHERE id = :old_id');
                            $this->db->bind(':new_id', $judgeId);
                            $this->db->bind(':old_id', $existingJudge->id);
                            
                            if (!$this->db->execute()) {
                                error_log("Failed to update judge ID for user ID " . $judgeId);
                                // Continue anyway, we'll use the existing judge entry
                            }
                        } else {
                            // No existing entry, create a new one
                            // Check if the judges table has the is_active column
                            $hasIsActiveColumn = $this->columnExists('judges', 'is_active');
                            
                            try {
                                if ($hasIsActiveColumn) {
                                    // Create a judge entry for this user with is_active
                                    $this->db->query('INSERT INTO judges (id, user_id, show_id, is_active) 
                                                   VALUES (:id, :user_id, :show_id, 1)');
                                } else {
                                    // Create a judge entry without is_active
                                    $this->db->query('INSERT INTO judges (id, user_id, show_id) 
                                                   VALUES (:id, :user_id, :show_id)');
                                }
                                
                                $this->db->bind(':id', $judgeId);
                                $this->db->bind(':user_id', $judgeId);
                                $this->db->bind(':show_id', $registration->show_id);
                                
                                if (!$this->db->execute()) {
                                    error_log("Failed to create judge entry for user ID " . $judgeId);
                                    return false;
                                }
                            } catch (Exception $e) {
                                error_log("Error creating judge entry: " . $e->getMessage());
                                // Try an alternative approach - insert without specifying the id
                                try {
                                    if ($hasIsActiveColumn) {
                                        $this->db->query('INSERT INTO judges (user_id, show_id, is_active) 
                                                       VALUES (:user_id, :show_id, 1)');
                                    } else {
                                        $this->db->query('INSERT INTO judges (user_id, show_id) 
                                                       VALUES (:user_id, :show_id)');
                                    }
                                    
                                    $this->db->bind(':user_id', $judgeId);
                                    $this->db->bind(':show_id', $registration->show_id);
                                    
                                    if (!$this->db->execute()) {
                                        error_log("Failed to create judge entry (alternative method) for user ID " . $judgeId);
                                        return false;
                                    }
                                } catch (Exception $e2) {
                                    error_log("Error creating judge entry (alternative method): " . $e2->getMessage());
                                    return false;
                                }
                            }
                        }
                        
                        // Now ensure the is_active column exists
                        $this->ensureColumnExists('judges', 'is_active', 'TINYINT(1) NOT NULL DEFAULT 1');
                    } catch (Exception $e) {
                        error_log("Error creating judge entry: " . $e->getMessage());
                        return false;
                    }
                } else {
                    error_log("Registration ID " . $registrationId . " not found");
                    return false;
                }
            } else {
                error_log("User ID " . $judgeId . " does not exist or is not a judge/admin");
                return false;
            }
        }
        
        $this->db->beginTransaction();
        
        try {
            $successCount = 0;
            
            foreach ($scores as $index => $score) {
                // Add debugging info
                error_log("Auto-saving score {$index}: registration_id={$score['registration_id']}, judge_id={$score['judge_id']}, metric_id={$score['metric_id']}, score={$score['score']}");
                
                // Make sure all required fields are present
                if (!isset($score['registration_id']) || !isset($score['judge_id']) || 
                    !isset($score['metric_id']) || !isset($score['score'])) {
                    error_log("Missing required fields for score {$index}");
                    continue;
                }
                
                // Mark as auto-saved
                $score['auto_saved'] = 1;
                $score['is_draft'] = 1;  // Auto-saved scores are always drafts
                
                // Save the score
                $result = $this->saveScore($score);
                
                if ($result) {
                    $successCount++;
                    error_log("Successfully auto-saved score {$index} with ID {$result}");
                } else {
                    error_log("Failed to auto-save score {$index}");
                }
            }
            
            // If we saved at least one score, consider it a success
            if ($successCount > 0) {
                $this->db->commit();
                error_log("Successfully auto-saved {$successCount} out of " . count($scores) . " scores");
                return true;
            } else {
                $this->db->rollBack();
                error_log("Failed to auto-save any scores");
                return false;
            }
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('Error auto-saving scores: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if all metrics have been scored for a registration by a judge
     * 
     * @param int $registrationId Registration ID
     * @param int $judgeId Judge ID
     * @param int $showId Show ID
     * @return bool
     */
    public function isJudgingComplete($registrationId, $judgeId, $showId) {
        // Count total metrics for this show
        $this->db->query('SELECT COUNT(*) as metric_count FROM judging_metrics WHERE show_id = :show_id');
        $this->db->bind(':show_id', $showId);
        $metricCount = $this->db->single()->metric_count;
        
        // Count scored metrics for this registration by this judge
        $this->db->query('SELECT COUNT(*) as scored_count FROM scores 
                          WHERE registration_id = :registration_id AND judge_id = :judge_id');
        $this->db->bind(':registration_id', $registrationId);
        $this->db->bind(':judge_id', $judgeId);
        $scoredCount = $this->db->single()->scored_count;
        
        return $scoredCount >= $metricCount;
    }
    
    /**
     * Calculate total score for a registration
     * 
     * @param int $registrationId Registration ID
     * @return float
     */
    public function calculateTotalScore($registrationId) {
        // Get the vehicle and show information
        $this->db->query('SELECT v.id as vehicle_id, v.year, r.show_id 
                          FROM registrations r 
                          JOIN vehicles v ON r.vehicle_id = v.id 
                          WHERE r.id = :registration_id');
        $this->db->bind(':registration_id', $registrationId);
        $vehicle = $this->db->single();
        
        if (!$vehicle) {
            return 0;
        }
        
        // Use the VehicleScoringModel to calculate the score using the current formula
        require_once APPROOT . '/models/VehicleScoringModel.php';
        $scoringModel = new VehicleScoringModel();
        
        // Always calculate the score fresh
        $totalScore = $scoringModel->calculateVehicleScore($vehicle->show_id, $vehicle->vehicle_id);
        
        // If calculation failed, fall back to simple weighted average
        if ($totalScore === false) {
            // Get age multiplier
            $ageMultiplier = $this->getAgeMultiplier($vehicle->show_id, $vehicle->year);
            
            // Get all finalized scores
            $this->db->query('SELECT s.score, jm.weight 
                              FROM scores s 
                              JOIN judging_metrics jm ON s.metric_id = jm.id 
                              WHERE s.registration_id = :registration_id AND s.is_draft = 0');
            $this->db->bind(':registration_id', $registrationId);
            $scores = $this->db->resultSet();
            
            // Calculate weighted score
            $totalScore = 0;
            $totalWeight = 0;
            
            foreach ($scores as $score) {
                $totalScore += $score->score * $score->weight;
                $totalWeight += $score->weight;
            }
            
            // Apply age multiplier
            if ($totalWeight > 0) {
                $weightedScore = $totalScore / $totalWeight;
                return $weightedScore * $ageMultiplier;
            }
            
            return 0;
        }
        
        return $totalScore;
    }
    
    /**
     * Get age multiplier for a vehicle
     * 
     * @param int $showId Show ID
     * @param int $vehicleYear Vehicle year
     * @return float
     */
    private function getAgeMultiplier($showId, $vehicleYear) {
        // If vehicle year is not set, return default weight
        if (empty($vehicleYear) || !is_numeric($vehicleYear)) {
            error_log("Vehicle year is empty or not numeric: " . $vehicleYear);
            return 1.0;
        }
        
        // Convert to integer
        $year = (int)$vehicleYear;
        
        $this->db->query('SELECT multiplier, min_age, max_age FROM age_weights 
                          WHERE show_id = :show_id 
                          AND :year BETWEEN min_age AND max_age');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':year', $year);
        $result = $this->db->single();
        
        if ($result) {
            error_log("Found age weight for year {$year}: {$result->multiplier} (range: {$result->min_age}-{$result->max_age})");
            return (float)$result->multiplier;
        }
        
        error_log("No age weight found for year {$year} in show {$showId}");
        return 1.0;
    }
    
    /**
     * Get metrics by show ID
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getMetricsByShowId($showId) {
        $this->db->query('SELECT * FROM judging_metrics WHERE show_id = :show_id ORDER BY display_order');
        $this->db->bind(':show_id', $showId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get metric by ID
     * 
     * @param int $id Metric ID
     * @return object|bool
     */
    public function getMetricById($id) {
        $this->db->query('SELECT * FROM judging_metrics WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->single();
    }
    
    /**
     * Add metric
     * 
     * @param array $data Metric data
     * @return bool|int False on failure, metric ID on success
     */
    public function addMetric($data) {
        $this->db->query('INSERT INTO judging_metrics (show_id, name, description, max_score, weight, display_order) 
                          VALUES (:show_id, :name, :description, :max_score, :weight, :display_order)');
        $this->db->bind(':show_id', $data['show_id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':max_score', $data['max_score']);
        $this->db->bind(':weight', $data['weight']);
        $this->db->bind(':display_order', $data['display_order']);
        
        return $this->db->execute() ? $this->db->lastInsertId() : false;
    }
    
    /**
     * Update metric
     * 
     * @param array $data Metric data
     * @return bool
     */
    public function updateMetric($data) {
        $this->db->query('UPDATE judging_metrics 
                          SET name = :name, description = :description, 
                          max_score = :max_score, weight = :weight, display_order = :display_order 
                          WHERE id = :id');
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':max_score', $data['max_score']);
        $this->db->bind(':weight', $data['weight']);
        $this->db->bind(':display_order', $data['display_order']);
        
        return $this->db->execute();
    }
    
    /**
     * Delete metric
     * 
     * @param int $id Metric ID
     * @return bool
     */
    public function deleteMetric($id) {
        $this->db->query('DELETE FROM judging_metrics WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Get age weights by show ID
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getAgeWeightsByShowId($showId) {
        $this->db->query('SELECT * FROM age_weights WHERE show_id = :show_id ORDER BY min_age');
        $this->db->bind(':show_id', $showId);
        
        $results = $this->db->resultSet();
        
        // Debug log
        error_log('Age weights for show ' . $showId . ': ' . count($results));
        
        return $results;
    }
    
    /**
     * Get judges by show ID
     * 
     * @param int $showId Show ID
     * @return array Array of judge objects
     */
    public function getJudgesByShowId($showId) {
        try {
            // Check if the judges table exists
            if (!$this->tableExists('judges')) {
                // Create the table if it doesn't exist
                $this->createJudgesTable();
                return [];
            }
            
            // Get judges for the show
            $this->db->query('SELECT j.*, u.name, COUNT(DISTINCT jc.category_id) as assigned_categories 
                             FROM judges j 
                             JOIN users u ON j.user_id = u.id 
                             LEFT JOIN judge_categories jc ON j.id = jc.judge_id 
                             WHERE j.show_id = :show_id 
                             GROUP BY j.id 
                             ORDER BY u.name');
            $this->db->bind(':show_id', $showId);
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error getting judges by show ID: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get judges by category ID
     * 
     * @param int $categoryId Category ID
     * @return array Array of judge objects
     */
    public function getJudgesByCategory($categoryId) {
        try {
            // Check if the judge_categories table exists
            if (!$this->tableExists('judge_categories')) {
                return [];
            }
            
            // Get judges for the category
            $this->db->query('SELECT j.*, u.name 
                             FROM judges j 
                             JOIN users u ON j.user_id = u.id 
                             JOIN judge_categories jc ON j.id = jc.judge_id 
                             WHERE jc.category_id = :category_id 
                             ORDER BY u.name');
            $this->db->bind(':category_id', $categoryId);
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error getting judges by category ID: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get judged vehicle count by show
     * 
     * @param int $showId Show ID
     * @return int Count of judged vehicles
     */
    public function getJudgedVehicleCountByShow($showId) {
        try {
            // Check if the scores table exists
            if (!$this->tableExists('scores')) {
                return 0;
            }
            
            // Get count of distinct registrations that have scores
            $this->db->query('SELECT COUNT(DISTINCT s.registration_id) as judged_count 
                             FROM scores s 
                             JOIN registrations r ON s.registration_id = r.id 
                             WHERE r.show_id = :show_id AND s.is_draft = 0');
            $this->db->bind(':show_id', $showId);
            
            $result = $this->db->single();
            return $result ? $result->judged_count : 0;
        } catch (Exception $e) {
            error_log('Error getting judged vehicle count by show: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get judged vehicle count by category
     * 
     * @param int $categoryId Category ID
     * @return int Count of judged vehicles
     */
    public function getJudgedVehicleCountByCategory($categoryId) {
        try {
            // Check if the scores table exists
            if (!$this->tableExists('scores')) {
                return 0;
            }
            
            // Get count of distinct registrations that have scores
            $this->db->query('SELECT COUNT(DISTINCT s.registration_id) as judged_count 
                             FROM scores s 
                             JOIN registrations r ON s.registration_id = r.id 
                             WHERE r.category_id = :category_id AND s.is_draft = 0');
            $this->db->bind(':category_id', $categoryId);
            
            $result = $this->db->single();
            return $result ? $result->judged_count : 0;
        } catch (Exception $e) {
            error_log('Error getting judged vehicle count by category: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Check if a table exists
     * 
     * @param string $tableName Table name
     * @return bool True if table exists, false otherwise
     */
    private function tableExists($tableName) {
        try {
            // Use a different approach that works better with MySQL
            $this->db->query("SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = :tableName");
            $this->db->bind(':tableName', $tableName);
            $result = $this->db->single();
            return ($result !== false);
        } catch (Exception $e) {
            error_log('Error checking if table exists: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if a column exists in a table
     * 
     * @param string $tableName Table name
     * @param string $columnName Column name
     * @return bool True if column exists, false otherwise
     */
    private function columnExists($tableName, $columnName) {
        try {
            // Use information_schema to check if column exists - more reliable
            $this->db->query("SELECT COUNT(*) as column_exists FROM information_schema.columns 
                             WHERE table_schema = DATABASE() 
                             AND table_name = :tableName 
                             AND column_name = :columnName");
            $this->db->bind(':tableName', $tableName);
            $this->db->bind(':columnName', $columnName);
            $result = $this->db->single();
            
            return ($result && $result->column_exists > 0);
        } catch (Exception $e) {
            error_log('Error checking if column exists: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create the judges table if it doesn't exist
     */
    private function createJudgesTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS judges (
                id INT AUTO_INCREMENT PRIMARY KEY,
                show_id INT NOT NULL,
                user_id INT NOT NULL,
                is_active TINYINT(1) NOT NULL DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_judge (show_id, user_id)
            )";
            
            $this->db->query($sql);
            $this->db->execute();
            
            // Create judge_categories table if it doesn't exist
            $sql = "CREATE TABLE IF NOT EXISTS judge_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                judge_id INT NOT NULL,
                category_id INT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_judge_category (judge_id, category_id)
            )";
            
            $this->db->query($sql);
            $this->db->execute();
            
            // Check if is_active column exists in judges table, add it if not
            $this->ensureColumnExists('judges', 'is_active', 'TINYINT(1) NOT NULL DEFAULT 1');
            
            error_log("Created judges and judge_categories tables");
        } catch (Exception $e) {
            error_log("Error creating judges tables: " . $e->getMessage());
        }
    }
    
    /**
     * Ensure a column exists in a table
     * 
     * @param string $tableName Table name
     * @param string $columnName Column name
     * @param string $columnDefinition Column definition (e.g., 'VARCHAR(255) NOT NULL')
     * @return bool True if successful, false otherwise
     */
    private function ensureColumnExists($tableName, $columnName, $columnDefinition) {
        try {
            // First check if the table exists
            if (!$this->tableExists($tableName)) {
                error_log("Table {$tableName} does not exist, cannot add column {$columnName}");
                return false;
            }
            
            // Check if column exists using the improved method
            $columnExists = $this->columnExists($tableName, $columnName);
            
            // Only add the column if it doesn't exist
            if (!$columnExists) {
                // Add column if it doesn't exist
                $sql = "ALTER TABLE {$tableName} ADD COLUMN {$columnName} {$columnDefinition}";
                $this->db->query($sql);
                $result = $this->db->execute();
                
                if ($result) {
                    error_log("Added {$columnName} column to {$tableName} table");
                    
                    // For judges table, set default value for existing rows
                    if ($tableName === 'judges' && $columnName === 'is_active') {
                        try {
                            $this->db->query("UPDATE judges SET is_active = 1");
                            $this->db->execute();
                        } catch (Exception $e) {
                            error_log("Error updating is_active values: " . $e->getMessage());
                            // Continue anyway, this is not critical
                        }
                    }
                } else {
                    error_log("Failed to add {$columnName} column to {$tableName} table");
                    return false;
                }
            } else {
                // Column already exists, no need to add it
                error_log("Column {$columnName} already exists in {$tableName} table");
            }
            return true;
        } catch (Exception $e) {
            error_log("Error ensuring column {$columnName} exists in {$tableName}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get scores for a specific registration and judge
     * 
     * @param int $registrationId Registration ID
     * @param int $judgeId Judge ID
     * @return array Array of scores
     */
    public function getScoresByRegistrationAndJudge($registrationId, $judgeId) {
        try {
            $this->db->query('SELECT s.*, m.name as metric_name, m.max_score 
                            FROM scores s 
                            JOIN judging_metrics m ON s.metric_id = m.id 
                            WHERE s.registration_id = :registration_id AND s.judge_id = :judge_id');
            $this->db->bind(':registration_id', $registrationId);
            $this->db->bind(':judge_id', $judgeId);
            
            $results = $this->db->resultSet();
            
            // Index by metric_id for easier access
            $indexedResults = [];
            foreach ($results as $result) {
                $indexedResults[$result->metric_id] = $result;
            }
            
            return $indexedResults;
        } catch (Exception $e) {
            error_log('Error getting scores by registration and judge: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get judging results for a registration
     * 
     * @param int $registrationId Registration ID
     * @return array Array of judging results
     */
    public function getJudgingResultsByRegistration($registrationId) {
        try {
            // Get scores for this registration
            $scores = $this->getScoresByRegistration($registrationId, false);
            
            if (empty($scores)) {
                return [];
            }
            
            // Organize scores by judge
            $judgeScores = [];
            foreach ($scores as $score) {
                if (!isset($judgeScores[$score->judge_id])) {
                    $judgeScores[$score->judge_id] = [
                        'judge_name' => $score->judge_name,
                        'total_score' => 0,
                        'max_possible' => 0,
                        'scores' => []
                    ];
                }
                
                $judgeScores[$score->judge_id]['scores'][] = $score;
                $judgeScores[$score->judge_id]['total_score'] += $score->score;
                $judgeScores[$score->judge_id]['max_possible'] += $score->max_score;
            }
            
            return $judgeScores;
        } catch (Exception $e) {
            error_log("Error in getJudgingResultsByRegistration: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get awards for a registration
     * 
     * @param int $registrationId Registration ID
     * @return array Array of awards
     */
    public function getAwardsByRegistration($registrationId) {
        try {
            // Check if awards table exists
            if (!$this->tableExists('awards')) {
                return [];
            }
            
            $this->db->query('SELECT a.*, c.name as category_name 
                             FROM awards a 
                             LEFT JOIN award_categories c ON a.category_id = c.id 
                             WHERE a.registration_id = :registration_id');
            $this->db->bind(':registration_id', $registrationId);
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in getAwardsByRegistration: " . $e->getMessage());
            return [];
        }
    }
    
    public function getScoresByRegistration($registrationId, $includeDrafts = true) {
        try {
            // Check if scores table exists
            if (!$this->tableExists('scores')) {
                error_log("Scores table does not exist");
                return [];
            }
            
            // Debug log
            error_log("getScoresByRegistration: Checking for scores for registration {$registrationId}");
            
            // First, let's check if there are any scores at all for this registration
            $this->db->query('SELECT COUNT(*) as count FROM scores WHERE registration_id = :registration_id');
            $this->db->bind(':registration_id', $registrationId);
            $countResult = $this->db->single();
            
            if (!$countResult || $countResult->count == 0) {
                error_log("No scores found for registration {$registrationId} in the database");
                return [];
            }
            
            error_log("Found {$countResult->count} raw scores in the database for registration {$registrationId}");
            
            // Get the raw scores first
            $this->db->query('SELECT * FROM scores WHERE registration_id = :registration_id');
            $this->db->bind(':registration_id', $registrationId);
            $rawScores = $this->db->resultSet();
            
            if (empty($rawScores)) {
                error_log("No raw scores found for registration {$registrationId}");
                return [];
            }
            
            error_log("Retrieved " . count($rawScores) . " raw scores");
            
            // Now get the metric information
            $metricIds = array_unique(array_map(function($score) {
                return $score->metric_id;
            }, $rawScores));
            
            $metricInfo = [];
            foreach ($metricIds as $metricId) {
                $this->db->query('SELECT id, name, max_score FROM judging_metrics WHERE id = :id');
                $this->db->bind(':id', $metricId);
                $metric = $this->db->single();
                if ($metric) {
                    $metricInfo[$metricId] = $metric;
                }
            }
            
            // Get judge/user information
            $judgeIds = array_unique(array_map(function($score) {
                return $score->judge_id;
            }, $rawScores));
            
            $judgeInfo = [];
            foreach ($judgeIds as $judgeId) {
                $this->db->query('SELECT id, name FROM users WHERE id = :id');
                $this->db->bind(':id', $judgeId);
                $user = $this->db->single();
                if ($user) {
                    $judgeInfo[$judgeId] = $user;
                }
            }
            
            // Now combine all the information
            $results = [];
            foreach ($rawScores as $score) {
                // Skip draft scores if not including drafts
                if (!$includeDrafts && $score->is_draft) {
                    continue;
                }
                
                $scoreObj = clone $score;
                
                // Add metric information
                if (isset($metricInfo[$score->metric_id])) {
                    $scoreObj->metric_name = $metricInfo[$score->metric_id]->name;
                    $scoreObj->max_score = $metricInfo[$score->metric_id]->max_score;
                } else {
                    $scoreObj->metric_name = "Unknown Metric";
                    $scoreObj->max_score = 10; // Default
                }
                
                // Add judge information
                if (isset($judgeInfo[$score->judge_id])) {
                    $scoreObj->judge_name = $judgeInfo[$score->judge_id]->name;
                } else {
                    $scoreObj->judge_name = "Unknown Judge";
                }
                
                $results[] = $scoreObj;
            }
            
            error_log("Processed " . count($results) . " scores with additional information");
            
            return $results;
        } catch (Exception $e) {
            error_log('Error getting scores by registration: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Delete all age weights for a show
     * 
     * @param int $showId Show ID
     * @return bool True if successful, false otherwise
     */
    public function deleteAllAgeWeightsByShowId($showId) {
        $this->db->query('DELETE FROM age_weights WHERE show_id = :show_id');
        $this->db->bind(':show_id', $showId);
        
        // Execute
        if ($this->db->execute()) {
            // Debug log
            error_log('Deleted all age weights for show ' . $showId);
            return true;
        } else {
            error_log('Failed to delete age weights for show ' . $showId);
            return false;
        }
    }
    
    /**
     * Get age weight by ID
     * 
     * @param int $id Age weight ID
     * @return object|bool
     */
    public function getAgeWeightById($id) {
        $this->db->query('SELECT * FROM age_weights WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->single();
    }
    
    /**
     * Add age weight
     * 
     * @param array $data Age weight data
     * @return bool|int False on failure, age weight ID on success
     */
    public function addAgeWeight($data) {
        // Check if the age_weights table has description and is_active columns
        $hasDescriptionColumn = $this->columnExists('age_weights', 'description');
        $hasIsActiveColumn = $this->columnExists('age_weights', 'is_active');
        
        if ($hasDescriptionColumn && $hasIsActiveColumn) {
            $this->db->query('INSERT INTO age_weights (show_id, min_age, max_age, multiplier, description, is_active) 
                              VALUES (:show_id, :min_age, :max_age, :multiplier, :description, :is_active)');
            $this->db->bind(':show_id', $data['show_id']);
            $this->db->bind(':min_age', $data['min_age']);
            $this->db->bind(':max_age', $data['max_age']);
            $this->db->bind(':multiplier', $data['multiplier']);
            $this->db->bind(':description', $data['description'] ?? '');
            $this->db->bind(':is_active', $data['is_active'] ?? 1);
        } else {
            $this->db->query('INSERT INTO age_weights (show_id, min_age, max_age, multiplier) 
                              VALUES (:show_id, :min_age, :max_age, :multiplier)');
            $this->db->bind(':show_id', $data['show_id']);
            $this->db->bind(':min_age', $data['min_age']);
            $this->db->bind(':max_age', $data['max_age']);
            $this->db->bind(':multiplier', $data['multiplier']);
        }
        
        return $this->db->execute() ? $this->db->lastInsertId() : false;
    }
    

    
    /**
     * Update age weight
     * 
     * @param array $data Age weight data
     * @return bool
     */
    public function updateAgeWeight($data) {
        // Check if the age_weights table has description and is_active columns
        $hasDescriptionColumn = $this->columnExists('age_weights', 'description');
        $hasIsActiveColumn = $this->columnExists('age_weights', 'is_active');
        
        if ($hasDescriptionColumn && $hasIsActiveColumn) {
            $this->db->query('UPDATE age_weights 
                              SET min_age = :min_age, max_age = :max_age, multiplier = :multiplier,
                              description = :description, is_active = :is_active 
                              WHERE id = :id');
            $this->db->bind(':id', $data['id']);
            $this->db->bind(':min_age', $data['min_age']);
            $this->db->bind(':max_age', $data['max_age']);
            $this->db->bind(':multiplier', $data['multiplier']);
            $this->db->bind(':description', $data['description'] ?? '');
            $this->db->bind(':is_active', $data['is_active'] ?? 1);
        } else {
            $this->db->query('UPDATE age_weights 
                              SET min_age = :min_age, max_age = :max_age, multiplier = :multiplier 
                              WHERE id = :id');
            $this->db->bind(':id', $data['id']);
            $this->db->bind(':min_age', $data['min_age']);
            $this->db->bind(':max_age', $data['max_age']);
            $this->db->bind(':multiplier', $data['multiplier']);
        }
        
        return $this->db->execute();
    }
    
    /**
     * Delete age weight
     * 
     * @param int $id Age weight ID
     * @return bool
     */
    public function deleteAgeWeight($id) {
        $this->db->query('DELETE FROM age_weights WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Calculate and save results for a show
     * 
     * @param int $showId Show ID
     * @return bool
     */
    public function calculateResults($showId) {
        // Get all categories for this show
        $showModel = new ShowModel();
        $categories = $showModel->getShowCategories($showId);
        
        $this->db->beginTransaction();
        
        try {
            // Clear existing results
            $this->db->query('DELETE FROM show_results WHERE show_id = :show_id');
            $this->db->bind(':show_id', $showId);
            $this->db->execute();
            
            // Process each category
            foreach ($categories as $category) {
                // Get all approved registrations for this category
                $this->db->query('SELECT id FROM registrations 
                                  WHERE show_id = :show_id AND category_id = :category_id AND status = :status');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':category_id', $category->id);
                $this->db->bind(':status', 'approved');
                $registrations = $this->db->resultSet();
                
                // Calculate scores for each registration
                $scores = [];
                foreach ($registrations as $registration) {
                    $totalScore = $this->calculateTotalScore($registration->id);
                    $fanVotes = $this->countFanVotes($showId, $registration->id);
                    
                    $scores[] = [
                        'registration_id' => $registration->id,
                        'total_score' => $totalScore,
                        'fan_votes' => $fanVotes
                    ];
                }
                
                // Sort by score (descending)
                usort($scores, function($a, $b) {
                    return $b['total_score'] <=> $a['total_score'];
                });
                
                // Save results
                $place = 1;
                foreach ($scores as $score) {
                    $this->db->query('INSERT INTO show_results (show_id, category_id, registration_id, 
                                      place, total_score, fan_votes, created_at) 
                                      VALUES (:show_id, :category_id, :registration_id, 
                                      :place, :total_score, :fan_votes, NOW())');
                    $this->db->bind(':show_id', $showId);
                    $this->db->bind(':category_id', $category->id);
                    $this->db->bind(':registration_id', $score['registration_id']);
                    $this->db->bind(':place', $place);
                    $this->db->bind(':total_score', $score['total_score']);
                    $this->db->bind(':fan_votes', $score['fan_votes']);
                    $this->db->execute();
                    
                    $place++;
                }
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('Error calculating results: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get results for a show
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getShowResults($showId) {
        try {
            $this->db->query('SELECT cw.*, sc.name as category_name, 
                              v.make, v.model, v.year, v.color, 
                              u.name as owner_name, r.registration_number, r.display_number,
                              (SELECT file_path FROM images WHERE entity_type = "vehicle" AND entity_id = v.id AND is_primary = 1 LIMIT 1) as primary_image
                              FROM category_winners cw 
                              JOIN show_categories sc ON cw.category_id = sc.id 
                              JOIN registrations r ON cw.registration_id = r.id 
                              JOIN vehicles v ON r.vehicle_id = v.id 
                              JOIN users u ON v.owner_id = u.id 
                              WHERE cw.show_id = :show_id 
                              ORDER BY sc.name, cw.place');
            $this->db->bind(':show_id', $showId);
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in getShowResults: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get results for a category
     * 
     * @param int $showId Show ID
     * @param int $categoryId Category ID
     * @return array
     */
    public function getCategoryResults($showId, $categoryId) {
        try {
            $this->db->query('SELECT cw.*, sc.name as category_name, 
                              v.make, v.model, v.year, v.color, 
                              u.name as owner_name, r.registration_number, r.display_number,
                              (SELECT file_path FROM images WHERE entity_type = "vehicle" AND entity_id = v.id AND is_primary = 1 LIMIT 1) as primary_image
                              FROM category_winners cw 
                              JOIN show_categories sc ON cw.category_id = sc.id 
                              JOIN registrations r ON cw.registration_id = r.id 
                              JOIN vehicles v ON r.vehicle_id = v.id 
                              JOIN users u ON v.owner_id = u.id 
                              WHERE cw.show_id = :show_id AND cw.category_id = :category_id
                              ORDER BY cw.place');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':category_id', $categoryId);
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in getCategoryResults: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if an IP has already voted for a show
     * 
     * @param int $showId Show ID
     * @param string $voterIp Voter's IP address
     * @return bool
     */
    public function hasIpVoted($showId, $voterIp) {
        // Check if fan_votes table exists
        if (!$this->tableExists('fan_votes')) {
            // Create the table if it doesn't exist
            if (!$this->createFanVotesTable()) {
                error_log("Failed to create fan_votes table");
                return false;
            }
            // If we just created the table, there can't be any votes yet
            return false;
        }
        
        $this->db->query('SELECT id FROM fan_votes WHERE show_id = :show_id AND voter_ip = :voter_ip AND fb_user_id IS NULL');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':voter_ip', $voterIp);
        $existingVote = $this->db->single();
        
        return !empty($existingVote);
    }
    
    /**
     * Record a fan vote (IP-based)
     * 
     * @param int $showId Show ID
     * @param int $registrationId Registration ID
     * @param string $voterIp Voter's IP address
     * @return bool
     */
    /**
     * Create fan_votes table if it doesn't exist
     * 
     * @return bool True if successful, false otherwise
     */
    private function createFanVotesTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS fan_votes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                show_id INT NOT NULL,
                registration_id INT NOT NULL,
                voter_ip VARCHAR(45) NOT NULL,
                fb_user_id VARCHAR(100) NULL,
                fb_user_name VARCHAR(255) NULL,
                fb_user_email VARCHAR(255) NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                INDEX (show_id),
                INDEX (registration_id),
                INDEX (voter_ip),
                INDEX (fb_user_id)
            )";
            
            $this->db->query($sql);
            $result = $this->db->execute();
            
            if ($result) {
                error_log("Created fan_votes table successfully");
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error creating fan_votes table: " . $e->getMessage());
            return false;
        }
    }
    
    public function recordFanVote($showId, $registrationId, $voterIp, $securityData = []) {
        try {
            // Check if fan_votes table exists
            if (!$this->tableExists('fan_votes')) {
                // Create the table if it doesn't exist
                if (!$this->createFanVotesTable()) {
                    error_log("Failed to create fan_votes table");
                    return false;
                }
            }

            // Initialize security model
            require_once APPROOT . '/models/VotingSecurityModel.php';
            $securityModel = new VotingSecurityModel();

            // Prepare vote data with security information
            $voteData = array_merge([
                'show_id' => $showId,
                'registration_id' => $registrationId,
                'voter_ip' => $voterIp,
                'vote_method' => 'ip'
            ], $securityData);

            // Calculate risk score and security flags
            $riskAnalysis = $securityModel->calculateRiskScore($voteData, $showId);
            $voteData['risk_score'] = $riskAnalysis['score'];
            $voteData['security_flags'] = json_encode($riskAnalysis['flags']);

            // Get security settings for auto-flagging
            $settings = $securityModel->getSecuritySettings($showId);
            $voteData['is_flagged'] = $riskAnalysis['score'] >= $settings->auto_flag_threshold;
            $voteData['is_approved'] = $riskAnalysis['score'] < $settings->auto_block_threshold;

            // Build dynamic SQL for insert
            $columns = ['show_id', 'registration_id', 'voter_ip', 'vote_method', 'risk_score', 'security_flags', 'is_flagged', 'is_approved', 'created_at'];
            $placeholders = [':show_id', ':registration_id', ':voter_ip', ':vote_method', ':risk_score', ':security_flags', ':is_flagged', ':is_approved', 'NOW()'];

            // Add optional security columns if present
            $optionalColumns = [
                'latitude', 'longitude', 'location_accuracy', 'device_fingerprint', 'user_agent',
                'screen_resolution', 'timezone', 'language', 'referrer', 'session_id',
                'distance_from_show', 'ip_country', 'ip_region', 'ip_city', 'is_vpn',
                'is_mobile', 'vote_duration_seconds'
            ];

            foreach ($optionalColumns as $col) {
                if (isset($voteData[$col])) {
                    $columns[] = $col;
                    $placeholders[] = ":$col";
                }
            }

            $sql = 'INSERT INTO fan_votes (' . implode(', ', $columns) . ') VALUES (' . implode(', ', $placeholders) . ')';

            $this->db->query($sql);
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':registration_id', $registrationId);
            $this->db->bind(':voter_ip', $voterIp);
            $this->db->bind(':vote_method', 'ip');
            $this->db->bind(':risk_score', $voteData['risk_score']);
            $this->db->bind(':security_flags', $voteData['security_flags']);
            $this->db->bind(':is_flagged', $voteData['is_flagged']);
            $this->db->bind(':is_approved', $voteData['is_approved']);

            // Bind optional parameters
            foreach ($optionalColumns as $col) {
                if (isset($voteData[$col])) {
                    $this->db->bind(":$col", $voteData[$col]);
                }
            }

            $result = $this->db->execute();

            // Create alerts if high risk
            if ($result && $riskAnalysis['score'] >= $settings->auto_flag_threshold) {
                $alertTitle = 'High Risk Vote Detected';
                $alertMessage = "A vote with risk score {$riskAnalysis['score']} was submitted. Flags: " . implode(', ', $riskAnalysis['flags']);
                $securityModel->createAlert($showId, 'high_risk_vote', 'warning', $alertTitle, $alertMessage, [
                    'vote_id' => $this->db->lastInsertId(),
                    'risk_score' => $riskAnalysis['score'],
                    'flags' => $riskAnalysis['flags']
                ]);
            }

            return $result;
        } catch (Exception $e) {
            error_log('Error recording fan vote: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Record a fan vote with Facebook authentication
     * 
     * @param int $showId Show ID
     * @param int $registrationId Registration ID
     * @param string $voterIp Voter's IP address
     * @param string $fbUserId Facebook user ID
     * @param string $fbUserName Facebook user name
     * @param string $fbUserEmail Facebook user email
     * @return bool
     */
    public function recordFanVoteFacebook($showId, $registrationId, $voterIp, $fbUserId, $fbUserName, $fbUserEmail = '', $securityData = []) {
        try {
            // Check if fan_votes table exists
            if (!$this->tableExists('fan_votes')) {
                // Create the table if it doesn't exist
                if (!$this->createFanVotesTable()) {
                    error_log("Failed to create fan_votes table");
                    return false;
                }
            }

            // Initialize security model
            require_once APPROOT . '/models/VotingSecurityModel.php';
            $securityModel = new VotingSecurityModel();

            // Check if this Facebook user has already voted
            $this->db->query('SELECT id FROM fan_votes WHERE show_id = :show_id AND fb_user_id = :fb_user_id');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':fb_user_id', $fbUserId);
            $existingVote = $this->db->single();

            // Prepare vote data with security information
            $voteData = array_merge([
                'show_id' => $showId,
                'registration_id' => $registrationId,
                'voter_ip' => $voterIp,
                'vote_method' => 'facebook',
                'fb_user_id' => $fbUserId,
                'fb_user_name' => $fbUserName,
                'fb_user_email' => $fbUserEmail
            ], $securityData);

            // Calculate risk score and security flags
            $riskAnalysis = $securityModel->calculateRiskScore($voteData, $showId);
            $voteData['risk_score'] = $riskAnalysis['score'];
            $voteData['security_flags'] = json_encode($riskAnalysis['flags']);

            // Get security settings for auto-flagging
            $settings = $securityModel->getSecuritySettings($showId);
            $voteData['is_flagged'] = $riskAnalysis['score'] >= $settings->auto_flag_threshold;
            $voteData['is_approved'] = $riskAnalysis['score'] < $settings->auto_block_threshold;

            if ($existingVote) {
                // Update existing vote with security data
                $updateColumns = [
                    'registration_id = :registration_id',
                    'voter_ip = :voter_ip',
                    'fb_user_name = :fb_user_name',
                    'fb_user_email = :fb_user_email',
                    'risk_score = :risk_score',
                    'security_flags = :security_flags',
                    'is_flagged = :is_flagged',
                    'is_approved = :is_approved',
                    'updated_at = NOW()'
                ];

                // Add optional security columns if present
                $optionalColumns = [
                    'latitude', 'longitude', 'location_accuracy', 'device_fingerprint', 'user_agent',
                    'screen_resolution', 'timezone', 'language', 'referrer', 'session_id',
                    'distance_from_show', 'ip_country', 'ip_region', 'ip_city', 'is_vpn',
                    'is_mobile', 'vote_duration_seconds'
                ];

                foreach ($optionalColumns as $col) {
                    if (isset($voteData[$col])) {
                        $updateColumns[] = "$col = :$col";
                    }
                }

                $sql = 'UPDATE fan_votes SET ' . implode(', ', $updateColumns) . ' WHERE id = :id';

                $this->db->query($sql);
                $this->db->bind(':registration_id', $registrationId);
                $this->db->bind(':voter_ip', $voterIp);
                $this->db->bind(':fb_user_name', $fbUserName);
                $this->db->bind(':fb_user_email', $fbUserEmail);
                $this->db->bind(':risk_score', $voteData['risk_score']);
                $this->db->bind(':security_flags', $voteData['security_flags']);
                $this->db->bind(':is_flagged', $voteData['is_flagged']);
                $this->db->bind(':is_approved', $voteData['is_approved']);
                $this->db->bind(':id', $existingVote->id);

                // Bind optional parameters
                foreach ($optionalColumns as $col) {
                    if (isset($voteData[$col])) {
                        $this->db->bind(":$col", $voteData[$col]);
                    }
                }

                $voteId = $existingVote->id;
            } else {
                // Create new vote with security data
                $columns = ['show_id', 'registration_id', 'voter_ip', 'fb_user_id', 'fb_user_name', 'fb_user_email',
                           'vote_method', 'risk_score', 'security_flags', 'is_flagged', 'is_approved', 'created_at'];
                $placeholders = [':show_id', ':registration_id', ':voter_ip', ':fb_user_id', ':fb_user_name', ':fb_user_email',
                               ':vote_method', ':risk_score', ':security_flags', ':is_flagged', ':is_approved', 'NOW()'];

                // Add optional security columns if present
                $optionalColumns = [
                    'latitude', 'longitude', 'location_accuracy', 'device_fingerprint', 'user_agent',
                    'screen_resolution', 'timezone', 'language', 'referrer', 'session_id',
                    'distance_from_show', 'ip_country', 'ip_region', 'ip_city', 'is_vpn',
                    'is_mobile', 'vote_duration_seconds'
                ];

                foreach ($optionalColumns as $col) {
                    if (isset($voteData[$col])) {
                        $columns[] = $col;
                        $placeholders[] = ":$col";
                    }
                }

                $sql = 'INSERT INTO fan_votes (' . implode(', ', $columns) . ') VALUES (' . implode(', ', $placeholders) . ')';

                $this->db->query($sql);
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':registration_id', $registrationId);
                $this->db->bind(':voter_ip', $voterIp);
                $this->db->bind(':fb_user_id', $fbUserId);
                $this->db->bind(':fb_user_name', $fbUserName);
                $this->db->bind(':fb_user_email', $fbUserEmail);
                $this->db->bind(':vote_method', 'facebook');
                $this->db->bind(':risk_score', $voteData['risk_score']);
                $this->db->bind(':security_flags', $voteData['security_flags']);
                $this->db->bind(':is_flagged', $voteData['is_flagged']);
                $this->db->bind(':is_approved', $voteData['is_approved']);

                // Bind optional parameters
                foreach ($optionalColumns as $col) {
                    if (isset($voteData[$col])) {
                        $this->db->bind(":$col", $voteData[$col]);
                    }
                }

                $voteId = null; // Will be set after execute
            }

            $result = $this->db->execute();

            if ($result && !$existingVote) {
                $voteId = $this->db->lastInsertId();
            }

            // Create alerts if high risk
            if ($result && $riskAnalysis['score'] >= $settings->auto_flag_threshold) {
                $alertTitle = 'High Risk Facebook Vote Detected';
                $alertMessage = "A Facebook vote with risk score {$riskAnalysis['score']} was submitted. Flags: " . implode(', ', $riskAnalysis['flags']);
                $securityModel->createAlert($showId, 'high_risk_vote', 'warning', $alertTitle, $alertMessage, [
                    'vote_id' => $voteId,
                    'risk_score' => $riskAnalysis['score'],
                    'flags' => $riskAnalysis['flags'],
                    'facebook_user' => $fbUserName
                ]);
            }

            return $result;
        } catch (Exception $e) {
            error_log('Error recording Facebook fan vote: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Record a manual fan vote by staff/coordinator/admin
     *
     * @param int $showId Show ID
     * @param int $registrationId Registration ID
     * @param string $voterName Name of the fan
     * @param string $voterContact Phone or email of the fan
     * @param int $enteredBy User ID of staff member entering the vote
     * @param string $enteredByRole Role of person entering (staff, coordinator, admin)
     * @param string $notes Optional notes about the manual entry
     * @return bool
     */
    public function recordManualFanVote($showId, $registrationId, $voterName, $voterContact, $enteredBy, $enteredByRole, $notes = '') {
        try {
            // Check if fan_votes table exists
            if (!$this->tableExists('fan_votes')) {
                // Create the table if it doesn't exist
                if (!$this->createFanVotesTable()) {
                    error_log("Failed to create fan_votes table");
                    return false;
                }
            }

            // Initialize security model
            require_once APPROOT . '/models/VotingSecurityModel.php';
            $securityModel = new VotingSecurityModel();

            // Get the IP address of the person entering the vote
            $voterIp = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

            // Prepare vote data
            $voteData = [
                'show_id' => $showId,
                'registration_id' => $registrationId,
                'voter_ip' => $voterIp,
                'vote_method' => 'manual_' . $enteredByRole,
                'manual_voter_name' => $voterName,
                'manual_voter_contact' => $voterContact,
                'manual_entered_by' => $enteredBy,
                'risk_score' => 0, // Manual votes start with 0 risk
                'security_flags' => json_encode(['manual_entry']),
                'is_flagged' => false,
                'is_approved' => true, // Manual votes are pre-approved
                'review_notes' => $notes
            ];

            // Insert the manual vote
            $this->db->query('INSERT INTO fan_votes
                             (show_id, registration_id, voter_ip, vote_method, manual_voter_name,
                              manual_voter_contact, manual_entered_by, risk_score, security_flags,
                              is_flagged, is_approved, review_notes, created_at)
                             VALUES
                             (:show_id, :registration_id, :voter_ip, :vote_method, :manual_voter_name,
                              :manual_voter_contact, :manual_entered_by, :risk_score, :security_flags,
                              :is_flagged, :is_approved, :review_notes, NOW())');

            $this->db->bind(':show_id', $showId);
            $this->db->bind(':registration_id', $registrationId);
            $this->db->bind(':voter_ip', $voterIp);
            $this->db->bind(':vote_method', 'manual_' . $enteredByRole);
            $this->db->bind(':manual_voter_name', $voterName);
            $this->db->bind(':manual_voter_contact', $voterContact);
            $this->db->bind(':manual_entered_by', $enteredBy);
            $this->db->bind(':risk_score', 0);
            $this->db->bind(':security_flags', json_encode(['manual_entry']));
            $this->db->bind(':is_flagged', false);
            $this->db->bind(':is_approved', true);
            $this->db->bind(':review_notes', $notes);

            $result = $this->db->execute();

            if ($result) {
                // Create audit log entry
                $voteId = $this->db->lastInsertId();
                $securityModel->createAlert($showId, 'manual_review_needed', 'info',
                    'Manual Vote Entered',
                    "Manual vote entered by $enteredByRole for voter: $voterName",
                    [
                        'vote_id' => $voteId,
                        'entered_by' => $enteredBy,
                        'entered_by_role' => $enteredByRole,
                        'voter_name' => $voterName,
                        'voter_contact' => $voterContact
                    ]
                );
            }

            return $result;
        } catch (Exception $e) {
            error_log('Error recording manual fan vote: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Count fan votes for a registration
     * 
     * @param int $showId Show ID
     * @param int $registrationId Registration ID
     * @return int
     */
    public function countFanVotes($showId, $registrationId) {
        // Check if fan_votes table exists
        if (!$this->tableExists('fan_votes')) {
            // Create the table if it doesn't exist
            if (!$this->createFanVotesTable()) {
                error_log("Failed to create fan_votes table");
                return 0;
            }
            // If we just created the table, there can't be any votes yet
            return 0;
        }
        
        $this->db->query('SELECT COUNT(*) as vote_count FROM fan_votes 
                          WHERE show_id = :show_id AND registration_id = :registration_id');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':registration_id', $registrationId);
        $result = $this->db->single();
        
        return $result ? $result->vote_count : 0;
    }
    
    /**
     * Get fan vote rankings for a show
     * 
     * @param int $showId Show ID
     * @param int $limit Optional limit
     * @return array
     */
    public function getFanVoteRankings($showId, $limit = null) {
        $sql = 'SELECT r.id as registration_id, r.display_number, r.registration_number, 
                v.make, v.model, v.year, v.color, 
                u.name as owner_name, sc.name as category_name, 
                COUNT(fv.id) as vote_count,
                (SELECT file_path FROM images WHERE entity_type = "vehicle" AND entity_id = v.id AND is_primary = 1 LIMIT 1) as primary_image
                FROM registrations r 
                JOIN vehicles v ON r.vehicle_id = v.id 
                JOIN users u ON v.owner_id = u.id 
                JOIN show_categories sc ON r.category_id = sc.id 
                LEFT JOIN fan_votes fv ON r.id = fv.registration_id 
                WHERE r.show_id = :show_id AND r.status = :status 
                GROUP BY r.id, r.display_number, r.registration_number, v.make, v.model, v.year, v.color, u.name, sc.name 
                ORDER BY vote_count DESC';
        
        if ($limit) {
            $sql .= ' LIMIT :limit';
        }
        
        $this->db->query($sql);
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':status', 'approved');
        
        if ($limit) {
            $this->db->bind(':limit', $limit, PDO::PARAM_INT);
        }
        
        return $this->db->resultSet();
    }
    
    /**
     * Get all votes for a show with voter details
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getShowVotes($showId) {
        // Check if fan_votes table exists
        if (!$this->tableExists('fan_votes')) {
            // Create the table if it doesn't exist
            if (!$this->createFanVotesTable()) {
                error_log("Failed to create fan_votes table");
                return [];
            }
            // If we just created the table, there can't be any votes yet
            return [];
        }
        
        $this->db->query('SELECT fv.id, fv.registration_id, fv.voter_ip, fv.fb_user_id, fv.fb_user_name, fv.fb_user_email, 
                          fv.created_at, r.registration_number, v.year, v.make, v.model, v.color, 
                          u.name as owner_name, sc.name as category_name
                          FROM fan_votes fv
                          JOIN registrations r ON fv.registration_id = r.id
                          JOIN vehicles v ON r.vehicle_id = v.id
                          JOIN users u ON v.owner_id = u.id
                          JOIN show_categories sc ON r.category_id = sc.id
                          WHERE fv.show_id = :show_id
                          ORDER BY fv.created_at DESC');
        $this->db->bind(':show_id', $showId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Check if judging is complete for a show
     * 
     * @param int $showId Show ID
     * @return bool
     */
    public function isShowJudgingComplete($showId) {
        // Get count of all approved registrations
        $this->db->query('SELECT COUNT(*) as reg_count FROM registrations 
                          WHERE show_id = :show_id AND status = :status');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':status', 'approved');
        $regCount = $this->db->single()->reg_count;
        
        if ($regCount == 0) {
            return false;
        }
        
        // Get count of judging metrics
        $this->db->query('SELECT COUNT(*) as metric_count FROM judging_metrics WHERE show_id = :show_id');
        $this->db->bind(':show_id', $showId);
        $metricCount = $this->db->single()->metric_count;
        
        if ($metricCount == 0) {
            return false;
        }
        
        // Get count of judge assignments
        $this->db->query('SELECT COUNT(DISTINCT judge_id) as judge_count FROM judge_assignments WHERE show_id = :show_id');
        $this->db->bind(':show_id', $showId);
        $judgeCount = $this->db->single()->judge_count;
        
        if ($judgeCount == 0) {
            return false;
        }
        
        // Get count of finalized scores
        $this->db->query('SELECT COUNT(*) as score_count FROM scores s 
                          JOIN registrations r ON s.registration_id = r.id 
                          WHERE r.show_id = :show_id AND s.is_draft = 0');
        $this->db->bind(':show_id', $showId);
        $scoreCount = $this->db->single()->score_count;
        
        // Expected number of scores: registrations * metrics * judges
        $expectedScores = $regCount * $metricCount * $judgeCount;
        
        return $scoreCount >= $expectedScores;
    }
    
    /**
     * Get judging progress for a show
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getJudgingProgress($showId) {
        $this->db->query('SELECT u.id as judge_id, u.name as judge_name, 
                          COUNT(DISTINCT r.id) as total_vehicles, 
                          COUNT(DISTINCT CASE WHEN EXISTS (
                              SELECT 1 FROM scores s2 
                              WHERE s2.registration_id = r.id AND s2.judge_id = u.id AND s2.is_draft = 0
                          ) THEN r.id ELSE NULL END) as judged_vehicles 
                          FROM users u 
                          JOIN judge_assignments ja ON u.id = ja.judge_id 
                          JOIN registrations r ON ja.show_id = r.show_id AND (ja.category_id = r.category_id OR ja.category_id IS NULL) 
                          WHERE ja.show_id = :show_id AND r.status = :status 
                          GROUP BY u.id, u.name');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':status', 'approved');
        
        return $this->db->resultSet();
    }
    
    /**
     * Get assigned categories for a judge
     * 
     * @param int $judgeId Judge ID
     * @return array Array of category IDs
     */
    public function getJudgeAssignedCategories($judgeId) {
        try {
            // Check if the judge_categories table exists
            if (!$this->tableExists('judge_categories')) {
                return [];
            }
            
            // Get assigned categories for this judge
            $this->db->query('SELECT category_id FROM judge_categories WHERE judge_id = :judge_id');
            $this->db->bind(':judge_id', $judgeId);
            $results = $this->db->resultSet();
            
            // Extract category IDs
            $categoryIds = [];
            foreach ($results as $result) {
                $categoryIds[] = $result->category_id;
            }
            
            return $categoryIds;
        } catch (Exception $e) {
            error_log('Error getting judge assigned categories: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update show judges
     * 
     * @param int $showId Show ID
     * @param array $judgeIds Array of user IDs to be judges
     * @param array $judgeCategories Array of category assignments [judge_id => [category_ids]]
     * @return bool True if successful, false otherwise
     */
    public function updateShowJudges($showId, $judgeIds, $judgeCategories) {
        try {
            // Check if the judges table exists
            if (!$this->tableExists('judges')) {
                // Create the table if it doesn't exist
                $this->createJudgesTable();
            } else {
                // Ensure the is_active column exists
                $this->ensureColumnExists('judges', 'is_active', 'TINYINT(1) NOT NULL DEFAULT 1');
            }
            
            // Check if judge_assignments table exists
            if (!$this->tableExists('judge_assignments')) {
                // Create the table if it doesn't exist
                $this->db->query("CREATE TABLE IF NOT EXISTS `judge_assignments` (
                    `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
                    `show_id` int UNSIGNED NOT NULL,
                    `judge_id` int UNSIGNED NOT NULL,
                    `category_id` int UNSIGNED NULL,
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;");
                $this->db->execute();
            }
            
            // Start transaction
            $this->db->beginTransaction();
            
            // First, set all existing judges for this show to inactive
            $this->db->query('UPDATE judges SET is_active = 0 WHERE show_id = :show_id');
            $this->db->bind(':show_id', $showId);
            $this->db->execute();
            
            // Remove all judge assignments for this show from judge_assignments table
            $this->db->query('DELETE FROM judge_assignments WHERE show_id = :show_id');
            $this->db->bind(':show_id', $showId);
            $this->db->execute();
            
            // Add or update judges
            foreach ($judgeIds as $userId) {
                // Check if judge already exists in judges table
                $this->db->query('SELECT id FROM judges WHERE show_id = :show_id AND user_id = :user_id');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':user_id', $userId);
                $existingJudge = $this->db->single();
                
                if ($existingJudge) {
                    // Update existing judge
                    $this->db->query('UPDATE judges SET is_active = 1 WHERE id = :id');
                    $this->db->bind(':id', $existingJudge->id);
                    $this->db->execute();
                    $judgeId = $existingJudge->id;
                } else {
                    // Add new judge
                    $this->db->query('INSERT INTO judges (show_id, user_id, is_active) VALUES (:show_id, :user_id, 1)');
                    $this->db->bind(':show_id', $showId);
                    $this->db->bind(':user_id', $userId);
                    $this->db->execute();
                    $judgeId = $this->db->lastInsertId();
                }
                
                // Delete existing category assignments for this judge
                $this->db->query('DELETE FROM judge_categories WHERE judge_id = :judge_id');
                $this->db->bind(':judge_id', $judgeId);
                $this->db->execute();
                
                // Add new category assignments and update judge_assignments table
                if (isset($judgeCategories[$userId]) && is_array($judgeCategories[$userId]) && !empty($judgeCategories[$userId])) {
                    foreach ($judgeCategories[$userId] as $categoryId) {
                        // Add to judge_categories table
                        $this->db->query('INSERT INTO judge_categories (judge_id, category_id) VALUES (:judge_id, :category_id)');
                        $this->db->bind(':judge_id', $judgeId);
                        $this->db->bind(':category_id', $categoryId);
                        $this->db->execute();
                        
                        // Add to judge_assignments table with category
                        $this->db->query('INSERT INTO judge_assignments (show_id, judge_id, category_id, created_at, updated_at) 
                                         VALUES (:show_id, :judge_id, :category_id, NOW(), NOW())');
                        $this->db->bind(':show_id', $showId);
                        $this->db->bind(':judge_id', $userId);
                        $this->db->bind(':category_id', $categoryId);
                        $this->db->execute();
                    }
                } else {
                    // Add to judge_assignments table without specific category
                    $this->db->query('INSERT INTO judge_assignments (show_id, judge_id, created_at, updated_at) 
                                     VALUES (:show_id, :judge_id, NOW(), NOW())');
                    $this->db->bind(':show_id', $showId);
                    $this->db->bind(':judge_id', $userId);
                    $this->db->execute();
                }
            }
            
            // Commit transaction
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollBack();
            error_log('Error updating show judges: ' . $e->getMessage());
            return false;
        }
    }
}
<?php
/**
 * Staff Controller
 * 
 * This controller handles all staff-related functionality.
 */
class StaffController extends Controller {
    private $showModel;
    private $userModel;
    private $vehicleModel;
    private $registrationModel;
    private $paymentModel;
    private $staffModel;
    private $auth;
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in and is a staff member
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole(['staff', 'coordinator', 'admin'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $this->showModel = $this->model('ShowModel');
        $this->userModel = $this->model('UserModel');
        $this->vehicleModel = $this->model('VehicleModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->paymentModel = $this->model('PaymentModel');
        $this->staffModel = $this->model('StaffModel');
        $this->db = new Database();
    }
    
    /**
     * Default index method - redirects to dashboard
     */
    public function index() {
        $this->redirect('staff/dashboard');
    }
    
    /**
     * Staff dashboard
     */
    public function dashboard() {
        // Get current user ID
        $userId = $this->auth->getCurrentUserId();

        // Get staff assignment counts and statistics only (for performance)
        $staffCounts = $this->staffModel->getStaffAssignmentCounts($userId);
        $staffStats = $this->staffModel->getStaffStats($userId);

        $data = [
            'title' => 'Staff Dashboard',
            'staff_counts' => $staffCounts,
            'staff_stats' => $staffStats
        ];

        $this->view('staff/dashboard', $data);
    }

    /**
     * AJAX endpoint for loading paginated staff assignments
     *
     * @return void
     */
    public function loadAssignments() {
        // Check if request is AJAX
        if (!isAjaxRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Check if user is logged in
        if (!isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            return;
        }

        // Get current user ID
        $userId = $this->auth->getCurrentUserId();

        // Get parameters
        $page = (int)($_GET['page'] ?? 1);
        $perPage = (int)($_GET['per_page'] ?? 20);
        $search = $_GET['search'] ?? '';
        $statusFilter = $_GET['status_filter'] ?? 'all';
        $dateFilter = $_GET['date_filter'] ?? '';
        $orderBy = $_GET['order_by'] ?? 'start_date';
        $orderDir = $_GET['order_dir'] ?? 'DESC';

        try {
            $result = $this->staffModel->getPaginatedStaffAssignments(
                $userId, $page, $perPage, $search, $statusFilter, $dateFilter, $orderBy, $orderDir
            );

            echo json_encode([
                'success' => true,
                'assignments' => $result['assignments'],
                'pagination' => $result['pagination']
            ]);
        } catch (Exception $e) {
            error_log('Error in StaffController::loadAssignments: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load assignments']);
        }
    }
    
    /**
     * Show details
     * 
     * @param int $id Show ID
     */
    public function show($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to view this show
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $id);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get categories
        $categories = $this->showModel->getShowCategories($id);
        
        // Get registration counts
        $registrationCounts = $this->registrationModel->countRegistrationsByCategory($id);
        
        $data = [
            'title' => $show->name,
            'show' => $show,
            'categories' => $categories,
            'registrationCounts' => $registrationCounts
        ];
        
        $this->view('staff/show', $data);
    }
    
    /**
     * Show registrations
     * 
     * @param int $showId Show ID
     */
    public function registrations($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to view this show
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $showId);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get registrations
        $registrations = $this->registrationModel->getShowRegistrations($showId);
        
        $data = [
            'title' => $show->name . ' - Registrations',
            'show' => $show,
            'registrations' => $registrations
        ];
        
        $this->view('staff/registrations', $data);
    }
    
    /**
     * View registration details
     * 
     * @param int $id Registration ID
     */
    public function registration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to view this registration
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $registration->show_id);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get vehicle
        $vehicle = $this->vehicleModel->getVehicleById($registration->vehicle_id);
        
        // Get owner
        $owner = $this->userModel->getUserById($vehicle->owner_id);
        
        // Get category
        $category = $this->showModel->getCategoryById($registration->category_id);
        
        $data = [
            'title' => 'Registration Details',
            'registration' => $registration,
            'show' => $show,
            'vehicle' => $vehicle,
            'owner' => $owner,
            'category' => $category
        ];
        
        $this->view('staff/registration', $data);
    }
    
    /**
     * Process check-in for a vehicle
     * 
     * @param int $id Registration ID
     */
    public function checkIn($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to check in for this show
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $registration->show_id);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Process check-in
        if ($this->registrationModel->checkInVehicle($id)) {
            $this->setFlashMessage('staff_message', 'Vehicle checked in successfully', 'success');
        } else {
            $this->setFlashMessage('staff_message', 'Failed to check in vehicle', 'danger');
        }
        
        // Redirect back to registration
        $this->redirect('staff/registration/' . $id);
    }
    
    /**
     * Undo check-in for a vehicle
     * 
     * @param int $id Registration ID
     */
    public function undoCheckIn($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to undo check-in for this show
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $registration->show_id);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Process undo check-in
        if ($this->registrationModel->undoCheckInVehicle($id)) {
            $this->setFlashMessage('staff_message', 'Check-in undone successfully', 'success');
        } else {
            $this->setFlashMessage('staff_message', 'Failed to undo check-in', 'danger');
        }
        
        // Redirect back to registration
        $this->redirect('staff/registration/' . $id);
    }
    
    /**
     * Create a new registration
     * 
     * @param int $showId Show ID
     */
    public function createRegistration($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to create registrations for this show
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $showId);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get registration counts for each category
        $registration_counts = $this->registrationModel->countRegistrationsByCategory($showId);
        
        // Add registration counts to show object
        $show->registration_counts = $registration_counts;
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Init data
        $data = [
            'title' => 'Create Registration',
            'show' => $show,
            'categories' => $categories,
            'payment_methods' => $paymentMethods,
            'current_step' => 'user_selection'
        ];
        
        $this->view('staff/create_registration', $data);
    }
    
    /**
     * Search for users via AJAX
     */
    public function searchUsers() {
        // Set content type to JSON
        header('Content-Type: application/json; charset=UTF-8');
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('StaffController::searchUsers - Method called');
            error_log('StaffController::searchUsers - POST data: ' . json_encode($_POST));
            error_log('StaffController::searchUsers - Is AJAX: ' . ($this->isAjax() ? 'Yes' : 'No'));
        }
        
        // Check if request is AJAX
        if (!$this->isAjax()) {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            echo json_encode(['success' => false, 'message' => 'Invalid security token']);
            return;
        }
        
        // Get search term
        $search = trim($_POST['search'] ?? '');
        $showId = intval($_POST['show_id'] ?? 0);
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('StaffController::searchUsers - Search term: ' . $search);
            error_log('StaffController::searchUsers - Show ID: ' . $showId);
        }
        
        if (empty($search) || strlen($search) < 3) {
            echo json_encode(['success' => false, 'message' => 'Please enter at least 3 characters to search']);
            return;
        }
        
        try {
            // Check if search term is numeric (might be an ID)
            if (is_numeric($search) && defined('DEBUG_MODE') && DEBUG_MODE) {
                $userById = $this->userModel->getUserById($search);
                if ($userById) {
                    error_log('StaffController::searchUsers - Found user by ID: ' . json_encode($userById));
                } else {
                    error_log('StaffController::searchUsers - No user found with ID: ' . $search);
                }
            }
            
            // Search for users
            $users = $this->userModel->searchUsers($search);
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('StaffController::searchUsers - Users found: ' . count($users));
                if (count($users) > 0) {
                    error_log('StaffController::searchUsers - First user: ' . json_encode($users[0]));
                } else {
                    // Get total user count for debugging
                    $allUsers = $this->userModel->getUsers();
                    error_log('StaffController::searchUsers - Total users in system: ' . count($allUsers));
                    
                    // Try a direct query for the exact email
                    if (filter_var($search, FILTER_VALIDATE_EMAIL)) {
                        $userByEmail = $this->userModel->getUserByEmail($search);
                        if ($userByEmail) {
                            error_log('StaffController::searchUsers - Found user by exact email: ' . json_encode($userByEmail));
                            // Add this user to the results
                            $users = [$userByEmail];
                        } else {
                            error_log('StaffController::searchUsers - No user found with exact email: ' . $search);
                        }
                    }
                }
            }
            
            // Convert stdClass objects to arrays for consistent JSON encoding
            $userData = [];
            foreach ($users as $user) {
                $userData[] = [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone ?? '',
                    'role' => $user->role ?? '',
                    'status' => $user->status ?? ''
                ];
            }
            
            // Get total user count for the response
            $totalUsers = 0;
            try {
                $this->db->query('SELECT COUNT(*) as count FROM users');
                $countResult = $this->db->single();
                $totalUsers = $countResult ? $countResult->count : 0;
            } catch (Exception $e) {
                error_log('Error getting total user count: ' . $e->getMessage());
            }
            
            // Add debug info in response if in debug mode
            $response = ['success' => true, 'data' => $userData];
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $response['debug'] = [
                    'search_term' => $search,
                    'show_id' => $showId,
                    'user_count' => count($users),
                    'total_users' => $totalUsers,
                    'timestamp' => gmdate('Y-m-d H:i:s')
                ];
            }
            
            echo json_encode($response);
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('StaffController::searchUsers - Error: ' . $e->getMessage());
                error_log('StaffController::searchUsers - Trace: ' . $e->getTraceAsString());
                echo json_encode([
                    'success' => false, 
                    'message' => 'Error: ' . $e->getMessage(),
                    'debug' => [
                        'error_trace' => $e->getTraceAsString(),
                        'search_term' => $search,
                        'show_id' => $showId
                    ]
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'An error occurred while searching for users']);
            }
        }
    }
    
    /**
     * Select a user for registration
     * 
     * @param int $userId User ID
     * @param int $showId Show ID
     */
    public function selectUser($userId, $showId) {
        // Get user
        $user = $this->userModel->getUserById($userId);
        
        if (!$user) {
            $this->setFlashMessage('staff_message', 'User not found', 'danger');
            $this->redirect('staff/createRegistration/' . $showId);
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get user's vehicles
        $vehicles = $this->vehicleModel->getVehiclesByOwner($userId);
        
        // Get categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get registration counts for each category
        $registration_counts = $this->registrationModel->countRegistrationsByCategory($showId);
        
        // Add registration counts to show object
        $show->registration_counts = $registration_counts;
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Init data
        $data = [
            'title' => 'Create Registration',
            'show' => $show,
            'categories' => $categories,
            'payment_methods' => $paymentMethods,
            'selected_user' => $user,
            'user_vehicles' => $vehicles,
            'current_step' => 'vehicle_selection'
        ];
        
        $this->view('staff/create_registration', $data);
    }
    
    /**
     * Register a new user
     */
    public function registerUser() {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Sanitize POST data
        $_POST = $this->sanitizeInput($_POST);
        
        // Get form data
        $showId = intval($_POST['show_id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $password = trim($_POST['password'] ?? '');
        
        // Validate data
        $errors = [];
        
        if (empty($name)) {
            $errors['name'] = 'Please enter a name';
        }
        
        if (empty($email)) {
            $errors['email'] = 'Please enter an email address';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Please enter a valid email address';
        } elseif ($this->userModel->emailExists($email)) {
            $errors['email'] = 'Email is already registered';
        }
        
        if (empty($password)) {
            $errors['password'] = 'Please enter a password';
        } elseif (strlen($password) < 8) {
            $errors['password'] = 'Password must be at least 8 characters';
        }
        
        // If there are errors, redirect back with errors
        if (!empty($errors)) {
            $this->setFlashMessage('staff_message', 'Please fix the errors in the form', 'danger');
            $this->redirect('staff/createRegistration/' . $showId);
            return;
        }
        
        // Create user using Auth class
        $userId = $this->auth->register(
            $name,
            $email,
            $password, // Auth class will hash the password
            'user',
            $phone
        );
        
        if (!$userId) {
            $this->setFlashMessage('staff_message', 'Failed to register user', 'danger');
            $this->redirect('staff/createRegistration/' . $showId);
            return;
        }
        
        // Get the newly created user
        $user = $this->userModel->getUserById($userId);
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        // Get categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get registration counts for each category
        $registration_counts = $this->registrationModel->countRegistrationsByCategory($showId);
        
        // Add registration counts to show object
        $show->registration_counts = $registration_counts;
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Init data
        $data = [
            'title' => 'Create Registration',
            'show' => $show,
            'categories' => $categories,
            'payment_methods' => $paymentMethods,
            'selected_user' => $user,
            'user_vehicles' => [], // New user has no vehicles
            'current_step' => 'vehicle_selection'
        ];
        
        $this->setFlashMessage('staff_message', 'User registered successfully. Now add a vehicle for this user.', 'success');
        $this->view('staff/create_registration', $data);
    }
    
    /**
     * Add a vehicle for a user
     * 
     * @param int $showId Show ID
     */
    public function addVehicle($showId) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Sanitize POST data
        $_POST = $this->sanitizeInput($_POST);
        
        // Get form data
        $userId = intval($_POST['user_id'] ?? 0);
        $year = intval($_POST['year'] ?? 0);
        $make = trim($_POST['make'] ?? '');
        $model = trim($_POST['model'] ?? '');
        $color = trim($_POST['color'] ?? '');
        $licensePlate = trim($_POST['license_plate'] ?? '');
        $vin = trim($_POST['vin'] ?? '');
        
        // Validate data
        $errors = [];
        
        if (empty($userId)) {
            $errors['user_id'] = 'User ID is required';
        }
        
        if (empty($year) || $year < 1900 || $year > (date('Y') + 1)) {
            $errors['year'] = 'Please enter a valid year';
        }
        
        if (empty($make)) {
            $errors['make'] = 'Please enter the make';
        }
        
        if (empty($model)) {
            $errors['model'] = 'Please enter the model';
        }
        
        if (empty($color)) {
            $errors['color'] = 'Please enter the color';
        }
        
        // If there are errors, redirect back with errors
        if (!empty($errors)) {
            // Get user
            $user = $this->userModel->getUserById($userId);
            
            // Get show
            $show = $this->showModel->getShowById($showId);
            
            // Get user's vehicles
            $vehicles = $this->vehicleModel->getVehiclesByOwner($userId);
            
            // Get categories
            $categories = $this->showModel->getShowCategories($showId);
            
            // Get registration counts for each category
            $registration_counts = $this->registrationModel->countRegistrationsByCategory($showId);
            
            // Add registration counts to show object
            $show->registration_counts = $registration_counts;
            
            // Get payment methods
            $paymentMethods = $this->paymentModel->getPaymentMethods();
            
            // Init data
            $data = [
                'title' => 'Create Registration',
                'show' => $show,
                'categories' => $categories,
                'payment_methods' => $paymentMethods,
                'selected_user' => $user,
                'user_vehicles' => $vehicles,
                'current_step' => 'vehicle_selection',
                'year' => $year,
                'make' => $make,
                'model' => $model,
                'color' => $color,
                'license_plate' => $licensePlate,
                'vin' => $vin
            ];
            
            // Add errors to data
            foreach ($errors as $key => $value) {
                $data[$key . '_err'] = $value;
            }
            
            $this->setFlashMessage('staff_message', 'Please fix the errors in the form', 'danger');
            $this->view('staff/create_registration', $data);
            return;
        }
        
        // Create vehicle
        $vehicleData = [
            'owner_id' => $userId,
            'year' => $year,
            'make' => $make,
            'model' => $model,
            'color' => $color,
            'license_plate' => $licensePlate,
            'vin' => $vin,
            'description' => '' // Required field in createVehicle
        ];
        
        $vehicleId = $this->vehicleModel->createVehicle($vehicleData);
        
        if (!$vehicleId) {
            $this->setFlashMessage('staff_message', 'Failed to add vehicle', 'danger');
            $this->redirect('staff/selectUser/' . $userId . '/' . $showId);
            return;
        }
        
        // Get the newly created vehicle
        $vehicle = $this->vehicleModel->getVehicleById($vehicleId);
        
        // Get user
        $user = $this->userModel->getUserById($userId);
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        // Get categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get registration counts for each category
        $registration_counts = $this->registrationModel->countRegistrationsByCategory($showId);
        
        // Add registration counts to show object
        $show->registration_counts = $registration_counts;
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Init data
        $data = [
            'title' => 'Create Registration',
            'show' => $show,
            'categories' => $categories,
            'payment_methods' => $paymentMethods,
            'selected_user' => $user,
            'selected_vehicle' => $vehicle,
            'current_step' => 'add_more_vehicles'
        ];
        
        $this->setFlashMessage('staff_message', 'Vehicle added successfully', 'success');
        $this->view('staff/create_registration', $data);
    }
    
    /**
     * Select a vehicle for registration
     * 
     * @param int $showId Show ID
     */
    public function selectVehicle($showId) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Sanitize POST data
        $_POST = $this->sanitizeInput($_POST);
        
        // Get form data
        $userId = intval($_POST['user_id'] ?? 0);
        $vehicleId = intval($_POST['vehicle_id'] ?? 0);
        
        // Validate data
        if (empty($userId) || empty($vehicleId)) {
            $this->setFlashMessage('staff_message', 'Invalid user or vehicle', 'danger');
            $this->redirect('staff/createRegistration/' . $showId);
            return;
        }
        
        // Get vehicle
        $vehicle = $this->vehicleModel->getVehicleById($vehicleId);
        
        if (!$vehicle || $vehicle->owner_id != $userId) {
            $this->setFlashMessage('staff_message', 'Invalid vehicle selection', 'danger');
            $this->redirect('staff/selectUser/' . $userId . '/' . $showId);
            return;
        }
        
        // Get user
        $user = $this->userModel->getUserById($userId);
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        // Get categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get registration counts for each category
        $registration_counts = $this->registrationModel->countRegistrationsByCategory($showId);
        
        // Add registration counts to show object
        $show->registration_counts = $registration_counts;
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Init data
        $data = [
            'title' => 'Create Registration',
            'show' => $show,
            'categories' => $categories,
            'payment_methods' => $paymentMethods,
            'selected_user' => $user,
            'selected_vehicle' => $vehicle,
            'current_step' => 'add_more_vehicles'
        ];
        
        $this->view('staff/create_registration', $data);
    }
    
    /**
     * Add more vehicles for a user
     * 
     * @param int $showId Show ID
     * @param int $userId User ID
     */
    public function addMoreVehicles($showId, $userId) {
        // Get user
        $user = $this->userModel->getUserById($userId);
        
        if (!$user) {
            $this->setFlashMessage('staff_message', 'User not found', 'danger');
            $this->redirect('staff/createRegistration/' . $showId);
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get user's vehicles
        $vehicles = $this->vehicleModel->getVehiclesByOwner($userId);
        
        // Get categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get registration counts for each category
        $registration_counts = $this->registrationModel->countRegistrationsByCategory($showId);
        
        // Add registration counts to show object
        $show->registration_counts = $registration_counts;
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Init data
        $data = [
            'title' => 'Create Registration',
            'show' => $show,
            'categories' => $categories,
            'payment_methods' => $paymentMethods,
            'selected_user' => $user,
            'user_vehicles' => $vehicles,
            'current_step' => 'vehicle_selection'
        ];
        
        $this->view('staff/create_registration', $data);
    }
    
    /**
     * Proceed to registration form
     * 
     * @param int $showId Show ID
     * @param int $userId User ID
     */
    public function proceedToRegistration($showId, $userId) {
        // Get user
        $user = $this->userModel->getUserById($userId);
        
        if (!$user) {
            $this->setFlashMessage('staff_message', 'User not found', 'danger');
            $this->redirect('staff/createRegistration/' . $showId);
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get user's vehicles
        $vehicles = $this->vehicleModel->getUserVehicles($userId);
        
        if (empty($vehicles)) {
            $this->setFlashMessage('staff_message', 'User has no vehicles. Please add a vehicle first.', 'warning');
            $this->redirect('staff/addMoreVehicles/' . $showId . '/' . $userId);
            return;
        }
        
        // Get categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get registration counts for each category
        $registration_counts = $this->registrationModel->countRegistrationsByCategory($showId);
        
        // Add registration counts to show object
        $show->registration_counts = $registration_counts;
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Get template for this show's registrations
        // First check if there's a show-specific template
        require_once APPROOT . '/models/EntityTemplateManager.php';
        require_once APPROOT . '/models/DefaultTemplateManager.php';
        $entityTemplateManager = new EntityTemplateManager();
        $defaultTemplateManager = new DefaultTemplateManager();
        $formDesignerModel = $this->model('FormDesignerModel');
        
        $showTemplate = $entityTemplateManager->getEntityTemplateId('show', $showId);
        
        if ($showTemplate) {
            $template = $formDesignerModel->getFormTemplateById($showTemplate);
            $templateSource = 'show-specific';
        } else {
            // If no show-specific template, check for default registration template
            $defaultTemplate = $defaultTemplateManager->getDefaultTemplate('registration');
            
            if ($defaultTemplate) {
                $template = $defaultTemplate;
                $templateSource = 'default';
            } else {
                // If no default template, use a system default template
                
                // Try to get a system default template for registrations
                $systemTemplate = $formDesignerModel->getFormTemplateByTypeAndEntity('show', 0);
                
                if ($systemTemplate) {
                    $template = $systemTemplate;
                    $templateSource = 'system';
                } else {
                    $template = null;
                    $templateSource = 'none';
                }
            }
        }
        
        // If we have a template, check if it has the required fields
        if ($template && isset($template->fields)) {
            $fields = json_decode($template->fields, true);
            $requiredFields = ['vehicle_id', 'category_id'];
            $missingFields = [];
            
            if (is_array($fields)) {
                // Check for each required field
                foreach ($requiredFields as $requiredField) {
                    $fieldFound = false;
                    foreach ($fields as $field) {
                        if (isset($field['id']) && $field['id'] === $requiredField) {
                            $fieldFound = true;
                            break;
                        }
                    }
                    
                    if (!$fieldFound) {
                        $missingFields[] = $requiredField;
                    }
                }
            }
            
            if (!empty($missingFields)) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("StaffController::proceedToRegistration - Template is missing required fields: " . implode(', ', $missingFields));
                }
                
                // If the template is missing critical fields, we should add them
                if (!is_array($fields)) {
                    $fields = [];
                }
                
                // Add missing fields to the template
                foreach ($missingFields as $missingField) {
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("StaffController::proceedToRegistration - Adding missing field to template: {$missingField}");
                    }
                    
                    switch ($missingField) {
                        case 'vehicle_id':
                            $fields[] = [
                                'id' => 'vehicle_id',
                                'type' => 'select',
                                'label' => 'Select Vehicle',
                                'required' => true,
                                'options' => 'vehicles',
                                'width' => 'col-12',
                                'placeholder' => 'Choose a vehicle'
                            ];
                            break;
                            
                        case 'category_id':
                            $fields[] = [
                                'id' => 'category_id',
                                'type' => 'select',
                                'label' => 'Vehicle Category',
                                'required' => true,
                                'options' => 'categories',
                                'width' => 'col-12',
                                'placeholder' => 'Select category'
                            ];
                            break;
                    }
                }
                
                // Update the template with the added fields
                $template->fields = json_encode($fields);
            }
        }
        
        // Init data
        $data = [
            'title' => 'Create Registration',
            'show' => $show,
            'categories' => $categories,
            'payment_methods' => $paymentMethods,
            'selected_user' => $user,
            'user_vehicles' => $vehicles,
            'template' => $template,
            'template_source' => $templateSource,
            'current_step' => 'registration_form'
        ];
        
        $this->view('staff/create_registration', $data);
    }
    
    /**
     * Submit registration
     * 
     * @param int $showId Show ID
     */
    public function submitRegistration($showId) {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Sanitize POST data
        $_POST = $this->sanitizeInput($_POST);
        
        // Get form data
        $userId = intval($_POST['user_id'] ?? 0);
        $vehicleId = trim($_POST['vehicle_id'] ?? '');
        $categoryId = trim($_POST['category_id'] ?? '');
        $paymentMethodId = trim($_POST['payment_method_id'] ?? '');
        $notes = trim($_POST['notes'] ?? '');
        
        // Get user
        $user = $this->userModel->getUserById($userId);
        
        if (!$user) {
            $this->setFlashMessage('staff_message', 'User not found', 'danger');
            $this->redirect('staff/createRegistration/' . $showId);
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get user's vehicles
        $vehicles = $this->vehicleModel->getVehiclesByOwner($userId);
        
        // Get categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get registration counts for each category
        $registration_counts = $this->registrationModel->countRegistrationsByCategory($showId);
        
        // Add registration counts to show object
        $show->registration_counts = $registration_counts;
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Get template for this show's registrations
        require_once APPROOT . '/models/EntityTemplateManager.php';
        require_once APPROOT . '/models/DefaultTemplateManager.php';
        $entityTemplateManager = new EntityTemplateManager();
        $defaultTemplateManager = new DefaultTemplateManager();
        $formDesignerModel = $this->model('FormDesignerModel');
        
        $showTemplate = $entityTemplateManager->getEntityTemplateId('show', $showId);
        
        if ($showTemplate) {
            $template = $formDesignerModel->getFormTemplateById($showTemplate);
            $templateSource = 'show-specific';
        } else {
            // If no show-specific template, check for default registration template
            $defaultTemplate = $defaultTemplateManager->getDefaultTemplate('registration');
            
            if ($defaultTemplate) {
                $template = $defaultTemplate;
                $templateSource = 'default';
            } else {
                // If no default template, use a system default template
                $systemTemplate = $formDesignerModel->getFormTemplateByTypeAndEntity('show', 0);
                
                if ($systemTemplate) {
                    $template = $systemTemplate;
                    $templateSource = 'system';
                } else {
                    $template = null;
                    $templateSource = 'none';
                }
            }
        }
        
        // Validate data
        $errors = [];
        
        if (empty($vehicleId)) {
            $errors['vehicle_id'] = 'Please select a vehicle';
        }
        
        if (empty($categoryId)) {
            $errors['category_id'] = 'Please select a category';
        }
        
        // If there are errors, redirect back with errors
        if (!empty($errors)) {
            // Init data
            $data = [
                'title' => 'Create Registration',
                'show' => $show,
                'categories' => $categories,
                'payment_methods' => $paymentMethods,
                'selected_user' => $user,
                'user_vehicles' => $vehicles,
                'template' => $template,
                'template_source' => $templateSource,
                'current_step' => 'registration_form',
                'vehicle_id' => $vehicleId,
                'category_id' => $categoryId,
                'payment_method_id' => $paymentMethodId,
                'notes' => $notes,
                'errors' => $errors
            ];
            
            $this->setFlashMessage('staff_message', 'Please fix the errors in the form', 'danger');
            $this->view('staff/create_registration', $data);
            return;
        }
        
        // Get category fee
        $fee = 0;
        foreach ($categories as $cat) {
            if ($cat->id == $categoryId) {
                $fee = $cat->registration_fee;
                break;
            }
        }
        
        // Check if show is free
        $isFreeShow = isset($show->is_free) && $show->is_free == 1;
        
        // Prepare registration data
        $registrationData = [
            'show_id' => $showId,
            'user_id' => $userId,
            'vehicle_id' => $vehicleId,
            'category_id' => $categoryId,
            'notes' => $notes,
            'fee' => $fee,
            'status' => 'pending'
        ];
        
        // If show is free, set fee to 0 and payment status to 'free'
        if ($isFreeShow) {
            $registrationData['fee'] = 0;
            $registrationData['payment_status'] = 'free';
            $registrationData['status'] = 'approved'; // Auto-approve free registrations
        } else {
            $registrationData['payment_status'] = 'pending';
            $registrationData['payment_method_id'] = $paymentMethodId;
        }
        
        // Create registration
        $registrationId = $this->registrationModel->createRegistration($registrationData);
        
        if (!$registrationId) {
            $this->setFlashMessage('staff_message', 'Failed to create registration', 'danger');
            
            // Init data
            $data = [
                'title' => 'Create Registration',
                'show' => $show,
                'categories' => $categories,
                'payment_methods' => $paymentMethods,
                'selected_user' => $user,
                'user_vehicles' => $vehicles,
                'template' => $template,
                'template_source' => $templateSource,
                'current_step' => 'registration_form',
                'vehicle_id' => $vehicleId,
                'category_id' => $categoryId,
                'payment_method_id' => $paymentMethodId,
                'notes' => $notes
            ];
            
            $this->view('staff/create_registration', $data);
            return;
        }
        
        $this->setFlashMessage('staff_message', 'Registration created successfully', 'success');
        $this->redirect('staff/registration/' . $registrationId);
    }
    
    /**
     * Edit a registration
     * 
     * @param int $id Registration ID
     */
    public function editRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to edit this registration
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $registration->show_id);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get categories
        $categories = $this->showModel->getShowCategories($registration->show_id);
        
        // Get vehicles (all vehicles for admin/coordinator/staff, only user's vehicles for regular users)
        if ($this->auth->hasRole(['admin', 'coordinator', 'staff'])) {
            $vehicles = $this->vehicleModel->getAllVehicles();
        } else {
            $vehicles = $this->vehicleModel->getVehiclesByOwner($userId);
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $id,
                'show_id' => $registration->show_id,
                'vehicle_id' => trim($_POST['vehicle_id']),
                'category_id' => trim($_POST['category_id']),
                'notes' => trim($_POST['notes']),
                'status' => trim($_POST['status']),
                'vehicle_id_err' => '',
                'category_id_err' => '',
                'status_err' => '',
                'title' => 'Edit Registration',
                'show' => $show,
                'categories' => $categories,
                'vehicles' => $vehicles,
                'registration' => $registration
            ];
            
            // Validate vehicle
            if (empty($data['vehicle_id'])) {
                $data['vehicle_id_err'] = 'Please select a vehicle';
            }
            
            // Validate category
            if (empty($data['category_id'])) {
                $data['category_id_err'] = 'Please select a category';
            }
            
            // Validate status
            if (empty($data['status'])) {
                $data['status_err'] = 'Please select a status';
            }
            
            // Check for errors
            if (empty($data['vehicle_id_err']) && empty($data['category_id_err']) && empty($data['status_err'])) {
                // Update registration
                if ($this->registrationModel->updateRegistration($data)) {
                    $this->setFlashMessage('staff_message', 'Registration updated successfully', 'success');
                    $this->redirect('staff/registration/' . $id);
                } else {
                    $this->setFlashMessage('staff_message', 'Failed to update registration', 'danger');
                    $this->view('staff/edit_registration', $data);
                }
            } else {
                // Load view with errors
                $this->view('staff/edit_registration', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $id,
                'show_id' => $registration->show_id,
                'vehicle_id' => $registration->vehicle_id,
                'category_id' => $registration->category_id,
                'notes' => $registration->notes,
                'status' => $registration->status,
                'vehicle_id_err' => '',
                'category_id_err' => '',
                'status_err' => '',
                'title' => 'Edit Registration',
                'show' => $show,
                'categories' => $categories,
                'vehicles' => $vehicles,
                'registration' => $registration
            ];
            
            $this->view('staff/edit_registration', $data);
        }
    }
    
    /**
     * Process manual payment for a registration
     * 
     * @param int $id Registration ID
     */
    public function processPayment($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to process payments for this show
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $registration->show_id);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'registration_id' => $id,
                'user_id' => $registration->owner_id,
                'payment_method_id' => trim($_POST['payment_method_id']),
                'payment_reference' => trim($_POST['reference']),
                'notes' => trim($_POST['notes']),
                'amount' => $show->registration_fee,
                'processed_by' => $userId,
                'payment_type' => 'registration',
                'related_id' => $id,
                'payment_method_id_err' => '',
                'title' => 'Process Payment',
                'show' => $show,
                'registration' => $registration,
                'payment_methods' => $paymentMethods
            ];
            
            // Validate payment method
            if (empty($data['payment_method_id'])) {
                $data['payment_method_id_err'] = 'Please select a payment method';
            }
            
            // Check for errors
            if (empty($data['payment_method_id_err'])) {
                // Process payment
                if ($this->paymentModel->processManualPayment($data)) {
                    // Update registration status to paid
                    $this->registrationModel->updatePayment([
                        'id' => $id,
                        'payment_status' => 'paid',
                        'fee' => $data['amount'],
                        'payment_method_id' => $data['payment_method_id'],
                        'payment_reference' => $data['payment_reference'] ?? null
                    ]);
                    
                    $this->setFlashMessage('staff_message', 'Payment processed successfully', 'success');
                    $this->redirect('staff/registration/' . $id);
                } else {
                    $this->setFlashMessage('staff_message', 'Failed to process payment', 'danger');
                    $this->view('staff/process_payment', $data);
                }
            } else {
                // Load view with errors
                $this->view('staff/process_payment', $data);
            }
        } else {
            // Init data
            $data = [
                'registration_id' => $id,
                'payment_method_id' => '',
                'reference' => '',
                'notes' => '',
                'amount' => $show->registration_fee,
                'processed_by' => $userId,
                'payment_method_id_err' => '',
                'title' => 'Process Payment',
                'show' => $show,
                'registration' => $registration,
                'payment_methods' => $paymentMethods
            ];
            
            $this->view('staff/process_payment', $data);
        }
    }
    
    /**
     * Print registration card
     * 
     * @param int $id Registration ID
     */
    public function printRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to print for this show
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $registration->show_id);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Redirect to the registration controller's printRegistration method
        $this->redirect('registration/printRegistration/' . $id);
    }

    /**
     * Staff voting security dashboard for assigned shows
     */
    public function votingSecurityDashboard($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);

        if (!$show) {
            $this->redirect('staff/dashboard');
            return;
        }

        // Check if staff is assigned to this show
        if (!$this->staffModel->isAssignedToShow($_SESSION['user_id'], $showId)) {
            $this->redirect('staff/dashboard');
            return;
        }

        // Initialize security model
        require_once APPROOT . '/models/VotingSecurityModel.php';
        $securityModel = new VotingSecurityModel();

        // Get pagination parameters
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(10, intval($_GET['limit']))) : 25;
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        $status = isset($_GET['status']) ? $_GET['status'] : 'all';
        $riskLevel = isset($_GET['risk']) ? $_GET['risk'] : 'all';

        // Get voting statistics (optimized)
        $stats = $securityModel->getVotingStats($showId);

        // Get paginated votes with filters
        $filters = [
            'show_id' => $showId,
            'search' => $search,
            'status' => $status,
            'risk_level' => $riskLevel
        ];

        $votesData = $securityModel->getPaginatedVotes($page, $limit, $filters);

        // Get flagged votes count (for priority display)
        $flaggedCount = $securityModel->getFlaggedVotesCount($showId);

        // Get vehicle count for manual vote dropdown (don't load all)
        $this->db->query('SELECT COUNT(*) as count FROM registrations WHERE show_id = :show_id');
        $this->db->bind(':show_id', $showId);
        $vehicleCount = $this->db->single()->count;

        $data = [
            'title' => 'Voting Security Dashboard - ' . $show->name,
            'show' => $show,
            'stats' => $stats,
            'votes' => $votesData['votes'],
            'total_votes' => $votesData['total'],
            'current_page' => $page,
            'total_pages' => ceil($votesData['total'] / $limit),
            'limit' => $limit,
            'flagged_count' => $flaggedCount,
            'vehicle_count' => $vehicleCount,
            'filters' => $filters
        ];

        $this->view('staff/voting_security_dashboard', $data);
    }

    /**
     * AJAX endpoint for searching vehicles - optimized for large datasets
     */
    public function searchVehicles($showId) {
        // Check access
        $show = $this->showModel->getShowById($showId);
        if (!$show || !$this->staffModel->isAssignedToShow($_SESSION['user_id'], $showId)) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        $query = isset($_GET['q']) ? trim($_GET['q']) : '';
        $limit = 20; // Limit results for performance

        try {
            if (empty($query)) {
                // Return recent registrations if no query
                $this->db->query('SELECT r.id, r.registration_number,
                                 CONCAT(v.year, " ", v.make, " ", v.model) as vehicle_info,
                                 COUNT(fv.id) as vote_count
                                 FROM registrations r
                                 LEFT JOIN vehicles v ON r.vehicle_id = v.id
                                 LEFT JOIN fan_votes fv ON r.id = fv.registration_id
                                 WHERE r.show_id = :show_id
                                 GROUP BY r.id
                                 ORDER BY r.created_at DESC
                                 LIMIT :limit');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':limit', $limit);
            } else {
                // Search vehicles by registration number, make, model, year
                $searchTerm = '%' . $query . '%';
                $this->db->query('SELECT r.id, r.registration_number,
                                 CONCAT(v.year, " ", v.make, " ", v.model) as vehicle_info,
                                 COUNT(fv.id) as vote_count
                                 FROM registrations r
                                 LEFT JOIN vehicles v ON r.vehicle_id = v.id
                                 LEFT JOIN fan_votes fv ON r.id = fv.registration_id
                                 WHERE r.show_id = :show_id
                                    AND (r.registration_number LIKE :search
                                         OR v.make LIKE :search2
                                         OR v.model LIKE :search3
                                         OR v.year LIKE :search4)
                                 GROUP BY r.id
                                 ORDER BY r.created_at DESC
                                 LIMIT :limit');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':search', $searchTerm);
                $this->db->bind(':search2', $searchTerm);
                $this->db->bind(':search3', $searchTerm);
                $this->db->bind(':search4', $searchTerm);
                $this->db->bind(':limit', $limit);
            }

            $vehicles = $this->db->resultSet();

            header('Content-Type: application/json');
            echo json_encode(['vehicles' => $vehicles]);

        } catch (Exception $e) {
            error_log('Error searching vehicles: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Search failed']);
        }
    }

    /**
     * Add manual vote (staff)
     */
    public function addManualVote($showId) {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('staff/votingSecurityDashboard/' . $showId);
            return;
        }

        // Verify CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->setFlashMessage('staff_message', 'Invalid request. Please try again.', 'danger');
            $this->redirect('staff/votingSecurityDashboard/' . $showId);
            return;
        }

        // Get show and verify access
        $show = $this->showModel->getShowById($showId);
        if (!$show || !$this->staffModel->isAssignedToShow($_SESSION['user_id'], $showId)) {
            $this->redirect('staff/dashboard');
            return;
        }

        // Sanitize input
        $_POST = $this->sanitizeInput($_POST);

        $registrationId = intval($_POST['registration_id']);
        $voterName = trim($_POST['voter_name']);
        $voterContact = trim($_POST['voter_contact']);
        $notes = trim($_POST['notes']);

        // Validate input
        if (empty($registrationId) || empty($voterName) || empty($voterContact)) {
            $this->setFlashMessage('staff_message', 'All fields are required.', 'danger');
            $this->redirect('staff/votingSecurityDashboard/' . $showId);
            return;
        }

        // Get judging model
        require_once APPROOT . '/models/JudgingModel.php';
        $judgingModel = new JudgingModel();

        // Record manual vote
        $result = $judgingModel->recordManualFanVote(
            $showId,
            $registrationId,
            $voterName,
            $voterContact,
            $_SESSION['user_id'],
            'staff',
            $notes
        );

        if ($result) {
            $this->setFlashMessage('staff_message', 'Manual vote added successfully.', 'success');
        } else {
            $this->setFlashMessage('staff_message', 'Failed to add manual vote. Please try again.', 'danger');
        }

        $this->redirect('staff/votingSecurityDashboard/' . $showId);
    }
}
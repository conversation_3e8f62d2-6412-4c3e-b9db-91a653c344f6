<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Judging & Metrics</h1>
            <p class="text-muted">Manage judging metrics, categories, and age weights</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
        </div>
    </div>

    <!-- Judging Settings Dashboard -->
    <div class="row g-4 mb-5">
        <!-- Scoring Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-danger bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-cog text-danger fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Scoring Settings</h4>
                    </div>
                    <p class="card-text text-muted">Configure scoring formulas, weights, and normalization settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/scoringSettings" class="stretched-link text-decoration-none">
                        <span class="d-none">Manage Scoring Settings</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Manage Scores Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-trophy text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Manage Scores</h4>
                    </div>
                    <p class="card-text text-muted">View and manage calculated scores, winners, and judging statistics.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/manageScores" class="stretched-link text-decoration-none">
                        <span class="d-none">View Scores</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Judging Metrics Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-balance-scale text-primary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Judging Metrics</h4>
                    </div>
                    <p class="card-text text-muted">Configure the metrics used for judging vehicles at your events.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/judgingMetrics" class="stretched-link text-decoration-none">
                        <span class="d-none">View Judging Metrics</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Default Metrics Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-sliders-h text-success fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Default Metrics</h4>
                    </div>
                    <p class="card-text text-muted">Manage the default metrics used for new shows and events.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/defaultMetrics" class="stretched-link text-decoration-none">
                        <span class="d-none">View Default Metrics</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Age Weights Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-calendar-alt text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Age Weights</h4>
                    </div>
                    <p class="card-text text-muted">Configure age-based scoring weights for vehicle judging.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/ageWeights" class="stretched-link text-decoration-none">
                        <span class="d-none">View Age Weights</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Voting Security Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-shield-alt text-success fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Voting Security</h4>
                    </div>
                    <p class="card-text text-muted">Configure fan voting security, fraud detection, and appeal settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/votingSecuritySettings" class="stretched-link text-decoration-none">
                        <span class="d-none">Manage Voting Security</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Vote Management Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-vote-yea text-primary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Vote Management</h4>
                    </div>
                    <p class="card-text text-muted">View, manage, and respond to all fan votes across all shows.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/voteManagement" class="stretched-link text-decoration-none">
                        <span class="d-none">Manage All Votes</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Default Age Weights Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-history text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Default Age Weights</h4>
                    </div>
                    <p class="card-text text-muted">Manage the default age weights used for new shows and events.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/defaultAgeWeights" class="stretched-link text-decoration-none">
                        <span class="d-none">View Default Age Weights</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Default Categories Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-danger bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-tags text-danger fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Default Categories</h4>
                    </div>
                    <p class="card-text text-muted">Manage the default vehicle categories used for new shows.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/defaultCategories" class="stretched-link text-decoration-none">
                        <span class="d-none">View Default Categories</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Fan Votes Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-secondary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-vote-yea text-secondary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Fan Votes</h4>
                    </div>
                    <p class="card-text text-muted">Configure and manage fan voting for your events.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/fanVotes" class="stretched-link text-decoration-none">
                        <span class="d-none">View Fan Votes</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Voting Security Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-danger bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-shield-alt text-danger fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Voting Security</h4>
                    </div>
                    <p class="card-text text-muted">Configure fraud prevention and security measures for fan voting.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/votingSecuritySettings" class="stretched-link text-decoration-none">
                        <span class="d-none">Voting Security Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Scoring Formulas Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-purple bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-calculator text-purple fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Scoring Formulas</h4>
                    </div>
                    <p class="card-text text-muted">Create and manage custom scoring formulas for vehicle judging.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/scoring/formulas" class="stretched-link text-decoration-none">
                        <span class="d-none">View Scoring Formulas</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
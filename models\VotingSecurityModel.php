<?php
/**
 * Voting Security Model
 * Handles all security-related functionality for fan voting
 */

class VotingSecurityModel {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get security settings for a show
     */
    public function getSecuritySettings($showId) {
        $this->db->query('SELECT * FROM voting_security_settings WHERE show_id = :show_id');
        $this->db->bind(':show_id', $showId);
        $settings = $this->db->single();
        
        // If no settings exist, create default ones
        if (!$settings) {
            $this->createDefaultSettings($showId);
            return $this->getSecuritySettings($showId);
        }
        
        return $settings;
    }
    
    /**
     * Create default security settings for a show
     */
    public function createDefaultSettings($showId) {
        $this->db->query('INSERT INTO voting_security_settings (show_id) VALUES (:show_id)');
        $this->db->bind(':show_id', $showId);
        return $this->db->execute();
    }
    
    /**
     * Update security settings for a show
     */
    public function updateSecuritySettings($showId, $settings) {
        $sql = 'UPDATE voting_security_settings SET ';
        $setParts = [];
        $params = [':show_id' => $showId];
        
        foreach ($settings as $key => $value) {
            $setParts[] = "$key = :$key";
            $params[":$key"] = $value;
        }
        
        $sql .= implode(', ', $setParts) . ', updated_at = NOW() WHERE show_id = :show_id';
        
        $this->db->query($sql);
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        
        return $this->db->execute();
    }
    
    /**
     * Calculate risk score for a vote
     */
    public function calculateRiskScore($voteData, $showId) {
        $riskScore = 0;
        $flags = [];
        $settings = $this->getSecuritySettings($showId);
        
        // Check GPS location if available
        if (isset($voteData['latitude']) && isset($voteData['longitude'])) {
            $distance = $this->calculateDistanceFromShow($voteData['latitude'], $voteData['longitude'], $showId);
            $voteData['distance_from_show'] = $distance;
            
            if ($settings->geofence_enabled && $distance > $settings->geofence_radius_miles) {
                $riskScore += 30;
                $flags[] = 'outside_geofence';
            }
        } else if ($settings->require_gps) {
            $riskScore += 20;
            $flags[] = 'no_gps_data';
        }
        
        // Check for VPN/Proxy usage
        if (isset($voteData['is_vpn']) && $voteData['is_vpn']) {
            $riskScore += 25;
            $flags[] = 'vpn_detected';
        }
        
        // Check velocity (rapid voting)
        if ($this->checkVelocityViolation($voteData['voter_ip'], $voteData['device_fingerprint'], $showId, $settings)) {
            $riskScore += 40;
            $flags[] = 'velocity_violation';
        }
        
        // Check for duplicate device fingerprints
        if ($this->checkDuplicateDevice($voteData['device_fingerprint'], $showId, $settings)) {
            $riskScore += 35;
            $flags[] = 'duplicate_device';
        }
        
        // Check for suspicious timing patterns
        if ($this->checkSuspiciousTiming($voteData, $showId)) {
            $riskScore += 20;
            $flags[] = 'suspicious_timing';
        }
        
        // Check for coordinated voting patterns
        if ($this->checkCoordinatedVoting($voteData, $showId)) {
            $riskScore += 30;
            $flags[] = 'coordinated_voting';
        }
        
        // Facebook verification checks
        if (isset($voteData['fb_user_id']) && $settings->social_verification_enabled) {
            $fbRisk = $this->checkFacebookProfile($voteData);
            $riskScore += $fbRisk['score'];
            $flags = array_merge($flags, $fbRisk['flags']);
        }
        
        return [
            'score' => min($riskScore, 100), // Cap at 100
            'flags' => $flags
        ];
    }
    
    /**
     * Calculate distance between voter location and show location
     */
    public function calculateDistanceFromShow($lat, $lon, $showId) {
        // Get show location
        $this->db->query('SELECT latitude, longitude FROM shows WHERE id = :show_id');
        $this->db->bind(':show_id', $showId);
        $show = $this->db->single();
        
        if (!$show || !$show->latitude || !$show->longitude) {
            return null; // Show location not set
        }
        
        // Calculate distance using Haversine formula
        $earthRadius = 3959; // miles
        
        $latDiff = deg2rad($show->latitude - $lat);
        $lonDiff = deg2rad($show->longitude - $lon);
        
        $a = sin($latDiff / 2) * sin($latDiff / 2) +
             cos(deg2rad($lat)) * cos(deg2rad($show->latitude)) *
             sin($lonDiff / 2) * sin($lonDiff / 2);
        
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        return $earthRadius * $c;
    }
    
    /**
     * Check for velocity violations (rapid voting)
     */
    public function checkVelocityViolation($ip, $deviceFingerprint, $showId, $settings) {
        if (!$settings->velocity_check_enabled) {
            return false;
        }
        
        $timeWindow = date('Y-m-d H:i:s', time() - $settings->velocity_window_seconds);
        
        // Check IP-based velocity
        $this->db->query('SELECT COUNT(*) as vote_count FROM fan_votes 
                         WHERE show_id = :show_id AND voter_ip = :ip 
                         AND created_at > :time_window');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':ip', $ip);
        $this->db->bind(':time_window', $timeWindow);
        $ipCount = $this->db->single()->vote_count;
        
        // Check device-based velocity
        $deviceCount = 0;
        if ($deviceFingerprint) {
            $this->db->query('SELECT COUNT(*) as vote_count FROM fan_votes 
                             WHERE show_id = :show_id AND device_fingerprint = :device 
                             AND created_at > :time_window');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':device', $deviceFingerprint);
            $this->db->bind(':time_window', $timeWindow);
            $deviceCount = $this->db->single()->vote_count;
        }
        
        return ($ipCount >= $settings->max_votes_in_window) || 
               ($deviceCount >= $settings->max_votes_in_window);
    }
    
    /**
     * Check for duplicate device usage
     */
    public function checkDuplicateDevice($deviceFingerprint, $showId, $settings) {
        if (!$deviceFingerprint) {
            return false;
        }
        
        $this->db->query('SELECT COUNT(*) as vote_count FROM fan_votes 
                         WHERE show_id = :show_id AND device_fingerprint = :device');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':device', $deviceFingerprint);
        $count = $this->db->single()->vote_count;
        
        return $count >= $settings->max_votes_per_device;
    }
    
    /**
     * Check for suspicious timing patterns
     */
    public function checkSuspiciousTiming($voteData, $showId) {
        // Check if vote is submitted too quickly after page load
        if (isset($voteData['vote_duration_seconds']) && $voteData['vote_duration_seconds'] < 5) {
            return true;
        }
        
        // Check for votes outside show hours
        $this->db->query('SELECT start_date, end_date FROM shows WHERE id = :show_id');
        $this->db->bind(':show_id', $showId);
        $show = $this->db->single();
        
        if ($show) {
            $now = time();
            $showStart = strtotime($show->start_date);
            $showEnd = strtotime($show->end_date);
            
            // Allow voting 2 hours before show starts and until 1 hour before show ends
            $votingStart = $showStart - (2 * 3600);
            $votingEnd = $showEnd - (1 * 3600);
            
            if ($now < $votingStart || $now > $votingEnd) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check for coordinated voting patterns
     */
    public function checkCoordinatedVoting($voteData, $showId) {
        // Check for multiple votes from same GPS coordinates
        if (isset($voteData['latitude']) && isset($voteData['longitude'])) {
            $this->db->query('SELECT COUNT(*) as vote_count FROM fan_votes 
                             WHERE show_id = :show_id 
                             AND ABS(latitude - :lat) < 0.0001 
                             AND ABS(longitude - :lon) < 0.0001
                             AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':lat', $voteData['latitude']);
            $this->db->bind(':lon', $voteData['longitude']);
            $coordCount = $this->db->single()->vote_count;
            
            if ($coordCount >= 3) { // 3 or more votes from same location in 1 hour
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check Facebook profile for verification
     */
    public function checkFacebookProfile($voteData) {
        $score = 0;
        $flags = [];
        
        // This would integrate with Facebook API to check profile details
        // For now, we'll implement basic checks based on available data
        
        if (empty($voteData['fb_user_name'])) {
            $score += 15;
            $flags[] = 'no_facebook_name';
        }
        
        if (empty($voteData['fb_user_email'])) {
            $score += 10;
            $flags[] = 'no_facebook_email';
        }
        
        // Check for duplicate Facebook votes
        $this->db->query('SELECT COUNT(*) as vote_count FROM fan_votes 
                         WHERE fb_user_id = :fb_user_id AND show_id = :show_id');
        $this->db->bind(':fb_user_id', $voteData['fb_user_id']);
        $this->db->bind(':show_id', $voteData['show_id']);
        $fbCount = $this->db->single()->vote_count;
        
        if ($fbCount > 0) {
            $score += 50;
            $flags[] = 'duplicate_facebook_vote';
        }
        
        return [
            'score' => $score,
            'flags' => $flags
        ];
    }

    /**
     * Record a voting session
     */
    public function recordVotingSession($sessionData) {
        $this->db->query('INSERT INTO voting_sessions
                         (session_id, show_id, ip_address, device_fingerprint, user_agent,
                          pages_viewed, vehicles_viewed, time_on_site_seconds)
                         VALUES (:session_id, :show_id, :ip_address, :device_fingerprint,
                                :user_agent, :pages_viewed, :vehicles_viewed, :time_on_site)
                         ON DUPLICATE KEY UPDATE
                         last_activity = NOW(),
                         pages_viewed = :pages_viewed,
                         vehicles_viewed = :vehicles_viewed,
                         time_on_site_seconds = :time_on_site');

        $this->db->bind(':session_id', $sessionData['session_id']);
        $this->db->bind(':show_id', $sessionData['show_id']);
        $this->db->bind(':ip_address', $sessionData['ip_address']);
        $this->db->bind(':device_fingerprint', $sessionData['device_fingerprint'] ?? null);
        $this->db->bind(':user_agent', $sessionData['user_agent'] ?? null);
        $this->db->bind(':pages_viewed', $sessionData['pages_viewed'] ?? 1);
        $this->db->bind(':vehicles_viewed', json_encode($sessionData['vehicles_viewed'] ?? []));
        $this->db->bind(':time_on_site', $sessionData['time_on_site_seconds'] ?? 0);

        return $this->db->execute();
    }

    /**
     * Create fraud detection alert and send notifications
     */
    public function createAlert($showId, $alertType, $severity, $title, $message, $alertData = null) {
        // Insert alert into database
        $this->db->query('INSERT INTO voting_alerts
                         (show_id, alert_type, severity, title, message, alert_data)
                         VALUES (:show_id, :alert_type, :severity, :title, :message, :alert_data)');

        $this->db->bind(':show_id', $showId);
        $this->db->bind(':alert_type', $alertType);
        $this->db->bind(':severity', $severity);
        $this->db->bind(':title', $title);
        $this->db->bind(':message', $message);
        $this->db->bind(':alert_data', $alertData ? json_encode($alertData) : null);

        $result = $this->db->execute();

        if ($result) {
            // Send notifications to admin and coordinator
            $this->sendSecurityNotifications($showId, $alertType, $severity, $title, $message, $alertData);
        }

        return $result;
    }

    /**
     * Send security notifications to admin and coordinator
     */
    private function sendSecurityNotifications($showId, $alertType, $severity, $title, $message, $alertData = null) {
        try {
            // Get show details
            $this->db->query('SELECT s.*, u.email as coordinator_email, u.phone as coordinator_phone
                             FROM shows s
                             LEFT JOIN users u ON s.coordinator_id = u.id
                             WHERE s.id = :show_id');
            $this->db->bind(':show_id', $showId);
            $show = $this->db->single();

            if (!$show) {
                return;
            }

            // Prepare notification data
            $notificationTitle = "🚨 Voting Security Alert: {$show->name}";
            $notificationMessage = "{$title}\n\n{$message}\n\nShow: {$show->name}\nTime: " . date('M j, Y g:i A');

            // Get admin users
            $this->db->query('SELECT id, email, phone FROM users WHERE role = "admin"');
            $admins = $this->db->resultSet();

            // Send notifications to admins
            foreach ($admins as $admin) {
                $this->queueSecurityNotification(
                    $admin->id,
                    $notificationTitle,
                    $notificationMessage,
                    $severity,
                    [
                        'show_id' => $showId,
                        'alert_type' => $alertType,
                        'url' => '/admin/votingSecuritySettings'
                    ]
                );
            }

            // Send notification to coordinator if different from admin
            if ($show->coordinator_id) {
                $coordinatorIsAdmin = false;
                foreach ($admins as $admin) {
                    if ($admin->id == $show->coordinator_id) {
                        $coordinatorIsAdmin = true;
                        break;
                    }
                }

                if (!$coordinatorIsAdmin) {
                    $this->queueSecurityNotification(
                        $show->coordinator_id,
                        $notificationTitle,
                        $notificationMessage,
                        $severity,
                        [
                            'show_id' => $showId,
                            'alert_type' => $alertType,
                            'url' => '/coordinator/votingSecurityDashboard/' . $showId
                        ]
                    );
                }
            }

        } catch (Exception $e) {
            error_log('Error sending security notifications: ' . $e->getMessage());
        }
    }

    /**
     * Queue security notification using existing notification system
     */
    private function queueSecurityNotification($userId, $title, $message, $severity, $data = []) {
        try {
            // Queue email notification directly to database
            $this->db->query('INSERT INTO notification_queue
                             (user_id, subscription_id, notification_type, event_id, event_type,
                              notification_category, scheduled_for, subject, message, status, created_at)
                             VALUES (:user_id, 0, :notification_type, 0, "security_alert",
                                     "security", NOW(), :subject, :message, "pending", NOW())');

            $this->db->bind(':user_id', $userId);
            $this->db->bind(':notification_type', 'email');
            $this->db->bind(':subject', $title);
            $this->db->bind(':message', $message);
            $this->db->execute();

            // Queue push notification for critical alerts
            if ($severity === 'critical' || $severity === 'warning') {
                $this->db->query('INSERT INTO notification_queue
                                 (user_id, subscription_id, notification_type, event_id, event_type,
                                  notification_category, scheduled_for, subject, message, status, created_at)
                                 VALUES (:user_id, 0, :notification_type, 0, "security_alert",
                                         "security", NOW(), :subject, :message, "pending", NOW())');

                $this->db->bind(':user_id', $userId);
                $this->db->bind(':notification_type', 'push');
                $this->db->bind(':subject', $title);
                $this->db->bind(':message', $message);
                $this->db->execute();
            }

            // Queue SMS for critical alerts only
            if ($severity === 'critical') {
                $this->db->query('INSERT INTO notification_queue
                                 (user_id, subscription_id, notification_type, event_id, event_type,
                                  notification_category, scheduled_for, subject, message, status, created_at)
                                 VALUES (:user_id, 0, :notification_type, 0, "security_alert",
                                         "security", NOW(), :subject, :message, "pending", NOW())');

                $this->db->bind(':user_id', $userId);
                $this->db->bind(':notification_type', 'sms');
                $this->db->bind(':subject', $title);
                $this->db->bind(':message', $message);
                $this->db->execute();
            }

        } catch (Exception $e) {
            error_log('Error queueing security notification: ' . $e->getMessage());
        }
    }

    /**
     * Get alerts for a show
     */
    public function getAlertsForShow($showId, $unacknowledgedOnly = false) {
        $sql = 'SELECT * FROM voting_alerts WHERE show_id = :show_id';
        if ($unacknowledgedOnly) {
            $sql .= ' AND acknowledged = FALSE';
        }
        $sql .= ' ORDER BY created_at DESC';

        $this->db->query($sql);
        $this->db->bind(':show_id', $showId);
        return $this->db->resultSet();
    }

    /**
     * Acknowledge an alert
     */
    public function acknowledgeAlert($alertId, $userId) {
        $this->db->query('UPDATE voting_alerts
                         SET acknowledged = TRUE, acknowledged_by = :user_id, acknowledged_at = NOW()
                         WHERE id = :alert_id');
        $this->db->bind(':alert_id', $alertId);
        $this->db->bind(':user_id', $userId);
        return $this->db->execute();
    }

    /**
     * Record honeypot interaction
     */
    public function recordHoneypotInteraction($showId, $honeypotId, $ipAddress, $deviceFingerprint = null, $userAgent = null, $sessionId = null) {
        $this->db->query('INSERT INTO honeypot_votes
                         (show_id, honeypot_identifier, ip_address, device_fingerprint, user_agent, session_id)
                         VALUES (:show_id, :honeypot_id, :ip_address, :device_fingerprint, :user_agent, :session_id)');

        $this->db->bind(':show_id', $showId);
        $this->db->bind(':honeypot_id', $honeypotId);
        $this->db->bind(':ip_address', $ipAddress);
        $this->db->bind(':device_fingerprint', $deviceFingerprint);
        $this->db->bind(':user_agent', $userAgent);
        $this->db->bind(':session_id', $sessionId);

        return $this->db->execute();
    }

    /**
     * Check if IP/device has triggered honeypot
     */
    public function hasTriggeredHoneypot($showId, $ipAddress, $deviceFingerprint = null) {
        $sql = 'SELECT COUNT(*) as count FROM honeypot_votes
                WHERE show_id = :show_id AND ip_address = :ip_address';
        $params = [':show_id' => $showId, ':ip_address' => $ipAddress];

        if ($deviceFingerprint) {
            $sql .= ' OR device_fingerprint = :device_fingerprint';
            $params[':device_fingerprint'] = $deviceFingerprint;
        }

        $this->db->query($sql);
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }

        return $this->db->single()->count > 0;
    }

    /**
     * Get voting statistics for a show
     */
    public function getVotingStats($showId) {
        $stats = [];

        // Total votes
        $this->db->query('SELECT COUNT(*) as total FROM fan_votes WHERE show_id = :show_id');
        $this->db->bind(':show_id', $showId);
        $stats['total_votes'] = $this->db->single()->total;

        // Flagged votes
        $this->db->query('SELECT COUNT(*) as flagged FROM fan_votes WHERE show_id = :show_id AND is_flagged = TRUE');
        $this->db->bind(':show_id', $showId);
        $stats['flagged_votes'] = $this->db->single()->flagged;

        // Approved votes
        $this->db->query('SELECT COUNT(*) as approved FROM fan_votes WHERE show_id = :show_id AND is_approved = TRUE');
        $this->db->bind(':show_id', $showId);
        $stats['approved_votes'] = $this->db->single()->approved;

        // High risk votes
        $this->db->query('SELECT COUNT(*) as high_risk FROM fan_votes WHERE show_id = :show_id AND risk_score >= 70');
        $this->db->bind(':show_id', $showId);
        $stats['high_risk_votes'] = $this->db->single()->high_risk;

        // Vote methods breakdown
        $this->db->query('SELECT vote_method, COUNT(*) as count FROM fan_votes
                         WHERE show_id = :show_id GROUP BY vote_method');
        $this->db->bind(':show_id', $showId);
        $methods = $this->db->resultSet();
        $stats['vote_methods'] = [];
        foreach ($methods as $method) {
            $stats['vote_methods'][$method->vote_method] = $method->count;
        }

        return $stats;
    }

    /**
     * Get IP geolocation data
     */
    public function getIpGeolocation($ipAddress) {
        try {
            // Use the IP geolocation service
            require_once APPROOT . '/helpers/IpGeolocationService.php';
            $geoService = new IpGeolocationService();
            return $geoService->getLocation($ipAddress);
        } catch (Exception $e) {
            error_log('IP Geolocation error: ' . $e->getMessage());
            // Return default data on error
            return [
                'country' => 'US',
                'region' => 'Unknown',
                'city' => 'Unknown',
                'latitude' => 0,
                'longitude' => 0,
                'is_vpn' => false,
                'accuracy' => 'unknown'
            ];
        }
    }

    /**
     * Generate device fingerprint from browser data
     */
    public function generateDeviceFingerprint($browserData) {
        $components = [
            $browserData['user_agent'] ?? '',
            $browserData['screen_resolution'] ?? '',
            $browserData['timezone'] ?? '',
            $browserData['language'] ?? '',
            $browserData['platform'] ?? '',
            $browserData['plugins'] ?? '',
            $browserData['canvas_fingerprint'] ?? ''
        ];

        return hash('sha256', implode('|', $components));
    }

    /**
     * Get system-wide voting statistics
     */
    public function getSystemVotingStats($showId = null) {
        try {
            $whereClause = $showId ? 'WHERE show_id = :show_id' : '';

            // Total votes
            $this->db->query("SELECT COUNT(*) as total FROM fan_votes {$whereClause}");
            if ($showId) $this->db->bind(':show_id', $showId);
            $totalVotes = $this->db->single()->total ?? 0;

            // Flagged votes
            $this->db->query("SELECT COUNT(*) as flagged FROM fan_votes {$whereClause} " .
                           ($whereClause ? 'AND' : 'WHERE') . " is_flagged = TRUE");
            if ($showId) $this->db->bind(':show_id', $showId);
            $flaggedVotes = $this->db->single()->flagged ?? 0;

            // High risk votes (score >= 70)
            $this->db->query("SELECT COUNT(*) as high_risk FROM fan_votes {$whereClause} " .
                           ($whereClause ? 'AND' : 'WHERE') . " risk_score >= 70");
            if ($showId) $this->db->bind(':show_id', $showId);
            $highRiskVotes = $this->db->single()->high_risk ?? 0;

            // Pending appeals
            $this->db->query("SELECT COUNT(*) as pending FROM vote_appeals {$whereClause} " .
                           ($whereClause ? 'AND' : 'WHERE') . " status = 'pending'");
            if ($showId) $this->db->bind(':show_id', $showId);
            $pendingAppeals = $this->db->single()->pending ?? 0;

            return [
                'total_votes' => $totalVotes,
                'flagged_votes' => $flaggedVotes,
                'high_risk_votes' => $highRiskVotes,
                'pending_appeals' => $pendingAppeals
            ];

        } catch (Exception $e) {
            error_log('Error getting system voting stats: ' . $e->getMessage());
            return [
                'total_votes' => 0,
                'flagged_votes' => 0,
                'high_risk_votes' => 0,
                'pending_appeals' => 0
            ];
        }
    }

    /**
     * Get recent votes with security analysis
     */
    public function getRecentVotesWithSecurity($showId = null, $limit = 50) {
        try {
            $whereClause = $showId ? 'WHERE fv.show_id = :show_id' : '';

            $this->db->query("SELECT fv.*, s.name as show_name,
                             CONCAT(v.year, ' ', v.make, ' ', v.model) as vehicle_info
                             FROM fan_votes fv
                             LEFT JOIN shows s ON fv.show_id = s.id
                             LEFT JOIN registrations r ON fv.registration_id = r.id
                             LEFT JOIN vehicles v ON r.vehicle_id = v.id
                             {$whereClause}
                             ORDER BY fv.created_at DESC
                             LIMIT :limit");

            if ($showId) $this->db->bind(':show_id', $showId);
            $this->db->bind(':limit', $limit);

            return $this->db->resultSet();

        } catch (Exception $e) {
            error_log('Error getting recent votes: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get flagged votes requiring review
     */
    public function getFlaggedVotes($showId = null) {
        try {
            $whereClause = $showId ? 'WHERE fv.show_id = :show_id AND' : 'WHERE';

            $this->db->query("SELECT fv.*, s.name as show_name,
                             CONCAT(v.year, ' ', v.make, ' ', v.model) as vehicle_info
                             FROM fan_votes fv
                             LEFT JOIN shows s ON fv.show_id = s.id
                             LEFT JOIN registrations r ON fv.registration_id = r.id
                             LEFT JOIN vehicles v ON r.vehicle_id = v.id
                             {$whereClause} fv.is_flagged = TRUE
                             ORDER BY fv.created_at DESC");

            if ($showId) $this->db->bind(':show_id', $showId);

            return $this->db->resultSet();

        } catch (Exception $e) {
            error_log('Error getting flagged votes: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get recent alerts
     */
    public function getRecentAlerts($showId = null, $limit = 20) {
        try {
            $whereClause = $showId ? 'WHERE va.show_id = :show_id' : '';

            $this->db->query("SELECT va.*, s.name as show_name
                             FROM voting_alerts va
                             LEFT JOIN shows s ON va.show_id = s.id
                             {$whereClause}
                             ORDER BY va.created_at DESC
                             LIMIT :limit");

            if ($showId) $this->db->bind(':show_id', $showId);
            $this->db->bind(':limit', $limit);

            return $this->db->resultSet();

        } catch (Exception $e) {
            error_log('Error getting recent alerts: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get pending appeals
     */
    public function getPendingAppeals($showId = null) {
        try {
            $whereClause = $showId ? 'WHERE va.show_id = :show_id AND' : 'WHERE';

            $this->db->query("SELECT va.*, s.name as show_name
                             FROM vote_appeals va
                             LEFT JOIN shows s ON va.show_id = s.id
                             {$whereClause} va.status = 'pending'
                             ORDER BY va.created_at ASC");

            if ($showId) $this->db->bind(':show_id', $showId);

            return $this->db->resultSet();

        } catch (Exception $e) {
            error_log('Error getting pending appeals: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Bulk approve votes
     */
    public function bulkApproveVotes($voteIds, $userId) {
        try {
            if (empty($voteIds)) return false;

            $placeholders = str_repeat('?,', count($voteIds) - 1) . '?';
            $this->db->query("UPDATE fan_votes
                             SET is_approved = TRUE, is_flagged = FALSE,
                                 reviewed_by = ?, reviewed_at = NOW(),
                                 review_notes = 'Bulk approved by admin'
                             WHERE id IN ({$placeholders})");

            $this->db->bind(1, $userId);
            foreach ($voteIds as $index => $voteId) {
                $this->db->bind($index + 2, $voteId);
            }

            return $this->db->execute();

        } catch (Exception $e) {
            error_log('Error bulk approving votes: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Bulk flag votes
     */
    public function bulkFlagVotes($voteIds, $userId) {
        try {
            if (empty($voteIds)) return false;

            $placeholders = str_repeat('?,', count($voteIds) - 1) . '?';
            $this->db->query("UPDATE fan_votes
                             SET is_flagged = TRUE,
                                 reviewed_by = ?, reviewed_at = NOW(),
                                 review_notes = 'Bulk flagged by admin'
                             WHERE id IN ({$placeholders})");

            $this->db->bind(1, $userId);
            foreach ($voteIds as $index => $voteId) {
                $this->db->bind($index + 2, $voteId);
            }

            return $this->db->execute();

        } catch (Exception $e) {
            error_log('Error bulk flagging votes: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Bulk delete votes
     */
    public function bulkDeleteVotes($voteIds, $userId) {
        try {
            if (empty($voteIds)) return false;

            // Log the deletion for audit trail
            foreach ($voteIds as $voteId) {
                $this->db->query("INSERT INTO system_logs (log_type, message, user_id, created_at)
                                 VALUES ('vote_deletion', 'Vote ID {$voteId} deleted by admin', ?, NOW())");
                $this->db->bind(1, $userId);
                $this->db->execute();
            }

            // Delete the votes
            $placeholders = str_repeat('?,', count($voteIds) - 1) . '?';
            $this->db->query("DELETE FROM fan_votes WHERE id IN ({$placeholders})");

            foreach ($voteIds as $index => $voteId) {
                $this->db->bind($index + 1, $voteId);
            }

            return $this->db->execute();

        } catch (Exception $e) {
            error_log('Error bulk deleting votes: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Approve vote appeal
     */
    public function approveVoteAppeal($appealId, $userId, $adminResponse = '') {
        try {
            // Get appeal details
            $this->db->query("SELECT * FROM vote_appeals WHERE id = ?");
            $this->db->bind(1, $appealId);
            $appeal = $this->db->single();

            if (!$appeal) return false;

            // Update appeal status
            $this->db->query("UPDATE vote_appeals
                             SET status = 'approved', reviewed_by = ?, reviewed_at = NOW(),
                                 admin_response = ?
                             WHERE id = ?");
            $this->db->bind(1, $userId);
            $this->db->bind(2, $adminResponse);
            $this->db->bind(3, $appealId);

            if (!$this->db->execute()) return false;

            // Add the vote manually
            $this->db->query("INSERT INTO fan_votes
                             (show_id, registration_id, voter_ip, vote_method, manual_voter_name,
                              manual_voter_contact, manual_entered_by, is_approved, risk_score,
                              review_notes, created_at)
                             VALUES (?, ?, '0.0.0.0', 'manual_admin', ?, ?, ?, TRUE, 0,
                                     'Vote added via approved appeal', NOW())");

            $this->db->bind(1, $appeal->show_id);
            $this->db->bind(2, $appeal->registration_id);
            $this->db->bind(3, $appeal->voter_name);
            $this->db->bind(4, $appeal->voter_email);
            $this->db->bind(5, $userId);

            return $this->db->execute();

        } catch (Exception $e) {
            error_log('Error approving vote appeal: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Deny vote appeal
     */
    public function denyVoteAppeal($appealId, $userId, $adminResponse = '') {
        try {
            $this->db->query("UPDATE vote_appeals
                             SET status = 'denied', reviewed_by = ?, reviewed_at = NOW(),
                                 admin_response = ?
                             WHERE id = ?");
            $this->db->bind(1, $userId);
            $this->db->bind(2, $adminResponse);
            $this->db->bind(3, $appealId);

            return $this->db->execute();

        } catch (Exception $e) {
            error_log('Error denying vote appeal: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get paginated votes with advanced filtering - optimized for thousands of votes
     */
    public function getPaginatedVotes($page = 1, $limit = 25, $filters = []) {
        try {
            $offset = ($page - 1) * $limit;
            $whereConditions = [];
            $params = [];
            $paramIndex = 1;

            // Build WHERE conditions based on filters
            if (!empty($filters['show_id'])) {
                $whereConditions[] = "fv.show_id = ?";
                $params[$paramIndex++] = $filters['show_id'];
            }

            if (!empty($filters['search'])) {
                $whereConditions[] = "(s.name LIKE ? OR CONCAT(v.year, ' ', v.make, ' ', v.model) LIKE ? OR fv.voter_ip LIKE ? OR fv.fb_user_name LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[$paramIndex++] = $searchTerm;
                $params[$paramIndex++] = $searchTerm;
                $params[$paramIndex++] = $searchTerm;
                $params[$paramIndex++] = $searchTerm;
            }

            if (!empty($filters['status']) && $filters['status'] !== 'all') {
                switch ($filters['status']) {
                    case 'flagged':
                        $whereConditions[] = "fv.is_flagged = TRUE";
                        break;
                    case 'approved':
                        $whereConditions[] = "fv.is_approved = TRUE AND fv.is_flagged = FALSE";
                        break;
                    case 'pending':
                        $whereConditions[] = "fv.is_approved = FALSE AND fv.is_flagged = FALSE";
                        break;
                }
            }

            if (!empty($filters['risk_level']) && $filters['risk_level'] !== 'all') {
                switch ($filters['risk_level']) {
                    case 'high':
                        $whereConditions[] = "fv.risk_score >= 70";
                        break;
                    case 'medium':
                        $whereConditions[] = "fv.risk_score >= 40 AND fv.risk_score < 70";
                        break;
                    case 'low':
                        $whereConditions[] = "fv.risk_score < 40";
                        break;
                }
            }

            if (!empty($filters['date_from'])) {
                $whereConditions[] = "fv.created_at >= ?";
                $params[$paramIndex++] = $filters['date_from'] . ' 00:00:00';
            }

            if (!empty($filters['date_to'])) {
                $whereConditions[] = "fv.created_at <= ?";
                $params[$paramIndex++] = $filters['date_to'] . ' 23:59:59';
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // Get total count for pagination
            $countQuery = "SELECT COUNT(*) as total
                          FROM fan_votes fv
                          LEFT JOIN shows s ON fv.show_id = s.id
                          LEFT JOIN registrations r ON fv.registration_id = r.id
                          LEFT JOIN vehicles v ON r.vehicle_id = v.id
                          {$whereClause}";

            $this->db->query($countQuery);
            foreach ($params as $index => $value) {
                $this->db->bind($index, $value);
            }
            $total = $this->db->single()->total;

            // Get paginated results
            $dataQuery = "SELECT fv.*, s.name as show_name,
                         CONCAT(v.year, ' ', v.make, ' ', v.model) as vehicle_info,
                         u.name as entered_by_name
                         FROM fan_votes fv
                         LEFT JOIN shows s ON fv.show_id = s.id
                         LEFT JOIN registrations r ON fv.registration_id = r.id
                         LEFT JOIN vehicles v ON r.vehicle_id = v.id
                         LEFT JOIN users u ON fv.manual_entered_by = u.id
                         {$whereClause}
                         ORDER BY fv.created_at DESC
                         LIMIT ? OFFSET ?";

            $this->db->query($dataQuery);
            foreach ($params as $index => $value) {
                $this->db->bind($index, $value);
            }
            $this->db->bind($paramIndex++, $limit);
            $this->db->bind($paramIndex, $offset);

            $votes = $this->db->resultSet();

            return [
                'votes' => $votes,
                'total' => $total
            ];

        } catch (Exception $e) {
            error_log('Error getting paginated votes: ' . $e->getMessage());
            return [
                'votes' => [],
                'total' => 0
            ];
        }
    }

    /**
     * Get flagged votes count - optimized
     */
    public function getFlaggedVotesCount($showId = null) {
        try {
            $whereClause = $showId ? 'WHERE show_id = ? AND is_flagged = TRUE' : 'WHERE is_flagged = TRUE';

            $this->db->query("SELECT COUNT(*) as count FROM fan_votes {$whereClause}");
            if ($showId) $this->db->bind(1, $showId);

            return $this->db->single()->count ?? 0;

        } catch (Exception $e) {
            error_log('Error getting flagged votes count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get pending appeals count - optimized
     */
    public function getPendingAppealsCount($showId = null) {
        try {
            $whereClause = $showId ? 'WHERE show_id = ? AND status = "pending"' : 'WHERE status = "pending"';

            $this->db->query("SELECT COUNT(*) as count FROM vote_appeals {$whereClause}");
            if ($showId) $this->db->bind(1, $showId);

            return $this->db->single()->count ?? 0;

        } catch (Exception $e) {
            error_log('Error getting pending appeals count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get critical alerts only - optimized
     */
    public function getCriticalAlerts($showId = null, $limit = 5) {
        try {
            $whereClause = $showId ? 'WHERE va.show_id = ? AND va.severity IN ("critical", "error")' : 'WHERE va.severity IN ("critical", "error")';

            $this->db->query("SELECT va.*, s.name as show_name
                             FROM voting_alerts va
                             LEFT JOIN shows s ON va.show_id = s.id
                             {$whereClause}
                             ORDER BY va.created_at DESC
                             LIMIT ?");

            $paramIndex = 1;
            if ($showId) $this->db->bind($paramIndex++, $showId);
            $this->db->bind($paramIndex, $limit);

            return $this->db->resultSet();

        } catch (Exception $e) {
            error_log('Error getting critical alerts: ' . $e->getMessage());
            return [];
        }
    }
}
